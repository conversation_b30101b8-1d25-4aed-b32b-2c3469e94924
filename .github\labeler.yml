# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

"Changes: Sprites":
- changed-files:
  - any-glob-to-any-file: '**/*.rsi/*.png'

"Changes: Map":
- changed-files:
  - any-glob-to-any-file:
    - 'Resources/Maps/**/*.yml'
    - 'Resources/Prototypes/Maps/**/*.yml'

"Changes: UI":
- changed-files:
  - any-glob-to-any-file: '**/*.xaml*'

"Changes: Shaders":
- changed-files:
  - any-glob-to-any-file: '**/*.swsl'

"Changes: Audio":
- changed-files:
  - any-glob-to-any-file: '**/*.ogg'

"Changes: No C#":
- changed-files:
  # Equiv to any-glob-to-all as long as this has one matcher. If ALL changed files are not C# files, then apply label.
  - all-globs-to-all-files: "!**/*.cs"