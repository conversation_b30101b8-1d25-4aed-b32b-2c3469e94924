# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: Build & Test Debug

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches: [ master, staging, stable ]
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]
    branches: [ master, staging, stable ]

jobs:
  build:
    if: github.event.pull_request.draft == false
    strategy:
      matrix:
        os: [ubuntu-latest]

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout Master
      uses: actions/checkout@v4.2.2

    - name: Setup Submodule
      run: |
        git submodule update --init --recursive

    - name: Pull engine updates
      uses: space-wizards/submodule-dependency@v0.1.5

    - name: Update Engine Submodules
      run: |
        cd RobustToolbox/
        git submodule update --init --recursive

    - name: Setup .NET Core
      uses: actions/setup-dotnet@v4.1.0
      with:
        dotnet-version: 9.0.x

    - name: Install dependencies
      run: dotnet restore

    - name: Build Project
      run: dotnet build --configuration DebugOpt --no-restore /p:WarningsAsErrors=nullable /m

    - name: Run Content.Tests
      run: dotnet test --no-build --configuration DebugOpt Content.Tests/Content.Tests.csproj -- NUnit.ConsoleOut=0

    - name: Run Content.IntegrationTests
      shell: pwsh
      run: |
        $env:DOTNET_gcServer=1
        dotnet test --no-build --configuration DebugOpt Content.IntegrationTests/Content.IntegrationTests.csproj -- NUnit.ConsoleOut=0 NUnit.MapWarningTo=Failed
  ci-success:
    name: Build & Test Debug
    needs:
      - build
    runs-on: ubuntu-latest
    steps:
      - name: CI succeeded
        run: exit 0
