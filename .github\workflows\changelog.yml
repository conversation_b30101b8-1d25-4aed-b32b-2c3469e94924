# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
# From https://github.com/DeltaV-Station/Delta-v/
name: PR Changelogs
concurrency: commit_action
on:
  pull_request_target:
    types: [closed]

env:
  GITHUB_TOKEN: ${{ secrets.BOT_TOKEN }}
  CHANGELOG_DIR: ${{ vars.CHANGELOG_DIR }}
  PR_NUMBER: ${{ github.event.number }}

jobs:
  changelog:
    runs-on: self-hosted
    if: github.event.pull_request.merged == true
    permissions:
      contents: write
    steps:
      - name: Checkout Master
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.BOT_TOKEN }}
          ref: "${{ vars.CHANGELOG_BRANCH }}"

      - name: Setup Git
        run: |
          git config --global user.name "${{ vars.CHANGELOG_USER }}"
          git config --global user.email "${{ vars.CHANGELOG_EMAIL }}"
        shell: bash

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18.x

      - name: Install Dependencies
        run: |
          cd "Tools/changelog"
          npm install
        shell: bash
        continue-on-error: true

      - name: Generate Changelog
        run: |
          cd "Tools/changelog"
          node changelog.js
        shell: bash
        continue-on-error: true

      - name: Commit Changelog
        run: |
          git add *.yml
          git commit -m "${{ vars.CHANGELOG_MESSAGE }} (#${{ env.PR_NUMBER }})"
          git push
        shell: bash
        continue-on-error: true