# SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: MIT

name: CRLF Check

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

jobs:
  build:
    name: CRLF Check
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4.2.2
    - name: Check for CRLF
      run: Tools/check_crlf.py
