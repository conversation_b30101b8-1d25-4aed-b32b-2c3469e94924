# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: "Labels: Approved"

on:
  pull_request_review:
    types: [submitted]

jobs:
  add_label:
    # Change the repository name after you've made sure the team name is correct for your fork!
    if: ${{ (github.repository == 'Goob-Station/Goob-Station') && (github.event.review.state == 'APPROVED') }}
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
    - uses: tspascoal/get-user-teams-membership@v3
      id: checkUserMember
      with:
        username: ${{ github.actor }}
        team: "maintainers"
        GITHUB_TOKEN: ${{ secrets.BOT_TOKEN }}
    - if: ${{ steps.checkUserMember.outputs.isTeamMember == 'true' }}
      uses: actions-ecosystem/action-add-labels@v1
      with:
        labels: "S: Approved"
