# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: "Labels: Size"
on: pull_request_target

jobs:
  size-label:
    runs-on: ubuntu-latest
    steps:
      - name: size-label
        uses: "pascalgn/size-label-action@v0.5.5"
        env:
          GITHUB_TOKEN: "${{ secrets.BOT_TOKEN }}"
        with:
          # Custom size configuration
          sizes: >
            {
              "0": "XS",
              "10": "S",
              "100": "M",
              "1000": "L",
              "5000": "XL"
            }
