# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: MIT

name: No submodule update checker

on:
  pull_request:
    paths:
      - 'RobustToolbox'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  this_aint_right:
    name: Submodule update in pr found
    runs-on: ubuntu-latest
    steps:
      - name: Fail
        run: exit 1
