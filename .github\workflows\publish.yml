# SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 Pieter-<PERSON> <<EMAIL>>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: Publish

on:
  workflow_dispatch:
  schedule:
    - cron: '0 10 * * *'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: self-hosted

    steps:
    - name: Install dependencies
      run: sudo apt-get install -y python3-paramiko python3-lxml

    - name: Check .NET Installation
      id: check-dotnet
      run: |
        if dotnet --info > /dev/null 2>&1; then
          echo "skip=true" >> $GITHUB_ENV
        else
          echo "skip=false" >> $GITHUB_ENV
        fi

    - uses: actions/checkout@v3.6.0
      with:
        submodules: 'recursive'
    - name: Setup .NET Core
      if: env.skip == 'false'
      uses: actions/setup-dotnet@v3.2.0
      with:
        dotnet-version: 9.0.x

    - name: Get Engine Tag
      run: |
        cd RobustToolbox
        git fetch --depth=1


    - name: Clean
      run: dotnet clean

    - name: Install dependencies
      run: dotnet restore

    - name: Build Packaging
      run: dotnet build Content.Packaging --configuration Release --no-restore /m

    - name: Package server
      run: dotnet run --project Content.Packaging server --platform win-x64 --platform linux-x64 --platform osx-x64 --platform linux-arm64

    - name: Package client
      run: dotnet run --project Content.Packaging client --no-wipe-release

    - name: Publish version
      run: Tools/publish_multi_request.py
      env:
        PUBLISH_TOKEN: ${{ secrets.PUBLISH_TOKEN }}
        GITHUB_REPOSITORY: ${{ vars.GITHUB_REPOSITORY }}

    - name: Publish changelog (Discord)
      run: Tools/actions_changelogs_since_last_run.py
      env:
        GITHUB_TOKEN: ${{ secrets.BOT_TOKEN }}
        DISCORD_WEBHOOK_URL: ${{ secrets.CHANGELOG_DISCORD_WEBHOOK }}

    - name: Publish changelog (RSS)
      run: Tools/actions_changelog_rss.py
      env:
        CHANGELOG_RSS_KEY: ${{ secrets.CHANGELOG_RSS_KEY }}
