      
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: Update REUSE Headers

on:
  pull_request_target:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review

jobs:
  update_headers:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.head_ref }}
          fetch-depth: 0
          token: ${{ secrets.BOT_TOKEN }}

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Get Changed Files and PR Info
        id: changed_files
        env:
           PR_BODY: ${{ github.event.pull_request.body }}
           GH_TOKEN: ${{ secrets.BOT_TOKEN }}
           GH_REPO: ${{ github.repository }}
           PR_NUMBER: ${{ github.event.pull_request.number }}
        run: |
          set -e # Exit script on first error

          echo "Fetching files for PR #${PR_NUMBER} in repo ${GH_REPO}"

          PR_FILES_JSON=$(curl -s -f -H "Authorization: token ${GH_TOKEN}" \
            "https://api.github.com/repos/${GH_REPO}/pulls/${PR_NUMBER}/files")

          if [ $? -ne 0 ]; then
            echo "Error fetching PR files from GitHub API."
            exit 1
          fi
          
          if ! echo "$PR_FILES_JSON" | jq -e . > /dev/null; then
             echo "Warning: Received empty or invalid JSON from GitHub API for PR files."
             PR_FILES="" # Set PR_FILES to empty to avoid errors below
          else
             PR_FILES=$(echo "$PR_FILES_JSON" | jq -r '.[] | select(.filename != null) | "\(.status) \(.filename)"')
          fi

          if [ -z "$PR_FILES" ]; then
            echo "No files found in PR."
            ADDED_FILES=""
            MODIFIED_FILES=""
          else
            ADDED_FILES=$(echo "$PR_FILES" | grep "^added" | grep -E '\.(cs|ya?ml)$' | sed 's/^added //' | xargs) || true
            MODIFIED_FILES=$(echo "$PR_FILES" | grep "^modified" | grep -E '\.(cs|ya?ml)$' | sed 's/^modified //' | xargs) || true
          fi

          echo "Added Files: $ADDED_FILES"
          echo "Modified Files: $MODIFIED_FILES"

          echo "ADDED_FILES_LIST<<EOF" >> $GITHUB_ENV
          echo "$ADDED_FILES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

          echo "MODIFIED_FILES_LIST<<EOF" >> $GITHUB_ENV
          echo "$MODIFIED_FILES" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

          echo "Checking PR body for license..."
          if grep -q "LICENSE: MIT" <<< "$PR_BODY"; then
            echo "PR_LICENSE=mit" >> $GITHUB_ENV
            echo "License specified in PR: MIT"
          elif grep -q "LICENSE: AGPL" <<< "$PR_BODY"; then
            echo "PR_LICENSE=agpl" >> $GITHUB_ENV
            echo "License specified in PR: AGPL"
          elif grep -q "LICENSE: MPL" <<< "$PR_BODY"; then
            echo "PR_LICENSE=mpl" >> $GITHUB_ENV
            echo "License specified in PR: MPL"
          else
            echo "PR_LICENSE=agpl" >> $GITHUB_ENV
            echo "No license specified in PR, using default: AGPL"
          fi

      - name: Run REUSE Header Update Script
        run: |
          python Tools/update_pr_reuse_headers.py \
            --files-added ${{ env.ADDED_FILES_LIST }} \
            --files-modified ${{ env.MODIFIED_FILES_LIST }} \
            --pr-license ${{ env.PR_LICENSE }} \
            --pr-base-sha ${{ github.event.pull_request.base.sha }} \
            --pr-head-sha ${{ github.event.pull_request.head.sha }}
        working-directory: ${{ github.workspace }}

      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: "chore: Automatically update REUSE headers"
          commit_user_name: ${{ vars.CHANGELOG_USER }}
          commit_user_email: ${{ vars.CHANGELOG_EMAIL }}
          commit_author: ${{ vars.CHANGELOG_USER }} <${{ vars.CHANGELOG_EMAIL }}>
          skip_dirty_check: false
          skip_fetch: true

    
