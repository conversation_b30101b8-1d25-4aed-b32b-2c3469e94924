name: <PERSON> and <PERSON> Stale Pull Requests

on:
  workflow_dispatch:
  schedule:
    # Runs daily at midnight UTC.
    - cron: '0 0 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      # Currently we are not planning on marking issues as stale, so this permission is read only.
      # Just gonna leave this in here in case we need it in the future.
      issues: read
      pull-requests: write # Required to add labels, comment, close PRs.

    steps:
      - name: <PERSON> and <PERSON> Stale Pull Requests
        uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

          # --- Ignore Issues ---
          # Set to -1 so we never mark issues as stale.
          days-before-issue-stale: -1

          # --- Pull Request Specific Settings ---
          # Number of days of inactivity before a Pull Request becomes stale (currently 2 weeks)
          days-before-pr-stale: 14

          # Number of days of inactivity after being marked stale before a Pull Request is closed (2 more weeks)
          days-before-pr-close: 14

          # Label to use when marking a PR as stale
          stale-pr-label: 'Note: Stale'

          # Comment to post when marking a PR as stale
          stale-pr-message: |
            This pull request has been automatically marked as stale because it has not had
            any activity for 14 days. It will be closed in another 14 days if no further activity occurs.
            Thank you for your contributions.

            You can always ask for help on our development [discord](https://discord.gg/RYNBE6fUW2/).

            If you'd like to keep this open, just leave any comment, and the stale label will be removed.
          # Comment to post when closing a stale PR
          close-pr-message: |
            This pull request has been automatically closed because there has been no more activity in the 14 days
            since being marked stale.

            Please feel free to reopen or open a new pull request if you'd still like this to be addressed.

            Again, you can always ask for help on our development [discord](https://discord.gg/RYNBE6fUW2).

            Thanks again for your contribution!
          # Pull Requests with these labels will never be considered stale

          # Set to true to ignore PRs in a milestone (defaults to false)
          exempt-all-pr-milestones: true
          
          # Remove stale label from PRs on update (default is true)
          remove-pr-stale-when-updated: true
          
          ascending: true
