# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: RSI Validator

on:
  push:
    branches: [ master, staging, stable ]
  merge_group:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  validate_rsis:
    name: Validate RSIs
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2
      - name: Setup Submodule
        run: git submodule update --init
      - name: Pull engine updates
        uses: space-wizards/submodule-dependency@v0.1.5
      - name: Install Python dependencies
        run: |
          pip3 install --ignore-installed --user pillow jsonschema
      - name: Validate RSIs
        run: |
          python3 RobustToolbox/Schemas/validate_rsis.py Resources/
