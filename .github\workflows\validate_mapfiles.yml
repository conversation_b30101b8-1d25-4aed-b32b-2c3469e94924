# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2023 <PERSON>-<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: Map file schema validator
on:
  push:
    branches: [ master, staging, stable ]
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  yaml-schema-validation:
    name: YAML map schema validator
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4.2.2
    - name: Setup Submodule
      run: git submodule update --init
    - name: Pull engine updates
      uses: space-wizards/submodule-dependency@v0.1.5
    - uses: PaulRitter/yaml-schema-validator@v1
      with:
        schema: RobustToolbox/Schemas/mapfile.yml
        path_pattern: .*Resources/Maps/.*
        validators_path: RobustToolbox/Schemas/mapfile_validators.py
        validators_requirements: RobustToolbox/Schemas/mapfile_requirements.txt
