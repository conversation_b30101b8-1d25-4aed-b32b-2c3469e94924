# SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 Pieter-<PERSON> <<EMAIL>>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

name: YAML Linter

on:
  push:
    branches: [ master, staging, stable ]
  merge_group:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: YAML Linter
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2
      - name: Setup submodule
        run: |
          git submodule update --init --recursive
      - name: Pull engine updates
        uses: space-wizards/submodule-dependency@v0.1.5
      - name: Update Engine Submodules
        run: |
          cd RobustToolbox/
          git submodule update --init --recursive
      - name: Setup .NET Core
        uses: actions/setup-dotnet@v4.1.0
        with:
          dotnet-version: 9.0.x
      - name: Install dependencies
        run: dotnet restore
      - name: Build
        run: dotnet build --configuration Release --no-restore /p:WarningsAsErrors= /m
      - name: Run Linter
        run: dotnet run --project Content.YAMLLinter/Content.YAMLLinter.csproj --no-build
