{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "<PERSON><PERSON><PERSON> Lin<PERSON>",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build-yaml-linter",
            "program": "${workspaceFolder}/bin/Content.YAMLLinter/Content.YAMLLinter.dll",
            "cwd": "${workspaceFolder}/Content.YAMLLinter",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // Client configurations
        {
            "name": "Client",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client - Tools",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Tools",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client - Release",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Release",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // Compatibility renderer client configurations
        {
            "name": "Client - (Compatibility renderer)",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": "--cvar display.compat=true",
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client - Tools - (Compatibility renderer)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Tools",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": ["--cvar display.compat=true"],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        {
            "name": "Client - Release - (Compatibility renderer)",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Release",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": ["--cvar display.compat=true"],
            "console": "internalConsole",
            "stopAtEntry": false
        },
        // Server configurations
        {
            "name": "Server",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [],
            "console": "integratedTerminal",
            "stopAtEntry": false
        },
        {
            "name": "ServerCursor",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [],
            "console": "integratedTerminal",
            "stopAtEntry": false,
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "powershell",
                "pipeArgs": ["-Command"],
                "debuggerPath": "C:/netcoredbg-win64/netcoredbg/netcoredbg.exe",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
        },
        {
            "name": "ClientCursor",
            "type": "coreclr",
            "request": "launch",
            "program": "${workspaceFolder}/bin/Content.Client/Content.Client.dll",
            "args": [],
            "console": "integratedTerminal",
            "stopAtEntry": false,
            "pipeTransport": {
                "pipeCwd": "${workspaceFolder}",
                "pipeProgram": "powershell",
                "pipeArgs": ["-Command"],
                "debuggerPath": "C:/netcoredbg-win64/netcoredbg/netcoredbg.exe",
                "debuggerArgs": ["--interpreter=vscode"],
                "quoteArgs": true
            },
        },
        {
            "name": "Server - Tools",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Tools",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [""],
            "console": "integratedTerminal",
            "stopAtEntry": false
        },
        {
            "name": "Server - Release",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build - Release",
            "program": "${workspaceFolder}/bin/Content.Server/Content.Server.dll",
            "args": [""],
            "console": "integratedTerminal",
            "stopAtEntry": false
        },
    ],
    "compounds": [
        {
            "name": "Server/Client - Cursor",
            "configurations": [
                "ServerCursor",
                "ClientCursor"
            ],
        },
        {
            "name": "Server/Client",
            "configurations": [
                "Server",
                "Client"
            ],
            "preLaunchTask": "build"
        },
        {
            "name": "Server/Client - Tools",
            "configurations": [
                "Server - Tools",
                "Client - Tools"
            ],
            "preLaunchTask": "build - Tools"
        },
        {
            "name": "Server/Client - Release",
            "configurations": [
                "Server - Release",
                "Client - Release"
            ],
            "preLaunchTask": "build - Release"
        }
    ]
}
