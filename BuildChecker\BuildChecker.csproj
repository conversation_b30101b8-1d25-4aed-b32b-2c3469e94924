<?xml version="1.0" encoding="utf-8"?>
<!--
SPDX-FileCopyrightText: 2017 PJB3005 <<EMAIL>>
SPDX-FileCopyrightText: 2018 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2019 Acruid <<EMAIL>>
SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
SPDX-FileCopyrightText: 2023 Pieter-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
SPDX-FileCopyrightText: 2024 Kira Bridget<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 PoTeletubby <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 MoutardOMiel <<EMAIL>>
SPDX-FileCopyrightText: 2025 Winkarst <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<!--
This is a dummy .csproj file to check things like submodules.
Better this than other errors.

If you want to create this kind of file yourself, you have to create an empty .NET application,
Then strip it of everything until you have the <Project> tags.
VS refuses to load the project if you make a bare project file and use Add -> Existing Project... for some reason.

You want to handle the Build, Clean and Rebuild tasks to prevent missing task errors on build.

If you want to learn more about these kinds of things, check out Microsoft's official documentation about MSBuild:
https://docs.microsoft.com/en-us/visualstudio/msbuild/msbuild
-->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Python>python3</Python>
    <Python Condition="'$(OS)'=='Windows_NT' Or '$(OS)'=='Windows'">py -3</Python>
    <ProjectGuid>{C899FCA4-7037-4E49-ABC2-44DE72487110}</ProjectGuid>
    <TargetFramework>net4.7.2</TargetFramework>
    <RestorePackages>false</RestorePackages>
  </PropertyGroup>
  <PropertyGroup>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Tools|AnyCPU' ">
    <OutputPath>bin\Tools\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'DebugOpt|AnyCPU' ">
    <OutputPath>bin\DebugOpt\</OutputPath>
  </PropertyGroup>
  <Target Name="Build">
    <Exec Command="$(Python) git_helper.py" CustomErrorRegularExpression="^Error" />
  </Target>
  <Target Name="Rebuild" DependsOnTargets="Build" />
  <Target Name="Clean">
    <Message Importance="low" Text="Ignoring 'Clean' target." />
  </Target>
  <Target Name="Compile">
  </Target>
  <Target Name="CoreCompile">
  </Target>
</Project>
