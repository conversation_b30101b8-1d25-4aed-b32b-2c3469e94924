<!--
SPDX-FileCopyrightText: 2020 Acruid <<EMAIL>>
SPDX-FileCopyrightText: 2020 Paul <<EMAIL>>
SPDX-FileCopyrightText: 2020 Víctor <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>riers <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <OutputPath>..\bin\Content.Benchmarks\</OutputPath>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Exe</OutputType>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>12</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="BenchmarkDotNet" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Content.Client\Content.Client.csproj" />
    <ProjectReference Include="..\Content.Server\Content.Server.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
    <ProjectReference Include="..\Content.Tests\Content.Tests.csproj" />
    <ProjectReference Include="..\Content.IntegrationTests\Content.IntegrationTests.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Benchmarks\Robust.Benchmarks.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Server\Robust.Server.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
  </ItemGroup>
</Project>
