// SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 c4llv07e <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Content.Shared.Access;

namespace Content.Client.Access.UI;

[GenerateTypedNameReferences]
public sealed partial class AccessLevelControl : GridContainer
{
    [Dependency] private readonly ILogManager _logManager = default!;

    private ISawmill _sawmill = default!;

    public readonly Dictionary<ProtoId<AccessLevelPrototype>, Button> ButtonsList = new();

    public AccessLevelControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _sawmill = _logManager.GetSawmill("accesslevelcontrol");
    }

    public void Populate(List<ProtoId<AccessLevelPrototype>> accessLevels, IPrototypeManager prototypeManager)
    {
        foreach (var access in accessLevels)
        {
            if (!prototypeManager.TryIndex(access, out var accessLevel))
            {
                _sawmill.Error($"Unable to find accesslevel for {access}");
                continue;
            }

            var newButton = new Button
            {
                Text = accessLevel.GetAccessLevelName(),
                ToggleMode = true,
            };
            AddChild(newButton);
            ButtonsList.Add(accessLevel.ID, newButton);
        }
    }

    public void UpdateState(
        List<ProtoId<AccessLevelPrototype>> pressedList,
        List<ProtoId<AccessLevelPrototype>>? enabledList = null)
    {
        foreach (var (accessName, button) in ButtonsList)
        {
            button.Pressed = pressedList.Contains(accessName);
            button.Disabled = !(enabledList?.Contains(accessName) ?? true);
        }
    }
}