<!--
SPDX-FileCopyrightText: 2023 chromiumboy <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            MinSize="650 290">
    <BoxContainer Orientation="Vertical">
        <GridContainer Columns="2">
            <GridContainer Columns="3" HorizontalExpand="True">
                <Label Text="{Loc 'access-overrider-window-privileged-id'}" />
                <Button Name="PrivilegedIdButton" Access="Public"/>
                <Label Name="PrivilegedIdLabel" />
            </GridContainer>
        </GridContainer>
        <Label Name="TargetNameLabel" />
        <Control MinSize="0 8"/>
        <GridContainer Name="AccessLevelGrid" Columns="5" HorizontalAlignment="Center">

            <!-- Access level buttons are added here by the C# code -->

        </GridContainer>
        <Control MinSize="0 8"/>
        <Label Name="MissingPrivilegesLabel" />
        <Control MinSize="0 4"/>
        <Label Name="MissingPrivilegesText" />
    </BoxContainer>
</DefaultWindow>
