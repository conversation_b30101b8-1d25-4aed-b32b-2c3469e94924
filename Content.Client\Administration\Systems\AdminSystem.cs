// SPDX-FileCopyrightText: 2021 Clyybber <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Shared.Administration;
using Content.Shared.Administration.Events;
using Robust.Shared.Network;

namespace Content.Client.Administration.Systems
{
    public sealed partial class AdminSystem : EntitySystem
    {
        public event Action<List<PlayerInfo>>? PlayerListChanged;

        private Dictionary<NetUserId, PlayerInfo>? _playerList;
        public IReadOnlyList<PlayerInfo> PlayerList
        {
            get
            {
                if (_playerList != null) return _playerList.Values.ToList();

                return new List<PlayerInfo>();
            }
        }

        public override void Initialize()
        {
            base.Initialize();

            InitializeOverlay();
            SubscribeNetworkEvent<FullPlayerListEvent>(OnPlayerListChanged);
            SubscribeNetworkEvent<PlayerInfoChangedEvent>(OnPlayerInfoChanged);
        }

        public override void Shutdown()
        {
            base.Shutdown();
            ShutdownOverlay();
        }

        private void OnPlayerInfoChanged(PlayerInfoChangedEvent ev)
        {
            if(ev.PlayerInfo == null) return;

            if (_playerList == null) _playerList = new();

            _playerList[ev.PlayerInfo.SessionId] = ev.PlayerInfo;
            PlayerListChanged?.Invoke(_playerList.Values.ToList());
        }

        private void OnPlayerListChanged(FullPlayerListEvent msg)
        {
            _playerList = msg.PlayersInfo.ToDictionary(x => x.SessionId, x => x);
            PlayerListChanged?.Invoke(msg.PlayersInfo);
        }
    }
}