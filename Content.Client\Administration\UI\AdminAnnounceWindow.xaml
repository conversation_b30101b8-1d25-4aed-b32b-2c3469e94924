<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Rinkashikachi <<EMAIL>>
SPDX-FileCopyrightText: 2022 Veritius <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    Title="{Loc admin-announce-title}"
    MinWidth="500">
    <GridContainer Columns="1">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <LineEdit Name="Announcer" Access="Public" PlaceHolder="{Loc admin-announce-announcer-placeholder}" Text="{Loc admin-announce-announcer-default}" HorizontalExpand="True" SizeFlagsStretchRatio="2"/>
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="1" />
            <OptionButton Name="AnnounceMethod" Access="Public" HorizontalExpand="True" SizeFlagsStretchRatio="2"/>
        </BoxContainer>
        <TextEdit Name="Announcement" Access="Public" VerticalExpand="True" MinHeight="100" />

        <GridContainer Rows="1">
            <CheckBox Name="KeepWindowOpen" Access="Public" Text="{Loc 'admin-announce-keep-open'}" />
            <Button Name="AnnounceButton" Access="Public" Disabled="True" Text="{Loc admin-announce-button}" HorizontalAlignment="Center"/>
        </GridContainer>
    </GridContainer>
</DefaultWindow>
