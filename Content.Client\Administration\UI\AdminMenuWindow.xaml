<!--
SPDX-FileCopyrightText: 2021 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Myra <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:adminTab="clr-namespace:Content.Client.Administration.UI.Tabs.AdminTab"
    xmlns:adminbusTab="clr-namespace:Content.Client.Administration.UI.Tabs.AdminbusTab"
    xmlns:atmosTab="clr-namespace:Content.Client.Administration.UI.Tabs.AtmosTab"
    xmlns:tabs="clr-namespace:Content.Client.Administration.UI.Tabs"
    xmlns:playerTab="clr-namespace:Content.Client.Administration.UI.Tabs.PlayerTab"
    xmlns:objectsTab="clr-namespace:Content.Client.Administration.UI.Tabs.ObjectsTab"
    xmlns:panic="clr-namespace:Content.Client.Administration.UI.Tabs.PanicBunkerTab">
    <TabContainer Name="MasterTabContainer">
        <adminTab:AdminTab />
        <adminbusTab:AdminbusTab />
        <atmosTab:AtmosTab />
        <tabs:RoundTab />
        <tabs:ServerTab />
        <panic:PanicBunkerTab Name="PanicBunkerControl" Access="Public" />
        <playerTab:PlayerTab Name="PlayerTabControl" Access="Public" />
        <objectsTab:ObjectsTab Name="ObjectsTabControl" Access="Public" />
    </TabContainer>
</DefaultWindow>
