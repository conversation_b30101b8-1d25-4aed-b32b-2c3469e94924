// SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Administration.Notes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Administration.UI.AdminRemarks;

[GenerateTypedNameReferences]
public sealed partial class AdminMessagePopupMessage : Control
{
    public AdminMessagePopupMessage(AdminMessageEuiState.Message message)
    {
        RobustXamlLoader.Load(this);

        Admin.SetMessage(FormattedMessage.FromMarkupOrThrow(Loc.GetString(
            "admin-notes-message-admin",
            ("admin", message.AdminName),
            ("date", message.AddedOn.ToLocalTime()))));

        Message.SetMessage(message.Text);
    }
}