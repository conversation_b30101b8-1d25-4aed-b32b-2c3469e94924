<!--
SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<ui:FancyWindow xmlns="https://spacestation14.io"
                xmlns:ui="clr-namespace:Content.Client.UserInterface.Controls"
                VerticalExpand="True" HorizontalExpand="True"
                Title="{Loc admin-remarks-title}"
                SetSize="600 400">
    <PanelContainer StyleClasses="BackgroundDark">
        <BoxContainer Orientation="Vertical" Margin="4">
            <ScrollContainer VerticalExpand="True" HorizontalExpand="True" HScrollEnabled="False">
                <BoxContainer Orientation="Vertical" Name="NotesContainer" Access="Public" VerticalExpand="True" />
            </ScrollContainer>
        </BoxContainer>
    </PanelContainer>
</ui:FancyWindow>
