// SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using System.Linq;
using Content.Client.Administration.UI.Notes;
using Content.Client.UserInterface.Controls;
using Content.Shared.Administration.Notes;
using Content.Shared.Database;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.AdminRemarks;

[GenerateTypedNameReferences]
public sealed partial class AdminRemarksWindow : FancyWindow
{
    [Dependency] private readonly IEntitySystemManager _entitySystem = default!;
    private readonly SpriteSystem _sprites;
    private readonly Dictionary<(int, NoteType), AdminNotesLine> _inputs = new();

    public AdminRemarksWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _sprites = _entitySystem.GetEntitySystem<SpriteSystem>();
    }

    public void SetNotes(Dictionary<(int, NoteType), SharedAdminNote> notes)
    {
        foreach (var (id, input) in _inputs)
        {
            if (notes.ContainsKey(id))
                continue;
            NotesContainer.RemoveChild(input);
            _inputs.Remove(id);
        }

        foreach (var note in notes.Values.OrderByDescending(note => note.CreatedAt))
        {
            if (_inputs.TryGetValue((note.Id, note.NoteType), out var input))
            {
                input.UpdateNote(note);
                continue;
            }

            input = new AdminNotesLine(_sprites, note);
            NotesContainer.AddChild(input);
            _inputs[(note.Id, note.NoteType)] = input;
        }
    }
}