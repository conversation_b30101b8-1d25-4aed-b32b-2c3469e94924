// SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.BanList;

[GenerateTypedNameReferences]
public sealed partial class BanListIdsPopup : Popup
{
    public BanListIdsPopup(string? id, string? ip, string? hwid, string? guid)
    {
        RobustXamlLoader.Load(this);

        ID.Text = id;
        IP.Text = ip;
        HWId.Text = hwid;
        GUID.Text = guid;

        UserInterfaceManager.ModalRoot.AddChild(this);
    }
}