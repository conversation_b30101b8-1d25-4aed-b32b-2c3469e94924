// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Client.Administration.UI.CustomControls;
using Content.Shared.Administration.BanList;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.BanList.Bans;

[GenerateTypedNameReferences]
public sealed partial class BanListControl : Control
{
    public event Action<BanListLine>? LineIdsClicked;

    public BanListControl()
    {
        RobustXamlLoader.Load(this);
    }

    public void SetBans(List<SharedServerBan> bans)
    {
        for (var i = Bans.ChildCount - 1; i >= 1; i--)
        {
            Bans.GetChild(i).Dispose();
        }

        foreach (var ban in bans)
        {
            Bans.AddChild(new HSeparator());

            var line = new BanListLine(ban);
            line.IdsClicked += LineIdsClicked;

            Bans.AddChild(line);
        }
    }
}