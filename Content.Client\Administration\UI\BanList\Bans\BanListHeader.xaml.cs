// SPDX-FileCopyrightText: 2021 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON>k<PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.BanList.Bans;

[GenerateTypedNameReferences]
public sealed partial class BanListHeader : ContainerButton
{
    public BanListHeader()
    {
        RobustXamlLoader.Load(this);
    }
}