<!--
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 LankLTE <<EMAIL>>
SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Winkarst <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls">
    <PanelContainer StyleClasses="BackgroundDark">
        <SplitContainer Orientation="Vertical" ResizeMode="NotResizable">
            <SplitContainer Orientation="Horizontal" VerticalExpand="True">
                <cc:PlayerListControl Access="Public" Name="ChannelSelector" HorizontalExpand="True" SizeFlagsStretchRatio="2" />
                <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="2">
                    <BoxContainer Access="Public" Name="BwoinkArea" VerticalExpand="True" />
                </BoxContainer>
            </SplitContainer>
            <BoxContainer Orientation="Horizontal" SetHeight="30" >
                <CheckBox Name="AdminOnly" Access="Public" Text="{Loc 'admin-ahelp-admin-only'}" ToolTip="{Loc 'admin-ahelp-admin-only-tooltip'}" />
                <Control HorizontalExpand="True" MinWidth="5" />
                <CheckBox Name="PlaySound" Access="Public" Text="{Loc 'admin-bwoink-play-sound'}" Pressed="True" />
                <Control HorizontalExpand="True" MinWidth="5" />
                <Button Visible="True" Name="PopOut" Access="Public" Text="{Loc 'admin-logs-pop-out'}" StyleClasses="OpenBoth" HorizontalAlignment="Left" />
                <Control HorizontalExpand="True" />
                <Button Visible="False" Name="Bans" Text="{Loc 'admin-player-actions-bans'}" StyleClasses="OpenRight" />
                <Button Visible="False" Name="Notes" Text="{Loc 'admin-player-actions-notes'}" StyleClasses="OpenBoth" />
                <controls:ConfirmButton Visible="False" Name="Kick" Text="{Loc 'admin-player-actions-kick'}" ConfirmationText="{Loc 'admin-player-actions-confirm'}" StyleClasses="OpenBoth" />
                <Button Visible="False" Name="Ban" Text="{Loc 'admin-player-actions-ban'}" StyleClasses="OpenBoth" />
                <controls:ConfirmButton Visible="False" Name="Respawn" Text="{Loc 'admin-player-actions-respawn'}" ConfirmationText="{Loc 'admin-player-actions-confirm'}" StyleClasses="OpenBoth" />
                <Button Visible="False" Name="Follow" Text="{Loc 'admin-player-actions-follow'}" StyleClasses="OpenLeft" />
            </BoxContainer>
        </SplitContainer>
    </PanelContainer>
</Control>
