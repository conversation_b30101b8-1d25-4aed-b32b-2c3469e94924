// SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 T <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 E F R <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ElectroJr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 LankLTE <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX-7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2025 beck-thompson <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Text;
using Content.Client.Administration.Managers;
using Content.Client.Administration.UI.CustomControls;
using Content.Client.UserInterface.Systems.Bwoink;
using Content.Shared.Administration;
using Content.Shared.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.Console;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Network;
using Robust.Shared.Configuration;
using Robust.Shared.Utility;

namespace Content.Client.Administration.UI.Bwoink
{
    /// <summary>
    /// This window connects to a BwoinkSystem channel. BwoinkSystem manages the rest.
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class BwoinkControl : Control
    {
        [Dependency] private readonly IClientAdminManager _adminManager = default!;
        [Dependency] private readonly IClientConsoleHost _console = default!;
        [Dependency] private readonly IUserInterfaceManager _ui = default!;
        [Dependency] private readonly IConfigurationManager _cfg = default!;
        public AdminAHelpUIHandler AHelpHelper = default!;

        private PlayerInfo? _currentPlayer;

        public BwoinkControl()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            var newPlayerThreshold = 0;
            _cfg.OnValueChanged(CCVars.NewPlayerThreshold, (val) => { newPlayerThreshold = val; }, true);

            var uiController = _ui.GetUIController<AHelpUIController>();
            if (uiController.UIHelper is not AdminAHelpUIHandler helper)
                return;

            AHelpHelper = helper;

            _adminManager.AdminStatusUpdated += UpdateButtons;
            UpdateButtons();

            AdminOnly.OnToggled += args => PlaySound.Disabled = args.Pressed;

            ChannelSelector.OnSelectionChanged += sel =>
            {
                _currentPlayer = sel;
                SwitchToChannel(sel?.SessionId);
                ChannelSelector.PlayerListContainer.DirtyList();
            };

            ChannelSelector.OverrideText += (info, text) =>
            {
                var sb = new StringBuilder();

                if (info.Connected)
                    sb.Append(info.ActiveThisRound ? '⚫' : '◐');
                else
                    sb.Append(info.ActiveThisRound ? '⭘' : '·');

                sb.Append(' ');
                if (AHelpHelper.TryGetChannel(info.SessionId, out var panel) && panel.Unread > 0)
                {
                    if (panel.Unread < 11)
                        sb.Append(new Rune('➀' + (panel.Unread-1)));
                    else
                        sb.Append(new Rune(0x2639)); // ☹
                    sb.Append(' ');
                }

                // Mark antagonists with symbol
                if (info.Antag && info.ActiveThisRound)
                    sb.Append(new Rune(0x1F5E1)); // 🗡

                // Mark new players with symbol
                if (IsNewPlayer(info))
                    sb.Append(new Rune(0x23F2)); // ⏲

                sb.AppendFormat("\"{0}\"", text);

                return sb.ToString();
            };

            // <summary>
            // Returns true if the player's overall playtime is under the set threshold
            // </summary>
            bool IsNewPlayer(PlayerInfo info)
            {
                // Don't show every disconnected player as new, don't show 0-minute players as new if threshold is
                if (newPlayerThreshold <= 0 || info.OverallPlaytime is null && !info.Connected)
                    return false;

                return (info.OverallPlaytime is null
                        || info.OverallPlaytime < TimeSpan.FromMinutes(newPlayerThreshold));
            }

            ChannelSelector.Comparison = (a, b) =>
            {
                var ach = AHelpHelper.EnsurePanel(a.SessionId);
                var bch = AHelpHelper.EnsurePanel(b.SessionId);

                // Pinned players first
                if (a.IsPinned != b.IsPinned)
                    return a.IsPinned ? -1 : 1;

                // Then, any chat with unread messages.
                var aUnread = ach.Unread > 0;
                var bUnread = bch.Unread > 0;
                if (aUnread != bUnread)
                    return aUnread ? -1 : 1;

                // Then, any chat with recent messages from the current round
                var aRecent = a.ActiveThisRound && ach.LastMessage != DateTime.MinValue;
                var bRecent = b.ActiveThisRound && bch.LastMessage != DateTime.MinValue;
                if (aRecent != bRecent)
                    return aRecent ? -1 : 1;

                // Sort by connection status. Disconnected players will be last.
                if (a.Connected != b.Connected)
                    return a.Connected ? -1 : 1;

                // Sort connected players by whether they have joined the round, then by New Player status, then by Antag status
                if (a.Connected && b.Connected)
                {
                    var aNewPlayer = IsNewPlayer(a);
                    var bNewPlayer = IsNewPlayer(b);

                    //  Players who have joined the round will be listed before players in the lobby
                    if (a.ActiveThisRound != b.ActiveThisRound)
                        return a.ActiveThisRound ? -1 : 1;

                    //  Within both the joined group and lobby group, new players will be grouped and listed first
                    if (aNewPlayer != bNewPlayer)
                        return aNewPlayer ? -1 : 1;

                    //  Within all four previous groups, antagonists will be listed first.
                    if (a.Antag != b.Antag)
                        return a.Antag ? -1 : 1;
                }

                // Sort disconnected players by participation in the round
                if (!a.Connected && !b.Connected)
                {
                    if (a.ActiveThisRound != b.ActiveThisRound)
                        return a.ActiveThisRound ? -1 : 1;
                }

                // Finally, sort by the most recent message.
                return bch.LastMessage.CompareTo(ach.LastMessage);
            };


            Bans.OnPressed += _ =>
            {
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"banlist \"{_currentPlayer.SessionId}\"");
            };

            Notes.OnPressed += _ =>
            {
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"adminnotes \"{_currentPlayer.SessionId}\"");
            };

            Ban.OnPressed += _ =>
            {
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"banpanel \"{_currentPlayer.SessionId}\"");
            };

            Kick.OnPressed += _ =>
            {
                // TODO: Reason field
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"kick \"{_currentPlayer.Username}\"");
            };

            Follow.OnPressed += _ =>
            {
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"follow \"{_currentPlayer.NetEntity}\"");
            };

            Respawn.OnPressed += _ =>
            {
                if (_currentPlayer is not null)
                    _console.ExecuteCommand($"respawn \"{_currentPlayer.Username}\"");
            };

            PopOut.OnPressed += _ =>
            {
                uiController.PopOut();
            };
        }

        public void OnBwoink(NetUserId channel)
        {
            ChannelSelector.PopulateList();
        }


        public void SelectChannel(NetUserId channel)
        {
            if (!ChannelSelector.PlayerInfo.TryFirstOrDefault(
                i => i.SessionId == channel, out var info))
                return;

            // clear filter if we're trying to select a channel for a player that isn't currently filtered
            // i.e. through the message verb.
            var data = new PlayerListData(info);
            if (!ChannelSelector.PlayerListContainer.Data.Contains(data))
            {
                ChannelSelector.StopFiltering();
            }

            ChannelSelector.PopulateList();
            ChannelSelector.PlayerListContainer.Select(data);
        }

        public void UpdateButtons()
        {
            var disabled = _currentPlayer == null;

            Bans.Visible = _adminManager.HasFlag(AdminFlags.Ban);
            Bans.Disabled = !Bans.Visible || disabled;

            Notes.Visible = _adminManager.HasFlag(AdminFlags.ViewNotes);
            Notes.Disabled = !Notes.Visible || disabled;

            Ban.Visible = _adminManager.HasFlag(AdminFlags.Ban);
            Ban.Disabled = !Ban.Visible || disabled;

            Kick.Visible = _adminManager.CanCommand("kick");
            Kick.Disabled = !Kick.Visible || disabled;

            Respawn.Visible = _adminManager.CanCommand("respawn");
            Respawn.Disabled = !Respawn.Visible || disabled;

            Follow.Visible = _adminManager.CanCommand("follow");
            Follow.Disabled = !Follow.Visible || disabled;
        }

        private string FormatTabTitle(ItemList.Item li, PlayerInfo? pl = default)
        {
            pl ??= (PlayerInfo) li.Metadata!;
            var sb = new StringBuilder();
            sb.Append(pl.Connected ? '●' : '○');
            sb.Append(' ');
            if (AHelpHelper.TryGetChannel(pl.SessionId, out var panel) && panel.Unread > 0)
            {
                if (panel.Unread < 11)
                    sb.Append(new Rune('➀' + (panel.Unread-1)));
                else
                    sb.Append(new Rune(0x2639)); // ☹
                sb.Append(' ');
            }

            if (pl.Antag)
                sb.Append(new Rune(0x1F5E1)); // 🗡

            if (pl.OverallPlaytime <= TimeSpan.FromMinutes(_cfg.GetCVar(CCVars.NewPlayerThreshold)))
                sb.Append(new Rune(0x23F2)); // ⏲

            sb.AppendFormat("\"{0}\"", pl.CharacterName);

            if (pl.IdentityName != pl.CharacterName && pl.IdentityName != string.Empty)
                sb.Append(' ').AppendFormat("[{0}]", pl.IdentityName);

            sb.Append(' ').Append(pl.Username);

            return sb.ToString();
        }

        private void SwitchToChannel(NetUserId? ch)
        {
            UpdateButtons();

            AHelpHelper.HideAllPanels();
            if (ch != null)
            {
                var panel = AHelpHelper.EnsurePanel(ch.Value);
                panel.Visible = true;
            }
        }

        public void PopulateList()
        {
            // Maintain existing pin statuses
            var pinnedPlayers = ChannelSelector.PlayerInfo.Where(p => p.IsPinned).ToDictionary(p => p.SessionId);

            ChannelSelector.PopulateList();

            // Restore pin statuses
            foreach (var player in ChannelSelector.PlayerInfo)
            {
                if (pinnedPlayers.TryGetValue(player.SessionId, out var pinnedPlayer))
                {
                    player.IsPinned = pinnedPlayer.IsPinned;
                }
            }

            UpdateButtons();
        }
    }
}