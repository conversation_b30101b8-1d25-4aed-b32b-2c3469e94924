<!--
SPDX-FileCopyrightText: 2022 E F R <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<BoxContainer
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Orientation="Vertical"
    HorizontalExpand="True">
    <OutputPanel Name="TextOutput" VerticalExpand="true" />
    <RichTextLabel Name="TypingIndicator" Access="Public" />
    <HistoryLineEdit Name="SenderLineEdit" />
    <RichTextLabel Name="RelayedToDiscordLabel" Access="Public" Visible="False" />
</BoxContainer>
