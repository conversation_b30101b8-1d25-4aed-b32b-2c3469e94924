<!--
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:cc="clr-namespace:Content.Client.Administration.UI.Bwoink"
            SetSize="900 500"
            HeaderClass="windowHeaderAlert"
            TitleClass="windowTitleAlert"
            Title="{Loc 'bwoink-user-title'}" >
    <cc:BwoinkControl Name="Bwoink" Access="Public"/>
</DefaultWindow>
