<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 E F R <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<BoxContainer xmlns="https://spacestation14.io"
              xmlns:controls="using:Content.Client.UserInterface.Controls"
              Orientation="Vertical">
    <Control MinSize="0 5" />
    <LineEdit Name="FilterLineEdit"
              MinSize="100 0"
              HorizontalExpand="True"
              PlaceHolder="{Loc player-list-filter}"/>
    <PanelContainer Name="BackgroundPanel"
                    VerticalExpand="True"
                    HorizontalExpand="True">
        <controls:ListContainer Name="PlayerListContainer"
                  Access="Public"
                  Toggle="True"
                  Group="True"
                  MinSize="100 0"/>
    </PanelContainer>
</BoxContainer>
