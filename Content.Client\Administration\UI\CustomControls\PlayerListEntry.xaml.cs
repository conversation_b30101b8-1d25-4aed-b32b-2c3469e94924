// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Stylesheets;
using Content.Shared.Administration;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.CustomControls;

[GenerateTypedNameReferences]
public sealed partial class PlayerListEntry : BoxContainer
{
    public PlayerListEntry()
    {
        RobustXamlLoader.Load(this);
    }

    public event Action<PlayerInfo>? OnPinStatusChanged;

    public void Setup(PlayerInfo info, Func<PlayerInfo, string, string>? overrideText)
    {
        Update(info, overrideText);
        PlayerEntryPinButton.OnPressed += HandlePinButtonPressed(info);
    }

    private Action<BaseButton.ButtonEventArgs> HandlePinButtonPressed(PlayerInfo info)
    {
        return args =>
        {
            info.IsPinned = !info.IsPinned;
            UpdatePinButtonTexture(info.IsPinned);
            OnPinStatusChanged?.Invoke(info);
        };
    }

    private void Update(PlayerInfo info, Func<PlayerInfo, string, string>? overrideText)
    {
        PlayerEntryLabel.Text = overrideText?.Invoke(info, $"{info.CharacterName} ({info.Username})") ??
                                $"{info.CharacterName} ({info.Username})";

        UpdatePinButtonTexture(info.IsPinned);
    }

    private void UpdatePinButtonTexture(bool isPinned)
    {
        if (isPinned)
        {
            PlayerEntryPinButton?.RemoveStyleClass(StyleNano.StyleClassPinButtonUnpinned);
            PlayerEntryPinButton?.AddStyleClass(StyleNano.StyleClassPinButtonPinned);
        }
        else
        {
            PlayerEntryPinButton?.RemoveStyleClass(StyleNano.StyleClassPinButtonPinned);
            PlayerEntryPinButton?.AddStyleClass(StyleNano.StyleClassPinButtonUnpinned);
        }
    }
}