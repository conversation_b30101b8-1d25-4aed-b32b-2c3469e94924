<!--
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+Aiden<PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow
    SetSize="250 300"
    Margin="4 0"
    xmlns="https://spacestation14.io">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Name="QuantityBox" Orientation="Horizontal" Margin ="0 4">
            <Label Text="{Loc 'admin-add-reagent-window-amount-label'}" Margin="0 0 10 0"/>
        </BoxContainer>
        <LineEdit Name="SearchBar" PlaceHolder="{Loc 'admin-add-reagent-window-search-placeholder'}" HorizontalExpand="True"  Margin ="0 4"/>
        <ItemList Name="ReagentList" SelectMode="Single" VerticalExpand="True"  Margin ="0 4"/>
        <Button Name="AddButton" HorizontalExpand="True" Margin ="0 4" />
    </BoxContainer>
</DefaultWindow>
