<!--
SPDX-FileCopyrightText: 2021 TemporalOroboros <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow
    SetSize="300 400"
    Margin="4 0"
    xmlns="https://spacestation14.io">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 4">
            <Label Text ="{Loc 'admin-solutions-window-solution-label'}" Margin="0 0 10 0"/>
            <OptionButton Name="SolutionOption" HorizontalExpand="True"/>
        </BoxContainer>

        <!-- The total volume / capacity of the solution -->
        <BoxContainer Name="VolumeBox" Orientation="Vertical" HorizontalExpand="True" Margin="0 4"/>

        <!-- The temperature / heat capacity / thermal energy of the solution -->
        <Collapsible>
            <CollapsibleHeading Name="ThermalHeading" Title="{Loc 'admin-solutions-window-thermals'}" />
            <CollapsibleBody>
                <BoxContainer Name="ThermalBox" Orientation="Vertical" HorizontalExpand="True" Margin="0 4"/>
            </CollapsibleBody>
        </Collapsible>

        <!-- The reagents in the solution -->
        <ScrollContainer HorizontalExpand="True" VerticalExpand="True" Margin="0 4">
            <BoxContainer Name="ReagentList" Orientation="Vertical"/>
        </ScrollContainer>

        <Button Name="AddButton" Text="{Loc 'admin-solutions-window-add-new-button'}" HorizontalExpand="True"  Margin="0 4"/>
    </BoxContainer>
</DefaultWindow>
