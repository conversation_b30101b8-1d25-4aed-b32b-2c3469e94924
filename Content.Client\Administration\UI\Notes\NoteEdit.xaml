<!--
SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="Loading..."
                      MinSize="400 200">
    <BoxContainer Orientation="Vertical" Margin="4">
        <TextEdit Name="NoteTextEdit" HorizontalExpand="True" VerticalExpand="True" />
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Name="ExpiryLabel" Text="{Loc admin-note-editor-expiry-label}" Visible="False" />
            <HistoryLineEdit Name="ExpiryLineEdit" PlaceHolder="{Loc admin-note-editor-expiry-placeholder}"
                             Visible="False" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <OptionButton Name="TypeOption" HorizontalAlignment="Center" />
            <OptionButton Name="SeverityOption" HorizontalAlignment="Center" />
            <CheckBox Name="SecretCheckBox" Text="{Loc admin-note-editor-secret}"
                      ToolTip="{Loc admin-note-editor-secret-tooltip}" />
            <CheckBox Name="PermanentCheckBox" Pressed="True" Text="{Loc admin-note-editor-expiry-checkbox}"
                      ToolTip="{Loc admin-note-editor-expiry-checkbox-tooltip}" />
            <Button Name="SubmitButton" Text="{Loc admin-note-editor-submit}" HorizontalAlignment="Right" />
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
