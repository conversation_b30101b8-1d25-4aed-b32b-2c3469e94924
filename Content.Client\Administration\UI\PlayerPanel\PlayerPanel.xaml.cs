// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <103440971+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IgorAnt028 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Administration.Managers;
using Content.Client.UserInterface.Controls;
using Content.Shared.Administration;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Network;
using Robust.Shared.Utility;

namespace Content.Client.Administration.UI.PlayerPanel;

[GenerateTypedNameReferences]
public sealed partial class PlayerPanel : FancyWindow
{
    private readonly IClientAdminManager _adminManager;

    public event Action<string>? OnUsernameCopy;
    public event Action<NetUserId?>? OnOpenNotes;
    public event Action<NetUserId?>? OnOpenBans;
    public event Action<NetUserId?>? OnAhelp;
    public event Action<string?>? OnKick;
    public event Action<NetUserId?>? OnOpenBanPanel;
    public event Action<NetUserId?, bool>? OnWhitelistToggle;
    public event Action? OnFollow;
    public event Action? OnFreezeAndMuteToggle;
    public event Action? OnFreeze;
    public event Action? OnLogs;
    public event Action? OnDelete;
    public event Action? OnRejuvenate;

    public NetUserId? TargetPlayer;
    public string? TargetUsername;
    private bool _isWhitelisted;

    public PlayerPanel(IClientAdminManager adminManager)
    {
            RobustXamlLoader.Load(this);
            _adminManager = adminManager;

            UsernameCopyButton.OnPressed += _ => OnUsernameCopy?.Invoke(TargetUsername ?? "");
            BanButton.OnPressed += _ => OnOpenBanPanel?.Invoke(TargetPlayer);
            KickButton.OnPressed += _ => OnKick?.Invoke(TargetUsername);
            NotesButton.OnPressed += _ => OnOpenNotes?.Invoke(TargetPlayer);
            ShowBansButton.OnPressed += _ => OnOpenBans?.Invoke(TargetPlayer);
            AhelpButton.OnPressed += _ => OnAhelp?.Invoke(TargetPlayer);
            WhitelistToggle.OnPressed += _ =>
            {
                OnWhitelistToggle?.Invoke(TargetPlayer, _isWhitelisted);
                SetWhitelisted(!_isWhitelisted);
            };
            FollowButton.OnPressed += _ => OnFollow?.Invoke();
            FreezeButton.OnPressed += _ => OnFreeze?.Invoke();
            FreezeAndMuteToggleButton.OnPressed += _ => OnFreezeAndMuteToggle?.Invoke();
            LogsButton.OnPressed += _ => OnLogs?.Invoke();
            DeleteButton.OnPressed += _ => OnDelete?.Invoke();
            RejuvenateButton.OnPressed += _ => OnRejuvenate?.Invoke();
    }

    public void SetUsername(string player)
    {
        Title = Loc.GetString("player-panel-title", ("player", player));
        PlayerName.Text = Loc.GetString("player-panel-username", ("player", player));
    }

    public void SetWhitelisted(bool? whitelisted)
    {
        if (whitelisted == null)
        {
            Whitelisted.Text = null;
            WhitelistToggle.Visible = false;
        }
        else
        {
            Whitelisted.Text = Loc.GetString("player-panel-whitelisted");
            WhitelistToggle.Text = whitelisted.Value ? Loc.GetString("player-panel-true") : Loc.GetString("player-panel-false");
            WhitelistToggle.Visible = true;
            _isWhitelisted = whitelisted.Value;
        }
    }

    public void SetBans(int? totalBans, int? totalRoleBans)
    {
        // If one value exists then so should the other.
        DebugTools.Assert(totalBans.HasValue && totalRoleBans.HasValue || totalBans == null && totalRoleBans == null);

        Bans.Text = totalBans != null ? Loc.GetString("player-panel-bans", ("totalBans", totalBans)) : null;

        RoleBans.Text = totalRoleBans != null ? Loc.GetString("player-panel-rolebans", ("totalRoleBans", totalRoleBans)) : null;
    }

    public void SetNotes(int? totalNotes)
    {
        Notes.Text = totalNotes != null ? Loc.GetString("player-panel-notes", ("totalNotes", totalNotes)) : null;
    }

    public void SetSharedConnections(int sharedConnections)
    {
        SharedConnections.Text = Loc.GetString("player-panel-shared-connections", ("sharedConnections", sharedConnections));
    }

    public void SetPlaytime(TimeSpan playtime)
    {
        Playtime.Text = Loc.GetString("player-panel-playtime",
            ("days", playtime.Days),
            ("hours", playtime.Hours % 24),
            ("minutes", playtime.Minutes % (24 * 60)));
    }

    public void SetFrozen(bool canFreeze, bool frozen)
    {
        FreezeAndMuteToggleButton.Disabled = !canFreeze;
        FreezeButton.Disabled = !canFreeze || frozen;

        FreezeAndMuteToggleButton.Text = Loc.GetString(!frozen ? "player-panel-freeze-and-mute" : "player-panel-unfreeze");
    }

    public void SetAhelp(bool canAhelp)
    {
        AhelpButton.Disabled = !canAhelp;
    }

    public void SetButtons()
    {
        BanButton.Disabled = !_adminManager.CanCommand("banpanel");
        KickButton.Disabled = !_adminManager.CanCommand("kick");
        NotesButton.Disabled = !_adminManager.CanCommand("adminnotes");
        ShowBansButton.Disabled = !_adminManager.CanCommand("banlist");
        WhitelistToggle.Disabled =
            !(_adminManager.CanCommand("whitelistadd") && _adminManager.CanCommand("whitelistremove"));
        LogsButton.Disabled = !_adminManager.CanCommand("adminlogs");
        RejuvenateButton.Disabled = !_adminManager.HasFlag(AdminFlags.Debug);
        DeleteButton.Disabled = !_adminManager.HasFlag(AdminFlags.Debug);
    }
}