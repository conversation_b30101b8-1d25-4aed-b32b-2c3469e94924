// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <103440971+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Administration.Managers;
using Content.Client.Eui;
using Content.Shared.Administration;
using Content.Shared.Eui;
using JetBrains.Annotations;
using Robust.Client.Console;
using Robust.Client.UserInterface;

namespace Content.Client.Administration.UI.PlayerPanel;

[UsedImplicitly]
public sealed class PlayerPanelEui : BaseEui
{
    [Dependency] private readonly IClientConsoleHost _console = default!;
    [Dependency] private readonly IClientAdminManager _admin = default!;
    [Dependency] private readonly IClipboardManager _clipboard = default!;

    private PlayerPanel PlayerPanel { get;  }

    public PlayerPanelEui()
    {
        PlayerPanel = new PlayerPanel(_admin);

        PlayerPanel.OnUsernameCopy += username => _clipboard.SetText(username);
        PlayerPanel.OnOpenNotes += id => _console.ExecuteCommand($"adminnotes \"{id}\"");
        // Kick command does not support GUIDs
        PlayerPanel.OnKick += username => _console.ExecuteCommand($"kick \"{username}\"");
        PlayerPanel.OnOpenBanPanel += id => _console.ExecuteCommand($"banpanel \"{id}\"");
        PlayerPanel.OnOpenBans += id => _console.ExecuteCommand($"banlist \"{id}\"");
        PlayerPanel.OnAhelp += id => _console.ExecuteCommand($"openahelp \"{id}\"");
        PlayerPanel.OnWhitelistToggle += (id, whitelisted) =>
        {
            _console.ExecuteCommand(whitelisted ? $"whitelistremove \"{id}\"" : $"whitelistadd \"{id}\"");
        };

        PlayerPanel.OnFreezeAndMuteToggle += () => SendMessage(new PlayerPanelFreezeMessage(true));
        PlayerPanel.OnFreeze += () => SendMessage(new PlayerPanelFreezeMessage());
        PlayerPanel.OnLogs += () => SendMessage(new PlayerPanelLogsMessage());
        PlayerPanel.OnRejuvenate += () => SendMessage(new PlayerPanelRejuvenationMessage());
        PlayerPanel.OnDelete+= () => SendMessage(new PlayerPanelDeleteMessage());
        PlayerPanel.OnFollow += () => SendMessage(new PlayerPanelFollowMessage());

        PlayerPanel.OnClose += () => SendMessage(new CloseEuiMessage());
    }

    public override void Opened()
    {
        PlayerPanel.OpenCentered();
    }

    public override void Closed()
    {
        PlayerPanel.Close();
    }

    public override void HandleState(EuiStateBase state)
    {
        if (state is not PlayerPanelEuiState s)
            return;

        PlayerPanel.TargetPlayer = s.Guid;
        PlayerPanel.TargetUsername = s.Username;
        PlayerPanel.SetUsername(s.Username);
        PlayerPanel.SetPlaytime(s.Playtime);
        PlayerPanel.SetBans(s.TotalBans, s.TotalRoleBans);
        PlayerPanel.SetNotes(s.TotalNotes);
        PlayerPanel.SetWhitelisted(s.Whitelisted);
        PlayerPanel.SetSharedConnections(s.SharedConnections);
        PlayerPanel.SetFrozen(s.CanFreeze, s.Frozen);
        PlayerPanel.SetAhelp(s.CanAhelp);
        PlayerPanel.SetButtons();
    }
}