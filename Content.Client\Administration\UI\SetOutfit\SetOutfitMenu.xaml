<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow
    xmlns="https://spacestation14.io">
    <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
        <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="0.45">
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True" VerticalExpand="True"
                                    SizeFlagsStretchRatio="0.1">
                <LineEdit Name="SearchBar" PlaceHolder="Search" HorizontalExpand="True"
                                   SizeFlagsStretchRatio="0.6" />
            </BoxContainer>
            <ItemList Name="OutfitList" SelectMode="Single" VerticalExpand="True"
                               SizeFlagsStretchRatio="0.9" />
            <Button Name="ConfirmButton" HorizontalExpand="True" />
            <CheckBox  Name="ToggleSpecial" HorizontalExpand="True" /> <!-- Goobstation -->
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
