<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Rinkashikachi <<EMAIL>>
SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:at="clr-namespace:Content.Client.Administration.UI.Tabs.AdminTab"
    Margin="4"
    MinSize="50 50">
    <BoxContainer Orientation="Vertical">
        <GridContainer Columns="3">
            <cc:UICommandButton Command="kick" Text="{Loc admin-player-actions-window-title}" WindowType="{x:Type at:PlayerActionsWindow}" />
            <cc:CommandButton Command="banpanel" Text="{Loc admin-player-actions-window-ban}" />
            <cc:CommandButton Command="aghost" Text="{Loc admin-player-actions-window-admin-ghost}" />
            <cc:UICommandButton Command="tpto" Text="{Loc admin-player-actions-window-teleport}" WindowType="{x:Type at:TeleportWindow}" />
            <cc:CommandButton Command="permissions" Text="{Loc admin-player-actions-window-permissions}" />
            <cc:CommandButton Command="announceui" Text="{Loc admin-player-actions-window-announce}"/>
            <cc:UICommandButton Command="callshuttle" Text="{Loc admin-player-actions-window-shuttle}" WindowType="{x:Type at:AdminShuttleWindow}"/>
            <cc:CommandButton Command="adminlogs" Text="{Loc admin-player-actions-window-admin-logs}"/>
            <cc:CommandButton Command="faxui" Text="{Loc admin-player-actions-window-admin-fax}"/>
            <cc:CommandButton Command="timetransferpanel" Text="{Loc admin-player-actions-window-time-transfer}"/>
            <cc:CommandButton Command="achatwindow" Text="{Loc admin-player-actions-window-admin-chat}"/>
        </GridContainer>
    </BoxContainer>
</Control>
