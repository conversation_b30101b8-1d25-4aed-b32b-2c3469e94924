<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
SPDX-FileCopyrightText: 2022 E F R <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    Title="{Loc admin-player-actions-window-title}" MinSize="425 272">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-player-actions-reason}" MinWidth="100" />
            <Control MinWidth="50" />
            <LineEdit Name="ReasonLine" MinWidth="100" HorizontalExpand="True" />
        </BoxContainer>
        <cc:PlayerListControl Name="PlayerList" VerticalExpand="True" />
        <BoxContainer Orientation="Horizontal">
            <controls:ConfirmButton Name="SubmitKickButton" Text="{Loc admin-player-actions-kick}" ConfirmationText="{Loc 'admin-player-actions-confirm'}" Disabled="True"/>
            <Button Name="SubmitAHelpButton" Text="{Loc admin-player-actions-ahelp}" Disabled="True"/>
            <controls:ConfirmButton Name="SubmitRespawnButton" Text="{Loc admin-player-actions-respawn}" ConfirmationText="{Loc 'admin-player-actions-confirm'}" Disabled="True"/>
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
