<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow
    xmlns="https://spacestation14.io" Title="{Loc admin-ui-atmos-add-gas}">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-ui-atmos-grid}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <OptionButton Name="GridOptions" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-ui-atmos-tile-x}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="TileXSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-ui-atmos-tile-y}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="TileYSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-ui-atmos-gas}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <OptionButton Name="GasOptions" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc admin-ui-atmos-gas-amount}" MinSize="100 0" />
            <Control MinSize="50 0" />
            <SpinBox Name="AmountSpin" MinSize="100 0" HorizontalExpand="True" />
        </BoxContainer>
        <Button Name="SubmitButton" Text="{Loc admin-ui-atmos-add-gas}" />
    </BoxContainer>
</DefaultWindow>
