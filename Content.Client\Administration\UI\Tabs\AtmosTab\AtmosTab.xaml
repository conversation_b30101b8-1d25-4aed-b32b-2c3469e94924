<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:at="clr-namespace:Content.Client.Administration.UI.Tabs.AtmosTab"
    Margin="4"
    MinSize="50 50">
    <GridContainer Columns="4">
        <cc:UICommandButton Text="{Loc admin-ui-atmos-add}" Command="addatmos" WindowType="{x:Type at:AddAtmosWindow}" />
        <cc:UICommandButton Text="{Loc admin-ui-atmos-add-gas}" Command="addgas" WindowType="{x:Type at:AddGasWindow}" />
        <cc:UICommandButton Text="{Loc admin-ui-atmos-fill-gas}" Command="fillgas" WindowType="{x:Type at:FillGasWindow}" />
        <cc:UICommandButton Text="{Loc admin-ui-atmos-set-temperature}" Command="settemp"
                             WindowType="{x:Type at:SetTemperatureWindow}" />
    </GridContainer>
</Control>
