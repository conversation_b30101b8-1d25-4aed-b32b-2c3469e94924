<!--
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
         xmlns:ot="clr-namespace:Content.Client.Administration.UI.Tabs.ObjectsTab"
         xmlns:co="clr-namespace:Content.Client.UserInterface.Controls">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc object-tab-object-type}" />
            <OptionButton Name="ObjectTypeOptions" HorizontalAlignment="Left" />
            <LineEdit Name="SearchLineEdit" PlaceHolder="{Loc object-tab-object-search}" HorizontalExpand="True"
                      SizeFlagsStretchRatio="1" />
            <Button Name="RefreshListButton" Text="{Loc object-tab-refresh-button}" ToggleMode="False" />
        </BoxContainer>
        <cc:HSeparator />
        <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
            <ot:ObjectsTabHeader Name="ListHeader" />
            <cc:HSeparator />
            <co:SearchListContainer Name="SearchList" Access="Public" VerticalExpand="True" />
        </BoxContainer>
    </BoxContainer>
</Control>
