<!--
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2023 Pieter-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<PanelContainer xmlns="https://spacestation14.io"
                xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                Name="BackgroundColorPanel">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="NameLabel"
               SizeFlagsStretchRatio="5"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="EIDLabel"
               SizeFlagsStretchRatio="5"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Button Name="TeleportButton"
                Text="{Loc object-tab-entity-teleport}"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Button Name="DeleteButton"
                Text="{Loc object-tab-entity-delete}"
                SizeFlagsStretchRatio="3"
                HorizontalExpand="True"
                ClipText="True"/>
    </BoxContainer>
</PanelContainer>
