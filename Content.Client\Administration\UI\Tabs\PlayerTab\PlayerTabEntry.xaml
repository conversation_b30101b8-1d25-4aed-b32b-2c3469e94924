<!--
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aidenk<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<PanelContainer xmlns="https://spacestation14.io"
                xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                Name="BackgroundColorPanel">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="UsernameLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="CharacterLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="JobLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="RoleTypeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"/>
        <customControls:VSeparator/>
        <Label Name="OverallPlaytimeLabel"
               SizeFlagsStretchRatio="1"
               HorizontalExpand="True"
               ClipText="True"/>
    </BoxContainer>
</PanelContainer>
