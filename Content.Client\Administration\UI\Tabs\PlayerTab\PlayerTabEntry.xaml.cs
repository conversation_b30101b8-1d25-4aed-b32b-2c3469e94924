// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX-7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 slarticodefast <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Administration;
using Content.Shared.Roles;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Administration.UI.Tabs.PlayerTab;

[GenerateTypedNameReferences]
public sealed partial class PlayerTabEntry : PanelContainer
{
    [Dependency] private readonly IEntityManager _entMan = default!;

    public PlayerTabEntry(
        PlayerInfo player,
        StyleBoxFlat styleBoxFlat,
        AdminPlayerTabColorOption colorOption,
        AdminPlayerTabRoleTypeOption roleSetting,
        AdminPlayerTabSymbolOption symbolSetting)
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);
        var roles = _entMan.System<SharedRoleSystem>();

        UsernameLabel.Text = player.Username;
        if (!player.Connected)
            UsernameLabel.StyleClasses.Add("Disabled");
        JobLabel.Text = player.StartingJob;

        var colorAntags = false;
        var colorRoles = false;
        switch (colorOption)
        {
            case AdminPlayerTabColorOption.Off:
                break;
            case AdminPlayerTabColorOption.Character:
                colorAntags = true;
                break;
            case AdminPlayerTabColorOption.Roletype:
                colorRoles = true;
                break;
            default:
            case AdminPlayerTabColorOption.Both:
                colorAntags = true;
                colorRoles = true;
                break;
        }

        var symbol = string.Empty;
        switch (symbolSetting)
        {
            case AdminPlayerTabSymbolOption.Off:
                break;
            case AdminPlayerTabSymbolOption.Basic:
                symbol = player.Antag ? Loc.GetString("player-tab-antag-prefix") : string.Empty;
                break;
            default:
            case AdminPlayerTabSymbolOption.Specific:
                symbol = player.Antag ? player.RoleProto.Symbol : string.Empty;
                break;
        }

        CharacterLabel.Text = Loc.GetString("player-tab-character-name-antag-symbol", ("symbol", symbol), ("name", player.CharacterName));

        if (player.Antag && colorAntags)
            CharacterLabel.FontColorOverride = player.RoleProto.Color;
        if (player.IdentityName != player.CharacterName)
            CharacterLabel.Text += $" [{player.IdentityName}]";

        var roletype = RoleTypeLabel.Text = Loc.GetString(player.RoleProto.Name);
        var subtype = roles.GetRoleSubtypeLabel(player.RoleProto.Name, player.Subtype);
        switch (roleSetting)
        {
            case AdminPlayerTabRoleTypeOption.RoleTypeSubtype:
                RoleTypeLabel.Text = roletype != subtype
                    ? roletype + " - " +subtype
                    : roletype;
                break;
            case AdminPlayerTabRoleTypeOption.SubtypeRoleType:
                RoleTypeLabel.Text = roletype != subtype
                    ? subtype + " - " + roletype
                    : roletype;
                break;
            case AdminPlayerTabRoleTypeOption.RoleType:
                RoleTypeLabel.Text = roletype;
                break;
            default:
            case AdminPlayerTabRoleTypeOption.Subtype:
                RoleTypeLabel.Text = subtype;
                break;
        }

        if (colorRoles)
            RoleTypeLabel.FontColorOverride = player.RoleProto.Color;
        BackgroundColorPanel.PanelOverride = styleBoxFlat;
        OverallPlaytimeLabel.Text = player.PlaytimeString;
    }
}