<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
                 xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <PanelContainer Name="BackgroundColorPanel" Access="Public"/>
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="UsernameLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-username}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="CharacterLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-character}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="JobLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-job}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="RoleTypeLabel"
               SizeFlagsStretchRatio="2"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-roletype}"
               MouseFilter="Pass"/>
        <cc:VSeparator/>
        <Label Name="PlaytimeLabel"
               SizeFlagsStretchRatio="1"
               HorizontalExpand="True"
               ClipText="True"
               Text="{Loc player-tab-playtime}"
               MouseFilter="Pass"
               ToolTip="{Loc player-tab-entry-tooltip}"/>
    </BoxContainer>
</Control>
