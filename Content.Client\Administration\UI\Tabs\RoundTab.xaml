<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    Margin="4"
    MinSize="50 50">
    <GridContainer
        Columns="3">
        <cc:CommandButton Command="startround" Text="{Loc administration-ui-round-tab-start-round}" />
        <cc:CommandButton Command="endround" Text="{Loc administration-ui-round-tab-end-round}" />
        <cc:CommandButton Command="restartround" Text="{Loc administration-ui-round-tab-restart-round}" />
        <cc:CommandButton Command="restartroundnow" Text="{Loc administration-ui-round-tab-restart-round-now}" />
    </GridContainer>
</Control>
