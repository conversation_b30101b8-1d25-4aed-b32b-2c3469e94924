// SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Shared.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;

namespace Content.Client.Administration.UI.Tabs
{
    [GenerateTypedNameReferences]
    public sealed partial class ServerTab : Control
    {
        [Dependency] private readonly IConfigurationManager _config = default!;

        public ServerTab()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            _config.OnValueChanged(CCVars.OocEnabled, OocEnabledChanged, true);
            _config.OnValueChanged(CCVars.LoocEnabled, LoocEnabledChanged, true);
        }

        private void OocEnabledChanged(bool value)
        {
            SetOocButton.Pressed = value;
        }

        private void LoocEnabledChanged(bool value)
        {
            SetLoocButton.Pressed = value;
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
            {
                _config.UnsubValueChanged(CCVars.OocEnabled, OocEnabledChanged);
                _config.UnsubValueChanged(CCVars.LoocEnabled, LoocEnabledChanged);
            }
        }
    }
}