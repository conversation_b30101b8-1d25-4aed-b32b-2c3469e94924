// SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2020 ComicIronic <<EMAIL>>
// SPDX-FileCopyrightText: 2020 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Metal Gear Sloth <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2020 <PERSON>-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Pieter-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Víctor Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2020 silicons <<EMAIL>>
// SPDX-FileCopyrightText: 2021 GraniteSidewalk <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ScalyChimp <<EMAIL>>
// SPDX-FileCopyrightText: 2022 hubismal <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TemporalOroboros <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Atmos.Overlays;
using Content.Shared.Atmos;
using Content.Shared.Atmos.Components;
using Content.Shared.Atmos.EntitySystems;
using JetBrains.Annotations;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.ResourceManagement;
using Robust.Shared.GameStates;

namespace Content.Client.Atmos.EntitySystems
{
    [UsedImplicitly]
    public sealed class GasTileOverlaySystem : SharedGasTileOverlaySystem
    {
        [Dependency] private readonly IResourceCache _resourceCache = default!;
        [Dependency] private readonly IOverlayManager _overlayMan = default!;
        [Dependency] private readonly SpriteSystem _spriteSys = default!;
        [Dependency] private readonly SharedTransformSystem _xformSys = default!;

        private GasTileOverlay _overlay = default!;

        public override void Initialize()
        {
            base.Initialize();
            SubscribeNetworkEvent<GasOverlayUpdateEvent>(HandleGasOverlayUpdate);
            SubscribeLocalEvent<GasTileOverlayComponent, ComponentHandleState>(OnHandleState);

            _overlay = new GasTileOverlay(this, EntityManager, _resourceCache, ProtoMan, _spriteSys, _xformSys);
            _overlayMan.AddOverlay(_overlay);
        }

        public override void Shutdown()
        {
            base.Shutdown();
            _overlayMan.RemoveOverlay<GasTileOverlay>();
        }

        private void OnHandleState(EntityUid gridUid, GasTileOverlayComponent comp, ref ComponentHandleState args)
        {
            Dictionary<Vector2i, GasOverlayChunk> modifiedChunks;

            switch (args.Current)
            {
                // is this a delta or full state?
                case GasTileOverlayDeltaState delta:
                {
                    modifiedChunks = delta.ModifiedChunks;
                    foreach (var index in comp.Chunks.Keys)
                    {
                        if (!delta.AllChunks.Contains(index))
                            comp.Chunks.Remove(index);
                    }

                    break;
                }
                case GasTileOverlayState state:
                {
                    modifiedChunks = state.Chunks;
                    foreach (var index in comp.Chunks.Keys)
                    {
                        if (!state.Chunks.ContainsKey(index))
                            comp.Chunks.Remove(index);
                    }

                    break;
                }
                default:
                    return;
            }

            foreach (var (index, data) in modifiedChunks)
            {
                comp.Chunks[index] = data;
            }
        }

        private void HandleGasOverlayUpdate(GasOverlayUpdateEvent ev)
        {
            foreach (var (nent, removedIndicies) in ev.RemovedChunks)
            {
                var grid = GetEntity(nent);

                if (!TryComp(grid, out GasTileOverlayComponent? comp))
                    continue;

                foreach (var index in removedIndicies)
                {
                    comp.Chunks.Remove(index);
                }
            }

            foreach (var (nent, gridData) in ev.UpdatedChunks)
            {
                var grid = GetEntity(nent);

                if (!TryComp(grid, out GasTileOverlayComponent? comp))
                    continue;

                foreach (var chunkData in gridData)
                {
                    comp.Chunks[chunkData.Index] = chunkData;
                }
            }
        }
    }
}