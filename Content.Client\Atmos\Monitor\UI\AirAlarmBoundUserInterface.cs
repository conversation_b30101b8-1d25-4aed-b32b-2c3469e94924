// SPDX-FileCopyrightText: 2022 E F R <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Flipp Syder <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 vulppine <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2023 c4llv07e <<EMAIL>>
// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MilenVolf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Atmos;
using Content.Shared.Atmos.Monitor;
using Content.Shared.Atmos.Monitor.Components;
using Robust.Client.UserInterface;

namespace Content.Client.Atmos.Monitor.UI;

public sealed class AirAlarmBoundUserInterface : BoundUserInterface
{
    private AirAlarmWindow? _window;

    public AirAlarmBoundUserInterface(EntityUid owner, Enum uiKey) : base(owner, uiKey)
    {
    }

    protected override void Open()
    {
        base.Open();

        _window = this.CreateWindow<AirAlarmWindow>();
        _window.SetEntity(Owner);

        _window.AtmosDeviceDataChanged += OnDeviceDataChanged;
		_window.AtmosDeviceDataCopied += OnDeviceDataCopied;
        _window.AtmosAlarmThresholdChanged += OnThresholdChanged;
        _window.AirAlarmModeChanged += OnAirAlarmModeChanged;
        _window.AutoModeChanged += OnAutoModeChanged;
        _window.ResyncAllRequested += ResyncAllDevices;
    }

    private void ResyncAllDevices()
    {
        SendMessage(new AirAlarmResyncAllDevicesMessage());
    }

    private void OnDeviceDataChanged(string address, IAtmosDeviceData data)
    {
        SendMessage(new AirAlarmUpdateDeviceDataMessage(address, data));
    }

	private void OnDeviceDataCopied(IAtmosDeviceData data)
    {
        SendMessage(new AirAlarmCopyDeviceDataMessage(data));
    }

    private void OnAirAlarmModeChanged(AirAlarmMode mode)
    {
        SendMessage(new AirAlarmUpdateAlarmModeMessage(mode));
    }

    private void OnAutoModeChanged(bool enabled)
    {
        SendMessage(new AirAlarmUpdateAutoModeMessage(enabled));
    }

    private void OnThresholdChanged(string address, AtmosMonitorThresholdType type, AtmosAlarmThreshold threshold, Gas? gas = null)
    {
        SendMessage(new AirAlarmUpdateAlarmThresholdMessage(address, type, threshold, gas));
    }

    protected override void UpdateState(BoundUserInterfaceState state)
    {
        base.UpdateState(state);

        if (state is not AirAlarmUIState cast || _window == null)
        {
            return;
        }

        _window.UpdateState(cast);
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);

        if (disposing)
            _window?.Dispose();
    }
}