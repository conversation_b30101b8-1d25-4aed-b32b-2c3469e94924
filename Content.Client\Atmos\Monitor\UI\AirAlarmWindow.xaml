<!--
SPDX-FileCopyrightText: 2022 E<PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flip<PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
SPDX-FileCopyrightText: 2022 vulppine <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 c4llv07e <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<ui:FancyWindow xmlns="https://spacestation14.io"
            xmlns:x="http://schemas.microsoft.com/winfx/2007/xaml"
            xmlns:ui="clr-namespace:Content.Client.UserInterface.Controls"
            xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
            MinSize="500 500" Resizable="True" Title="Air Alarm">
    <BoxContainer Orientation="Vertical" Margin="5 5 5 5">
        <!-- Status (pressure, temperature, alarm state, device total, address, etc) -->
        <BoxContainer Orientation="Horizontal" Margin="0 0 0 2">
            <!-- Left column (view of entity) -->
            <PanelContainer Margin="2 0 6 0" StyleClasses="Inset" VerticalAlignment="Center" VerticalExpand="True">
                <SpriteView Name="EntityView" OverrideDirection="South" Scale="2 2" SetSize="64 64"/>
            </PanelContainer>
            <!-- Center column (pressure, temperature, alarm state) -->
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 0 6 0">
                <BoxContainer Orientation="Vertical" Margin="0 0 2 0" HorizontalExpand="True">
                    <Label Text="{Loc 'air-alarm-ui-window-pressure-label'}" />
                    <Label Text="{Loc 'air-alarm-ui-window-temperature-label'}" />
                    <Label Text="{Loc 'air-alarm-ui-window-alarm-state-label'}" />
                </BoxContainer>
                <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                    <RichTextLabel Name="CPressureLabel" />
                    <RichTextLabel Name="CTemperatureLabel" />
                    <RichTextLabel Name="CStatusLabel" />
                </BoxContainer>
            </BoxContainer>
            <!-- Right column (address, device total) -->
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal" Margin="0 0 2 0" HorizontalExpand="True">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-window-address-label'}" />
                        <Label Text="{Loc 'air-alarm-ui-window-device-count-label'}" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <RichTextLabel Name="CDeviceAddress" HorizontalAlignment="Right" />
                        <RichTextLabel Name="CDeviceTotal" HorizontalAlignment="Right" />
                    </BoxContainer>
                </BoxContainer>
                <Button Name="CResyncButton" Text="{Loc 'air-alarm-ui-window-resync-devices-label'}" HorizontalExpand="True" />
            </BoxContainer>
        </BoxContainer>
        <!-- Gas/Device Data -->
        <TabContainer Name="CTabContainer" VerticalExpand="True" Margin="0 0 0 2">
            <!-- Vent devices -->
            <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>

                <ScrollContainer>
                    <BoxContainer Name="CVentContainer" Orientation="Vertical"/>
                </ScrollContainer>
            </PanelContainer>
            <!-- Scrubber devices -->
            <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>

                <ScrollContainer>
                    <BoxContainer Name="CScrubberContainer" Orientation="Vertical"/>
                </ScrollContainer>
            </PanelContainer>
            <!-- Sensors -->
            <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>

                <ScrollContainer>
                    <BoxContainer Name="CSensorContainer" Orientation="Vertical"/>
                </ScrollContainer>
            </PanelContainer>
        </TabContainer>
        <!-- Mode buttons -->
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'air-alarm-ui-window-mode-label'}" Margin="0 0 2 0" />
            <OptionButton Name="CModeButton" HorizontalExpand="True" />
            <CheckBox Name="AutoModeCheckBox" Text="{Loc 'air-alarm-ui-window-auto-mode-label'}" />
        </BoxContainer>
    </BoxContainer>
</ui:FancyWindow>
