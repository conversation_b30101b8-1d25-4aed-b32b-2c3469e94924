<!--
SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flip<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <6766154+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <eoin.mc<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2023 Ilya246 <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical" Margin="2 0 2 4">
    <Collapsible>
        <CollapsibleHeading Name="CAddress" />
        <!-- Upper row: toggle, direction, checks -->
        <CollapsibleBody Margin="20 0 0 0">
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal" Margin ="0 0 0 2">
                    <CheckBox Name="CEnableDevice" Text="{Loc 'air-alarm-ui-widget-enable'}" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal" Margin="0 0 0 2" HorizontalExpand="True">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-vent-pump-label'}" Margin="0 0 0 1" />
                        <OptionButton Name="CPumpDirection" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-vent-pressure-label'}" Margin="0 0 0 1" />
                        <OptionButton Name="CPressureCheck" />
                    </BoxContainer>
                </BoxContainer>
                <!-- Lower row: pressure bounds, copy settings -->
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-vent-external-bound-label'}" Margin="0 0 0 1" />
                        <FloatSpinBox Name="CExternalBound" HorizontalExpand="True" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-vent-internal-bound-label'}" Margin="0 0 0 1" />
                        <FloatSpinBox Name="CInternalBound" HorizontalExpand="True" />
                    </BoxContainer>
                </BoxContainer>
                <BoxContainer Orientation="Horizontal" Margin ="0 0 0 2">
                    <Button Name="CCopySettings" Text="{Loc 'air-alarm-ui-widget-copy'}" ToolTip="{Loc 'air-alarm-ui-widget-copy-tooltip'}" />
                </BoxContainer>
            </BoxContainer>
        </CollapsibleBody>
    </Collapsible>
</BoxContainer>
