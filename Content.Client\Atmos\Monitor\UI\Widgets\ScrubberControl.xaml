<!--
SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flip<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <6766154+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <eoin.mc<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2023 Ilya246 <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical" Margin="2 0 2 4">
    <Collapsible>
        <CollapsibleHeading Name="CAddress" />
        <CollapsibleBody Margin="20 0 0 0">
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal" Margin="0 0 0 2">
                    <CheckBox Name="CEnableDevice" Text="{Loc 'air-alarm-ui-widget-enable'}" />
                </BoxContainer>
                <!-- Upper row: toggle, direction, volume rate, widenet, copy settings -->
                <BoxContainer Orientation="Horizontal" Margin="0 0 0 2" HorizontalExpand="True">
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-scrubber-pump-direction-label'}" Margin="0 0 0 1"/>
                        <OptionButton Name="CPumpDirection" HorizontalExpand="True" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                        <Label Text="{Loc 'air-alarm-ui-scrubber-volume-rate-label'}" Margin="0 0 0 1" />
                        <FloatSpinBox Name="CVolumeRate" HorizontalExpand="True" />
                    </BoxContainer>
                </BoxContainer>
                <BoxContainer>
                    <CheckBox Name="CWideNet" Text="{Loc 'air-alarm-ui-scrubber-wide-net-label'}" />
                </BoxContainer>
                <BoxContainer Orientation="Horizontal" Margin ="0 0 0 2">
                    <Button Name="CCopySettings" Text="{Loc 'air-alarm-ui-widget-copy'}" ToolTip="{Loc 'air-alarm-ui-widget-copy-tooltip'}" />
                </BoxContainer>
                <!-- Lower row: every single gas -->
                <Collapsible Margin="2 2 2 2">
                    <CollapsibleHeading Title="Gas filters" />
                    <CollapsibleBody Margin="20 0 0 0">
                        <!-- Assmos changes start -->
                        <BoxContainer Orientation="Vertical">
                            <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 0 0 5">
                                <GridContainer HorizontalExpand="True" Columns="3" Margin="0 5 0 0">
                                    <Button Name="CSelectAllButton" HorizontalExpand="True" Text="{Loc 'Select All'}" />
                                    <Button Name="CDeselectAllButton" HorizontalExpand="True" Text="{Loc 'Deselect All'}" />
                                    <Control HorizontalExpand="True" />
                                </GridContainer>
                            </BoxContainer>
                            <GridContainer HorizontalExpand="True" Name="CGasContainer" Columns="3" Margin="0 5 0 0" /> <!-- Revert to just this line to undo changes -->
                        </BoxContainer>
                        <!-- Assmos changes end -->
                    </CollapsibleBody>
                </Collapsible>
            </BoxContainer>
        </CollapsibleBody>
    </Collapsible>
</BoxContainer>
