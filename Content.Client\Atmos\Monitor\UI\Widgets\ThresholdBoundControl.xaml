<!--
SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<BoxContainer xmlns="https://spacestation14.io"
    HorizontalExpand="True" Orientation="Vertical"
            Margin = "20 0 0 0" MinSize="160 0" >
    <Label Name="CBoundLabel" HorizontalAlignment="Center" />
    <CheckBox Name="CBoundEnabled" HorizontalAlignment="Center" Text="{Loc 'Enable'}" Pressed="True" />
    <FloatSpinBox Name="CSpinner" />
</BoxContainer>
