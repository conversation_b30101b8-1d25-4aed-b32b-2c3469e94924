<!--
SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flipp <PERSON>yder <<EMAIL>>
SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <6766154+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <eoin.mc<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical" Margin="0 0 0 4">
    <Collapsible>
        <CollapsibleHeading Name="CName" />
        <CollapsibleBody Margin="20 0 0 0">
            <BoxContainer Orientation="Vertical">
                <BoxContainer Orientation="Horizontal">
                    <CheckBox Name="CEnabled" Text="{Loc 'Enabled'}" />
                </BoxContainer>
                <!-- Upper row: Danger bounds -->
                <BoxContainer Name="CDangerBounds" Orientation="Horizontal" Margin="0 0 0 2"/>
                <!-- Lower row: Warning bounds -->
                <BoxContainer Name="CWarningBounds" Orientation="Horizontal" Margin="0 6 0 2"/>
            </BoxContainer>
        </CollapsibleBody>
    </Collapsible>
</BoxContainer>
