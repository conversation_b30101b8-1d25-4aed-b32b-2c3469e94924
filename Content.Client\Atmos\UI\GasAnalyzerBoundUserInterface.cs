// SPDX-FileCopyrightText: 2020 Exp <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Víctor Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 theashtronaut <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Kevin Zheng <<EMAIL>>
// SPDX-FileCopyrightText: 2025 metalgearsloth <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.GameObjects;
using Robust.Client.UserInterface;
using static Content.Shared.Atmos.Components.GasAnalyzerComponent;

namespace Content.Client.Atmos.UI
{
    public sealed class GasAnalyzerBoundUserInterface : BoundUserInterface
    {
        [ViewVariables]
        private GasAnalyzerWindow? _window;

        public GasAnalyzerBoundUserInterface(EntityUid owner, Enum uiKey) : base(owner, uiKey)
        {
        }

        protected override void Open()
        {
            base.Open();

            _window = this.CreateWindowCenteredLeft<GasAnalyzerWindow>();
        }

        protected override void ReceiveMessage(BoundUserInterfaceMessage message)
        {
            if (_window == null)
                return;
            if (message is not GasAnalyzerUserMessage cast)
                return;
            _window.Populate(cast);
        }

        /// <summary>
        /// Closes UI and tells the server to disable the analyzer
        /// </summary>
        private void OnClose()
        {
            SendMessage(new GasAnalyzerDisableMessage());
            Close();
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            if (disposing)
                _window?.Dispose();
        }
    }
}