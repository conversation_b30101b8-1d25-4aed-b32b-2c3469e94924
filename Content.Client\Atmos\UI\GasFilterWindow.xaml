<!--
SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Tom <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            MinSize="480 400" Title="Filter">
    <BoxContainer Orientation="Vertical" Margin="5 5 5 5" SeparationOverride="10">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-filter-ui-filter-status}"/>
            <Button Name="ToggleStatusButton"/>
        </BoxContainer>

        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-filter-ui-filter-transfer-rate}"/>
            <LineEdit Name="FilterTransferRateInput" MinSize="70 0" />
            <Button Name="SetFilterRate" Text="{Loc comp-gas-filter-ui-filter-set-rate}" Disabled="True"/>
        </BoxContainer>

        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Name="CurrentGasLabel" />
        </BoxContainer>

        <BoxContainer Orientation="Vertical" VerticalExpand="True">
            <Label Text="{Loc comp-gas-filter-ui-filter-gas-select}" />
            <ItemList Name="GasList" SelectMode="Single" VerticalExpand="True"
                      SizeFlagsStretchRatio="0.9" />
            <Button Name="SelectGasButton" Text="{Loc comp-gas-filter-ui-filter-gas-confirm}" HorizontalExpand="True" Disabled="True" />
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
