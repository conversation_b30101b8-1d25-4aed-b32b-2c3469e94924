<!--
SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
            SetSize="340 110" MinSize="340 110" Title="Pressure Pump">
    <BoxContainer Orientation="Vertical" Margin="5 5 5 5" SeparationOverride="10">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-pump-ui-pump-status}" Margin="0 0 5 0"/>
            <Button Name="ToggleStatusButton"/>
            <Control HorizontalExpand="True"/>
            <Button HorizontalAlignment="Right" Name="SetOutputPressureButton" Text="{Loc comp-gas-pump-ui-pump-set-rate}" Disabled="True" Margin="0 0 5 0"/>
            <Button Name="SetMaxPressureButton" Text="{Loc comp-gas-pump-ui-pump-set-max}" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-pump-ui-pump-output-pressure}"/>
            <FloatSpinBox HorizontalExpand="True" Name="PumpPressureOutputInput" MinSize="70 0" />
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
