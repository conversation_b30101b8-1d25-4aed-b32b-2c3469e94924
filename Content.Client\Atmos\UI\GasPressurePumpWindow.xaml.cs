// SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Atmos;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Atmos.UI
{
    /// <summary>
    /// Client-side UI used to control a gas pressure pump.
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class GasPressurePumpWindow : FancyWindow
    {
        public bool PumpStatus = true;

        public event Action? ToggleStatusButtonPressed;
        public event Action<float>? PumpOutputPressureChanged;

        public float MaxPressure
        {
            get => _maxPressure;
            set
            {
                _maxPressure = value;

                PumpPressureOutputInput.Value = MathF.Min(value, PumpPressureOutputInput.Value);
            }
        }

        private float _maxPressure = Atmospherics.MaxOutputPressure;

        public GasPressurePumpWindow()
        {
            RobustXamlLoader.Load(this);

            ToggleStatusButton.OnPressed += _ => SetPumpStatus(!PumpStatus);
            ToggleStatusButton.OnPressed += _ => ToggleStatusButtonPressed?.Invoke();

            PumpPressureOutputInput.OnValueChanged += _ => SetOutputPressureButton.Disabled = false;

            SetOutputPressureButton.OnPressed += _ =>
            {
                PumpPressureOutputInput.Value = Math.Clamp(PumpPressureOutputInput.Value, 0f, _maxPressure);
                PumpOutputPressureChanged?.Invoke(PumpPressureOutputInput.Value);
                SetOutputPressureButton.Disabled = true;
            };

            SetMaxPressureButton.OnPressed += _ =>
            {
                PumpPressureOutputInput.Value = _maxPressure;
                SetOutputPressureButton.Disabled = false;
            };
        }

        public void SetOutputPressure(float pressure)
        {
            PumpPressureOutputInput.Value = pressure;
        }

        public void SetPumpStatus(bool enabled)
        {
            PumpStatus = enabled;
            if (enabled)
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-enabled");
            }
            else
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-disabled");
            }
        }
    }
}