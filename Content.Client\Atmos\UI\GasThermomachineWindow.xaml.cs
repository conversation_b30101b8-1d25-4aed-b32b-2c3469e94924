// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON>shin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Atmos.UI;

[GenerateTypedNameReferences]
public sealed partial class GasThermomachineWindow : DefaultWindow
{
    public bool Active = true;

    public FloatSpinBox TemperatureSpinbox;

    public GasThermomachineWindow()
    {
        RobustXamlLoader.Load(this);

        SpinboxHBox.AddChild(
            TemperatureSpinbox = new FloatSpinBox(.1f, 2) { MinWidth = 150, HorizontalExpand = true }
            );
    }

    public void SetActive(bool active)
    {
        Active = active;
        if (active)
        {
            ToggleStatusButton.Text = Loc.GetString("comp-gas-thermomachine-ui-status-enabled");
            ToggleStatusButton.Pressed = true;
        }
        else
        {
            ToggleStatusButton.Text = Loc.GetString("comp-gas-thermomachine-ui-status-disabled");
            ToggleStatusButton.Pressed = false;
        }
    }

    public void SetTemperature(float temperature)
    {
        TemperatureSpinbox.Value = temperature;
    }
}