<!--
SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
            MinSize="200 120" Title="Volume Pump">
    <BoxContainer Orientation="Vertical" Margin="5 5 5 5" SeparationOverride="10">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-pump-ui-pump-status}"/>
            <Control MinSize="5 0" />
            <Button Name="ToggleStatusButton"/>
        </BoxContainer>

        <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
            <Label Text="{Loc comp-gas-pump-ui-pump-transfer-rate}"/>
            <Control MinSize="5 0" />
            <LineEdit Name="PumpTransferRateInput" MinSize="70 0" />
            <Control MinSize="5 0" />
            <Button Name="SetMaxRateButton" Text="{Loc comp-gas-pump-ui-pump-set-max}" />
            <Control MinSize="5 0" />
            <Control HorizontalExpand="True" />
            <Button Name="SetTransferRateButton" Text="{Loc comp-gas-pump-ui-pump-set-rate}" HorizontalAlignment="Right" Disabled="True"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
