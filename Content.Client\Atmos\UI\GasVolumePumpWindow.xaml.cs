// SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2021 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Globalization;
using Content.Client.Atmos.EntitySystems;
using Content.Client.UserInterface.Controls;
using Content.Shared.Atmos;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Atmos.UI
{
    /// <summary>
    /// Client-side UI used to control a gas volume pump.
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class GasVolumePumpWindow : FancyWindow
    {
        public bool PumpStatus = true;

        public event Action? ToggleStatusButtonPressed;
        public event Action<string>? PumpTransferRateChanged;

        public GasVolumePumpWindow()
        {
            RobustXamlLoader.Load(this);

            ToggleStatusButton.OnPressed += _ => SetPumpStatus(!PumpStatus);
            ToggleStatusButton.OnPressed += _ => ToggleStatusButtonPressed?.Invoke();

            PumpTransferRateInput.OnTextChanged += _ => SetTransferRateButton.Disabled = false;
            SetTransferRateButton.OnPressed += _ =>
            {
                PumpTransferRateChanged?.Invoke(PumpTransferRateInput.Text ??= "");
                SetTransferRateButton.Disabled = true;
            };

            SetMaxRateButton.OnPressed += _ =>
            {
                PumpTransferRateInput.Text = Atmospherics.MaxTransferRate.ToString(CultureInfo.CurrentCulture);
                SetTransferRateButton.Disabled = false;
            };
        }

        public void SetTransferRate(float rate)
        {
            PumpTransferRateInput.Text = rate.ToString(CultureInfo.CurrentCulture);
        }

        public void SetPumpStatus(bool enabled)
        {
            PumpStatus = enabled;
            if (enabled)
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-enabled");
            }
            else
            {
                ToggleStatusButton.Text = Loc.GetString("comp-gas-pump-ui-status-disabled");
            }
        }
    }
}