// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using System.Linq;
using Content.Client.UserInterface.Controls;
using Content.Shared.Bed.Cryostorage;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Collections;
using Robust.Shared.Utility;

namespace Content.Client.Bed.Cryostorage;

[GenerateTypedNameReferences]
public sealed partial class CryostorageMenu : FancyWindow
{
    public event Action<NetEntity, string>? SlotRemoveButtonPressed;
    public event Action<NetEntity, string>? HandRemoveButtonPressed;

    public CryostorageMenu()
    {
        RobustXamlLoader.Load(this);
    }

    public void UpdateState(CryostorageBuiState state)
    {
        var data = state.PlayerData;
        var nonexistentEntries = new ValueList<CryostorageContainedPlayerData>(data);

        var children = new ValueList<Control>(EntriesContainer.Children);
        foreach (var control in children)
        {
            if (control is not CryostorageEntryControl entryControl)
                continue;

            if (data.Where(p => p.PlayerEnt == entryControl.Entity).FirstOrNull() is not { } datum)
            {
                EntriesContainer.Children.Remove(entryControl);
                continue;
            }

            nonexistentEntries.Remove(datum);
            entryControl.Update(datum);
        }

        foreach (var player in nonexistentEntries)
        {
            var control = new CryostorageEntryControl(player);
            control.SlotRemoveButtonPressed += a => SlotRemoveButtonPressed?.Invoke(player.PlayerEnt, a);
            control.HandRemoveButtonPressed += a => HandRemoveButtonPressed?.Invoke(player.PlayerEnt, a);
            EntriesContainer.Children.Add(control);
        }

        EmptyLabel.Visible = data.Count == 0;
    }
}