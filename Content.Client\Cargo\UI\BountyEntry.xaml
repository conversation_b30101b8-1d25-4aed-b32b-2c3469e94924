<!--
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<BoxContainer xmlns="https://spacestation14.io"
              xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
              Margin="10 10 10 0"
              HorizontalExpand="True"
              Visible="True">
    <PanelContainer StyleClasses="AngleRect" HorizontalExpand="True">
        <BoxContainer Orientation="Vertical"
                      HorizontalExpand="True">
            <BoxContainer Orientation="Horizontal">
                <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                    <RichTextLabel Name="RewardLabel"/>
                    <RichTextLabel Name="ManifestLabel"/>
                </BoxContainer>
                <Control MinWidth="10"/>
                <BoxContainer Orientation="Vertical" MinWidth="120">
                    <BoxContainer Orientation="Horizontal" MinWidth="120">
                        <Button Name="PrintButton"
                                Text="{Loc 'bounty-console-label-button-text'}"
                                HorizontalExpand="False"
                                HorizontalAlignment="Right"
                                StyleClasses="OpenRight"/>
                        <Button Name="SkipButton"
                                Text="{Loc 'bounty-console-skip-button-text'}"
                                HorizontalExpand="False"
                                HorizontalAlignment="Right"
                                StyleClasses="OpenLeft"/>
                    </BoxContainer>
                    <RichTextLabel Name="IdLabel" HorizontalAlignment="Right" Margin="0 0 5 0"/>
                </BoxContainer>
            </BoxContainer>
            <customControls:HSeparator Margin="5 10 5 10"/>
            <BoxContainer>
                <RichTextLabel Name="DescriptionLabel"/>
            </BoxContainer>
        </BoxContainer>
    </PanelContainer>
</BoxContainer>
