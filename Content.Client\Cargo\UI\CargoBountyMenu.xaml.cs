// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 BarryNorfolk <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Cargo;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Cargo.UI;

[GenerateTypedNameReferences]
public sealed partial class CargoBountyMenu : FancyWindow
{
    public Action<string>? OnLabelButtonPressed;
    public Action<string>? OnSkipButtonPressed;

    public CargoBountyMenu()
    {
        RobustXamlLoader.Load(this);

        MasterTabContainer.SetTabTitle(0, Loc.GetString("bounty-console-tab-available-label"));
        MasterTabContainer.SetTabTitle(1, Loc.GetString("bounty-console-tab-history-label"));
    }

    public void UpdateEntries(List<CargoBountyData> bounties, List<CargoBountyHistoryData> history, TimeSpan untilNextSkip)
    {
        BountyEntriesContainer.Children.Clear();
        foreach (var b in bounties)
        {
            var entry = new BountyEntry(b, untilNextSkip);
            entry.OnLabelButtonPressed += () => OnLabelButtonPressed?.Invoke(b.Id);
            entry.OnSkipButtonPressed += () => OnSkipButtonPressed?.Invoke(b.Id);

            BountyEntriesContainer.AddChild(entry);
        }
        BountyEntriesContainer.AddChild(new Control
        {
            MinHeight = 10
        });

        BountyHistoryContainer.Children.Clear();
        if (history.Count == 0)
        {
            NoHistoryLabel.Visible = true;
        }
        else
        {
            NoHistoryLabel.Visible = false;

            // Show the history in reverse, so last entry is first in the list
            for (var i = history.Count - 1; i >= 0; i--)
            {
                BountyHistoryContainer.AddChild(new BountyHistoryEntry(history[i]));
            }
        }
    }
}