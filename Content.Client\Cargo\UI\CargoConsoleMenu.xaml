<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 EmoGarbage404 <<EMAIL>>
SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                           xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                           xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                           SetSize="600 600"
                           MinSize="600 600">
    <BoxContainer Orientation="Vertical" Margin="15 5 15 10">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'cargo-console-menu-account-name-label'}"
                   StyleClasses="LabelKeyText" />
            <RichTextLabel Name="AccountNameLabel"
                   Text="{Loc 'cargo-console-menu-account-name-none-text'}" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'cargo-console-menu-points-label'}"
                   StyleClasses="LabelKeyText" />
            <RichTextLabel Name="PointsLabel"
                   Text="$0" />
        </BoxContainer>
        <Control MinHeight="10"/>
        <TabContainer Name="TabContainer" VerticalExpand="True">
            <BoxContainer Orientation="Vertical" VerticalExpand="True">
                <BoxContainer Orientation="Horizontal">
                    <OptionButton Name="Categories"
                                  Prefix="{Loc 'cargo-console-menu-categories-label'}"
                                  HorizontalExpand="True" />
                    <LineEdit Name="SearchBar"
                              PlaceHolder="{Loc 'cargo-console-menu-search-bar-placeholder'}"
                              HorizontalExpand="True" />
                </BoxContainer>
                <Control MinHeight="5"/>
                <ScrollContainer HorizontalExpand="True"
                                 VerticalExpand="True"
                                 SizeFlagsStretchRatio="2">
                    <BoxContainer Name="Products"
                                  Orientation="Vertical"
                                  HorizontalExpand="True"
                                  VerticalExpand="True">
                        <!-- Products get added here by code -->
                    </BoxContainer>
                </ScrollContainer>
                <Control MinHeight="5" Name="OrdersSpacer"/>
                <PanelContainer VerticalExpand="True"
                                SizeFlagsStretchRatio="1"
                                Name="Orders">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#000000" />
                    </PanelContainer.PanelOverride>
                    <ScrollContainer VerticalExpand="True">
                        <BoxContainer Orientation="Vertical" Margin="5">
                            <Label Text="{Loc 'cargo-console-menu-requests-label'}" />
                            <BoxContainer Name="Requests"
                                          Orientation="Vertical"
                                          VerticalExpand="True">
                                <!-- Requests are added here by code -->
                            </BoxContainer>
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>
            <!-- Funds tab -->
            <BoxContainer Orientation="Vertical" Margin="15">
                <BoxContainer Orientation="Horizontal">
                    <RichTextLabel Name="TransferLimitLabel" Margin="0 0 15 0"/>
                    <RichTextLabel Name="UnlimitedNotifier" Text="{Loc 'cargo-console-menu-account-action-transfer-limit-unlimited-notifier'}"/>
                </BoxContainer>
                <BoxContainer Orientation="Horizontal">
                    <RichTextLabel Text="{Loc 'cargo-console-menu-account-action-select'}" Margin="0 0 10 0"/>
                    <OptionButton Name="ActionOptions"/>
                </BoxContainer>
                <Control MinHeight="5"/>
                <BoxContainer Orientation="Horizontal">
                    <RichTextLabel Name="AmountText" Text="{ Loc 'cargo-console-menu-account-action-amount'}"/>
                    <SpinBox Name="TransferSpinBox" MinWidth="100" Value="10"/>
                </BoxContainer>
                <Control MinHeight="15"/>
                <BoxContainer HorizontalAlignment="Center">
                    <Button Name="AccountActionButton" Text="{ Loc 'cargo-console-menu-account-action-button'}" MinHeight="45" MinWidth="120"/>
                </BoxContainer>
                <Control VerticalExpand="True"/>
                <BoxContainer VerticalAlignment="Bottom" HorizontalAlignment="Center">
                    <Button Name="AccountLimitToggleButton" Text="{ Loc 'cargo-console-menu-toggle-account-lock-button'}" MinHeight="45" MinWidth="120"/>
                </BoxContainer>
            </BoxContainer>
        </TabContainer>
    </BoxContainer>
</controls:FancyWindow>
