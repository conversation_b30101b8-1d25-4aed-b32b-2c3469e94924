<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<PanelContainer xmlns="https://spacestation14.io"
                HorizontalExpand="True"
                Margin="0 1">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True">
        <TextureRect Name="Icon"
                     Access="Public"
                     MinSize="32 32"
                     RectClipContent="True" />
        <Control MinWidth="5"/>
        <BoxContainer Orientation="Vertical"
                      HorizontalExpand="True"
                      VerticalExpand="True">
            <RichTextLabel Name="ProductName"
                   Access="Public"
                   HorizontalExpand="True"
                   StyleClasses="LabelSubText" />
            <Label Name="Description"
                   Access="Public"
                   HorizontalExpand="True"
                   StyleClasses="LabelSubText"
                   ClipText="True" />
        </BoxContainer>
        <Button Name="Approve"
                Access="Public"
                Text="{Loc 'cargo-console-menu-cargo-order-row-approve-button'}"
                StyleClasses="OpenRight" />
        <Button Name="Cancel"
                Access="Public"
                Text="{Loc 'cargo-console-menu-cargo-order-row-cancel-button'}"
                StyleClasses="OpenLeft" />
    </BoxContainer>
</PanelContainer>
