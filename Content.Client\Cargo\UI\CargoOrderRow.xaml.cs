// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Cargo;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Cargo.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class CargoOrderRow : PanelContainer
    {
        public CargoOrderData? Order { get; set; }

        public CargoOrderRow()
        {
            RobustXamlLoader.Load(this);
        }
    }
}