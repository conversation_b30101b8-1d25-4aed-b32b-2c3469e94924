<!--
SPDX-FileCopyrightText: 2023 Checkraze <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
            SetSize="300 150"
            MinSize="300 150"
                      Title="{Loc 'cargo-pallet-console-menu-title'}">
    <BoxContainer Orientation="Vertical" Margin="5">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'cargo-pallet-menu-appraisal-label'}"
                   StyleClasses="LabelKeyText" />
            <Label Name="AppraisalLabel"
                   Text="{Loc 'cargo-pallet-menu-no-goods-text'}" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'cargo-pallet-menu-count-label'}"
                   StyleClasses="LabelKeyText" />
            <Label Name="CountLabel"
                   Text="{Loc 'cargo-pallet-menu-no-goods-text'}" />
        </BoxContainer>
        <Button Name="AppraiseButton"
                Text="{Loc 'cargo-pallet-appraise-button'}"/>
        <Button Name="SellButton"
                Text="{Loc 'cargo-pallet-sell-button'}"/>
        <TextureButton VerticalExpand="True" />
    </BoxContainer>
</controls:FancyWindow>
