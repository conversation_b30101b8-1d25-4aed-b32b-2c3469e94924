<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2022 corentt <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<PanelContainer xmlns="https://spacestation14.io"
                HorizontalExpand="True">
    <Button Name="MainButton"
            ToolTip=""
            Access="Public"
            HorizontalExpand="True"
            VerticalExpand="True"
            StyleClasses="OpenBoth"/>
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True">
        <TextureRect Name="Icon"
                     Access="Public"
                     MinSize="32 32"
                     RectClipContent="True" />
        <Label Name="ProductName"
               Access="Public"
               HorizontalExpand="True" />
        <PanelContainer StyleClasses="BackgroundDark">
            <Label Name="PointCost"
                   Access="Public"
                   MinSize="52 32"
                   Align="Right"
                   Margin="0 0 5 0"/>
        </PanelContainer>
    </BoxContainer>
</PanelContainer>
