// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2023 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 pathetic meowmeow <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Cargo;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Cargo.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class CargoShuttleMenu : FancyWindow
    {
        public CargoShuttleMenu()
        {
            RobustXamlLoader.Load(this);
            Title = Loc.GetString("cargo-shuttle-console-menu-title");
        }

        public void SetAccountName(string name)
        {
            AccountNameLabel.Text = name;
        }

        public void SetShuttleName(string name)
        {
            ShuttleNameLabel.Text = name;
        }

        public void SetOrders(SpriteSystem sprites, IPrototypeManager protoManager, List<CargoOrderData> orders)
        {
            Orders.DisposeAllChildren();

            foreach (var order in orders)
            {
                 var product = protoManager.Index<EntityPrototype>(order.ProductId);
                 var productName = product.Name;
                 var account = protoManager.Index(order.Account);

                 var row = new CargoOrderRow
                 {
                     Order = order,
                     Icon = { Texture = sprites.Frame0(product) },
                     ProductName =
                     {
                         Text = Loc.GetString(
                             "cargo-console-menu-populate-orders-cargo-order-row-product-name-text",
                             ("productName", productName),
                             ("orderAmount", order.OrderQuantity - order.NumDispatched),
                             ("orderRequester", order.Requester),
                             ("accountColor", account.Color),
                             ("account", Loc.GetString(account.Code)))
                     },
                     Description = {Text = Loc.GetString("cargo-console-menu-order-reason-description",
                         ("reason", order.Reason))}
                 };

                 row.Approve.Visible = false;
                 row.Cancel.Visible = false;

                 Orders.AddChild(row);
            }
        }
    }
}