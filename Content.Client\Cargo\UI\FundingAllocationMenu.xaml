﻿<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      Title="{Loc 'cargo-funding-alloc-console-menu-title'}">
    <BoxContainer Orientation="Vertical"
                  VerticalExpand="True"
                  HorizontalExpand="True"
                  Margin="10 5 10 10">
        <controls:TableContainer Columns="2" HorizontalExpand="True" VerticalExpand="True">
            <RichTextLabel Name="PrimaryCutLabel" Text="{Loc 'cargo-funding-alloc-console-label-primary-cut'}"/>
            <SpinBox Name="PrimaryCut"/>
            <RichTextLabel Name="LockboxCutLabel" Text="{Loc 'cargo-funding-alloc-console-label-lockbox-cut'}"/>
            <SpinBox Name="LockboxCut"/>
        </controls:TableContainer>
        <Label Name="HelpLabel" HorizontalAlignment="Center" StyleClasses="LabelSubText" Margin="0 10"/>
        <PanelContainer VerticalExpand="True" HorizontalExpand="True" VerticalAlignment="Top" MaxHeight="250">
            <PanelContainer.PanelOverride>
                <graphics:StyleBoxFlat BackgroundColor="#1B1B1E"/>
            </PanelContainer.PanelOverride>
            <controls:TableContainer Name="EntriesContainer" Columns="4" HorizontalExpand="True" VerticalExpand="True" Margin="5 0">
                <RichTextLabel Text="{Loc 'cargo-funding-alloc-console-label-account'}" HorizontalAlignment="Center"/>
                <RichTextLabel Text="{Loc 'cargo-funding-alloc-console-label-code'}" HorizontalAlignment="Center"/>
                <RichTextLabel Text="{Loc 'cargo-funding-alloc-console-label-balance'}" HorizontalAlignment="Center"/>
                <RichTextLabel Text="{Loc 'cargo-funding-alloc-console-label-cut'}" HorizontalAlignment="Center"/>
            </controls:TableContainer>
        </PanelContainer>
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="5 0">
            <Button Name="SaveButton" Text="{Loc 'cargo-funding-alloc-console-button-save'}"  Disabled="True"/>
            <RichTextLabel Name="SaveAlertLabel" HorizontalExpand="True" HorizontalAlignment="Right" Visible="False"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
