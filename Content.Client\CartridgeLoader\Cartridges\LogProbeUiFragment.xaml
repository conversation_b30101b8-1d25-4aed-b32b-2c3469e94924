<!--
SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2024 Kot <<EMAIL>>
SPDX-FileCopyrightText: 2024 Milon <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

 <cartridges:LogProbeUiFragment xmlns="https://spacestation14.io"
                                xmlns:cartridges="clr-namespace:Content.Client.CartridgeLoader.Cartridges"
                                xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                                Orientation="Vertical"
                                VerticalExpand="True">
    <PanelContainer>
        <PanelContainer.PanelOverride>
            <gfx:StyleBoxFlat BackgroundColor="#000000FF"
                              BorderColor="#5a5a5a"
                              BorderThickness="0 0 0 1"/>
        </PanelContainer.PanelOverride>
        <BoxContainer Orientation="Vertical" Margin="4 8">
            <!-- DeltaV begin - Add title label -->
            <Label Name="TitleLabel"
                   Text="{Loc 'log-probe-header-access'}"
                   StyleClasses="LabelHeading"
                   HorizontalAlignment="Center"
                   Margin="0 0 0 8"/>
            <!-- DeltaV end -->

            <!-- DeltaV begin - Add card number display -->
            <Label Name="CardNumberLabel"
                   StyleClasses="LabelSubText"
                   HorizontalAlignment="Center"
                   Margin="0 0 0 8"
                   Visible="False"/>
            <!-- DeltaV end -->

            <!-- DeltaV begin - Adjust column headers -->
            <BoxContainer Orientation="Horizontal">
                <Label Align="Right" SetWidth="26" ClipText="True" Text="{Loc 'log-probe-label-number'}"/>
                <Label Align="Center" SetWidth="100" ClipText="True" Text="{Loc 'log-probe-label-time'}"/>
                <Label Name="ContentLabel" Align="Left" SetWidth="390" ClipText="True" Text="{Loc 'log-probe-label-accessor'}"/>
            </BoxContainer>
            <!-- DeltaV end -->
        </BoxContainer>
    </PanelContainer>
    <ScrollContainer VerticalExpand="True" HScrollEnabled="True">
        <BoxContainer Orientation="Vertical" Name="ProbedDeviceContainer"/>
    </ScrollContainer>
    <BoxContainer Orientation="Horizontal" Margin="4 8">
        <Button Name="PrintButton" HorizontalAlignment="Left" Text="{Loc 'log-probe-print-button'}" Disabled="True"/>
        <BoxContainer HorizontalExpand="True"/>
        <Label Name="EntityName" Align="Right"/>
    </BoxContainer>
</cartridges:LogProbeUiFragment>
