using System.Linq;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Client.UserInterface.Controls;
using Content.Shared.CartridgeLoader.Cartridges;

namespace Content.Client.CartridgeLoader.Cartridges;

/// <summary>
///     Popup displayed to edit a NanoTask item
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class NanoTaskItemPopup : DefaultWindow
{
    private readonly ButtonGroup _priorityGroup = new();
    private int? _editingTaskId = null;

    public Action<int, NanoTaskItem>? TaskSaved;
    public Action<int>? TaskDeleted;
    public Action<NanoTaskItem>? TaskCreated;
    public Action<NanoTaskItem>? TaskPrinted;

    private NanoTaskItem MakeItem()
    {
        return new(
            description: DescriptionInput.Text,
            taskIsFor: RequesterInput.Text,
            isTaskDone: false,
            priority: _priorityGroup.Pressed switch {
                var item when item == LowButton => NanoTaskPriority.Low,
                var item when item == MediumButton => NanoTaskPriority.Medium,
                var item when item == HighButton => NanoTaskPriority.High,
                _ => NanoTaskPriority.Medium,
            }
        );
    }

    public NanoTaskItemPopup()
    {
        RobustXamlLoader.Load(this);

        LowButton.Group = _priorityGroup;
        MediumButton.Group = _priorityGroup;
        HighButton.Group = _priorityGroup;

        CancelButton.OnPressed += _ => Close();
        DeleteButton.OnPressed += _ =>
        {
            if (_editingTaskId is int id)
            {
                TaskDeleted?.Invoke(id);
            }
        };
        PrintButton.OnPressed += _ =>
        {
            TaskPrinted?.Invoke(MakeItem());
        };
        SaveButton.OnPressed += _ =>
        {
            if (_editingTaskId is int id)
            {
                TaskSaved?.Invoke(id, MakeItem());
            }
            else
            {
                TaskCreated?.Invoke(MakeItem());
            }
        };

        DescriptionInput.OnTextChanged += args =>
        {
            if (args.Text.Length > NanoTaskItem.MaximumStringLength)
                DescriptionInput.Text = args.Text[..NanoTaskItem.MaximumStringLength];
        };
        RequesterInput.OnTextChanged += args =>
        {
            if (args.Text.Length > NanoTaskItem.MaximumStringLength)
                RequesterInput.Text = args.Text[..NanoTaskItem.MaximumStringLength];
        };
    }

    public void SetEditingTaskId(int? id)
    {
        _editingTaskId = id;
        DeleteButton.Visible = id is not null;
    }

    public void ResetInputs(NanoTaskItem? item)
    {
        if (item is NanoTaskItem task)
        {
            var button = task.Priority switch
            {
                NanoTaskPriority.High => HighButton,
                NanoTaskPriority.Medium => MediumButton,
                NanoTaskPriority.Low => LowButton,
                _ => throw new ArgumentException("Invalid priority"),
            };
            button.Pressed = true;
            DescriptionInput.Text = task.Description;
            RequesterInput.Text = task.TaskIsFor;
        }
        else
        {
            MediumButton.Pressed = true;
            DescriptionInput.Text = "";
            RequesterInput.Text = "";
        }
    }
}
