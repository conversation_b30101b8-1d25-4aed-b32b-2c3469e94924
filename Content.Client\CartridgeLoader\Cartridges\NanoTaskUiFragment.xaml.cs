using System.Linq;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Content.Shared.CartridgeLoader.Cartridges;

namespace Content.Client.CartridgeLoader.Cartridges;

/// <summary>
///     Class displaying the main UI of NanoTask
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class NanoTaskUiFragment : BoxContainer
{
    public Action<int>? OpenTask;
    public Action<int>? ToggleTaskCompletion;
    public Action? NewTask;
    public List<NanoTaskItemAndId> Tasks = new();

    public NanoTaskUiFragment()
    {
        RobustXamlLoader.Load(this);
        Orientation = LayoutOrientation.Vertical;
        HorizontalExpand = true;
        VerticalExpand = true;
        NewTaskButton.OnPressed += _ => NewTask?.Invoke();
    }
    public void UpdateState(List<NanoTaskItemAndId> tasks)
    {
        Tasks = tasks;
        HighContainer.RemoveAllChildren();
        MediumContainer.RemoveAllChildren();
        LowContainer.RemoveAllChildren();

        HighPriority.Text = Loc.GetString("nano-task-ui-heading-high-priority-tasks", ("amount", tasks.Count(task => task.Data.Priority == NanoTaskPriority.High)));
        MediumPriority.Text = Loc.GetString("nano-task-ui-heading-medium-priority-tasks", ("amount", tasks.Count(task => task.Data.Priority == NanoTaskPriority.Medium)));
        LowPriority.Text = Loc.GetString("nano-task-ui-heading-low-priority-tasks", ("amount", tasks.Count(task => task.Data.Priority == NanoTaskPriority.Low)));

        foreach (var task in tasks)
        {
            var container = task.Data.Priority switch
            {
                NanoTaskPriority.High => HighContainer,
                NanoTaskPriority.Medium => MediumContainer,
                NanoTaskPriority.Low => LowContainer,
                _ => throw new ArgumentException("Invalid priority"),
            };
            var control = new NanoTaskItemControl(task);
            container.AddChild(control);
            control.OnMainPressed += id => OpenTask?.Invoke(id);
            control.OnDonePressed += id => ToggleTaskCompletion?.Invoke(id);
        }
    }
}
