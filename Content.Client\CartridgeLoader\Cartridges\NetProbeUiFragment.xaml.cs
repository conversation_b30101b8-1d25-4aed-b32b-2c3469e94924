// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NetProbeUiFragment : BoxContainer
{
    private readonly StyleBoxFlat _styleBox = new()
    {
        BackgroundColor = Color.Transparent,
        BorderColor = Color.FromHex("#5a5a5a"),
        BorderThickness = new Thickness(0, 0, 0, 1)
    };

    public NetProbeUiFragment()
    {
        RobustXamlLoader.Load(this);
        Orientation = LayoutOrientation.Vertical;
        HorizontalExpand = true;
        VerticalExpand = true;
        HeaderPanel.PanelOverride = _styleBox;
    }

    public void UpdateState(List<ProbedNetworkDevice> devices)
    {
        ProbedDeviceContainer.RemoveAllChildren();

        //Reverse the list so the oldest entries appear at the bottom
        devices.Reverse();

        //Enable scrolling if there are more entries that can fit on the screen
        ScrollContainer.HScrollEnabled = devices.Count > 9;

        foreach (var device in devices)
        {
            AddProbedDevice(device);
        }
    }

    private void AddProbedDevice(ProbedNetworkDevice device)
    {
        var row = new BoxContainer();
        row.HorizontalExpand = true;
        row.Orientation = LayoutOrientation.Horizontal;
        row.Margin = new Thickness(4);

        var nameLabel = new Label();
        nameLabel.Text = device.Name;
        nameLabel.HorizontalExpand = true;
        nameLabel.ClipText = true;
        row.AddChild(nameLabel);

        var addressLabel = new Label();
        addressLabel.Text = device.Address;
        addressLabel.HorizontalExpand = true;
        addressLabel.ClipText = true;
        row.AddChild(addressLabel);

        var frequencyLabel = new Label();
        frequencyLabel.Text = device.Frequency;
        frequencyLabel.HorizontalExpand = true;
        frequencyLabel.ClipText = true;
        row.AddChild(frequencyLabel);

        var networkLabel = new Label();
        networkLabel.Text = device.NetId;
        networkLabel.HorizontalExpand = true;
        networkLabel.ClipText = true;
        row.AddChild(networkLabel);

        ProbedDeviceContainer.AddChild(row);
    }
}