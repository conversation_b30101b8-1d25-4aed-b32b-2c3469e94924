<!--
SPDX-FileCopyrightText: 2023 MishaUnity <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<cartridges:NewsReaderUiFragment xmlns:cartridges="clr-namespace:Content.Client.CartridgeLoader.Cartridges"
                               xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                               xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                               xmlns="https://spacestation14.io"
                               Margin="1 0 2 0"
                               Orientation="Vertical"
                               HorizontalExpand="True"
                               VerticalExpand="True">
    <PanelContainer StyleClasses="BackgroundDark"></PanelContainer>
    <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="5,5,5,5">
        <Button
            Name="Prev"
            MinWidth="64"
            Disabled="True"
            HorizontalAlignment="Left"
            Text="{Loc 'news-read-ui-prev-text'}"
            ToolTip="{Loc 'news-read-ui-prev-tooltip'}"
            Access="Public"
            HorizontalExpand="True"/>
        <Button
            Name="Next"
            MinWidth="64"
            Disabled="True"
            HorizontalAlignment="Right"
            Text="{Loc 'news-read-ui-next-text'}"
            ToolTip="{Loc 'news-read-ui-next-tooltip'}"/>
    </BoxContainer>
    <controls:StripeBack Name="ArticleNameContainer">
        <PanelContainer>
            <Label Name="PageNum" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="4,0,0,0"/>
            <Label Name="PageName" Align="Center"/>
        </PanelContainer>
    </controls:StripeBack>
    <PanelContainer VerticalExpand="True">
        <PanelContainer.PanelOverride>
            <gfx:StyleBoxFlat BackgroundColor="#80808005" />
        </PanelContainer.PanelOverride>
        <ScrollContainer
                Name="PageTextScroll"
                HScrollEnabled="False"
                HorizontalExpand="True"
                MinSize="0 0"
                SizeFlagsStretchRatio="2"
                VerticalExpand="True">
            <BoxContainer
                Name="PageTextContainer"
                MinSize="0 0"
                Orientation="Vertical"
                SizeFlagsStretchRatio="2"
                VerticalExpand="True">
            </BoxContainer>
            <RichTextLabel Margin="8,8,8,8" Name="PageText" VerticalAlignment="Top"/>
        </ScrollContainer>
    </PanelContainer>
    <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="5,5,5,5">
        <Button
            Name="NotificationSwitch"
            ToolTip="{Loc news-reader-ui-mute-tooltip}"
            MinWidth="20"/>
        <RichTextLabel Margin="5,2,2,2" Name="ShareTime" VerticalAlignment="Top"/>
        <customControls:VSeparator Margin="2 0"/>
        <RichTextLabel Margin="5,2,2,2" Name="Author" VerticalAlignment="Top" HorizontalAlignment="Right"/>
    </BoxContainer>
</cartridges:NewsReaderUiFragment>
