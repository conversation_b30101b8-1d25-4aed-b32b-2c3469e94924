<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 MishaUnity <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<cartridges:NotekeeperUiFragment xmlns:cartridges="clr-namespace:Content.Client.CartridgeLoader.Cartridges"
                                 xmlns="https://spacestation14.io" Margin="1 0 2 0">
    <PanelContainer StyleClasses="BackgroundDark"></PanelContainer>
    <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
        <ScrollContainer HorizontalExpand="True" VerticalExpand="True" HScrollEnabled="True">
            <BoxContainer Orientation="Vertical" Name="MessageContainer" HorizontalExpand="True" VerticalExpand="True"/>
        </ScrollContainer>
        <LineEdit Name="Input" HorizontalExpand="True" SetHeight="32"/>
    </BoxContainer>
</cartridges:NotekeeperUiFragment>
