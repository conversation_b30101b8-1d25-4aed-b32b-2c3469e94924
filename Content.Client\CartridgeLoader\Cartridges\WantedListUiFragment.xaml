<!--
SPDX-FileCopyrightText: 2024 Эдуард <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<cartridges:WantedListUiFragment xmlns:cartridges="clr-namespace:Content.Client.CartridgeLoader.Cartridges"
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Orientation="Vertical"
        VerticalExpand="True"
        HorizontalExpand="True">

    <LineEdit Name="SearchBar" PlaceHolder="{Loc 'wanted-list-search-placeholder'}"/>

    <BoxContainer Name="MainContainer" Orientation="Horizontal" HorizontalExpand="True" VerticalExpand="True">
        <Label Name="NoRecords" Text="{Loc 'wanted-list-label-no-records'}" Align="Center" VAlign="Center" HorizontalExpand="True" FontColorOverride="DarkGray"/>

        <!-- Any attempts to set dimensions for ListContainer breaks the renderer, I have to roughly set sizes and margins in other controllers. -->
        <controls:ListContainer
            Name="RecordsList"
            HorizontalAlignment="Left"
            VerticalExpand="True"
            Visible="False"
            Toggle="True"
            Group="True"
            SetWidth="192" />

        <Label Name="RecordUnselected"
               Text="{Loc 'criminal-records-console-select-record-info'}"
               Align="Center"
               FontColorOverride="DarkGray"
               Visible="False"
               HorizontalExpand="True" />
        <BoxContainer Name="PersonContainer" Orientation="Vertical" HorizontalExpand="True" SetWidth="334" Margin="5 0 77 0">
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                <Label Name="PersonName" StyleClasses="LabelBig" />
                <RichTextLabel Name="PersonState" HorizontalAlignment="Right" HorizontalExpand="True" />
            </BoxContainer>
            <PanelContainer StyleClasses="LowDivider" Margin="0 5 0 5"/>
            <ScrollContainer VerticalExpand="True" HScrollEnabled="False">
                <BoxContainer Name="DataContainer" Orientation="Vertical">
                    <RichTextLabel Name="TargetAge" />
                    <RichTextLabel Name="TargetJob" />
                    <RichTextLabel Name="TargetSpecies" />
                    <RichTextLabel Name="TargetGender" />
                    <PanelContainer StyleClasses="LowDivider" Margin="0 5 0 5"/>
                    <RichTextLabel Name="InitiatorName" VerticalAlignment="Stretch"/>
                    <RichTextLabel Name="WantedReason" VerticalAlignment="Stretch"/>
                    <PanelContainer StyleClasses="LowDivider" Margin="0 5 0 5" />
                    <controls:TableContainer Name="HistoryTable" Columns="3" Visible="False" HorizontalAlignment="Stretch" />
                </BoxContainer>
            </ScrollContainer>
        </BoxContainer>
    </BoxContainer>
</cartridges:WantedListUiFragment>
