<!--
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 y7o4ka <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<ui:ChangelogWindow xmlns="https://spacestation14.io"
                    xmlns:ui="clr-namespace:Content.Client.Changelog"
                    Title="{Loc 'changelog-window-title'}"
                    MinSize="500 400"
                    SetSize="500 400">
    <PanelContainer StyleClasses="AngleRect" />
    <BoxContainer Orientation="Vertical">
        <TabContainer Name="Tabs" Access="Public" HorizontalExpand="True" VerticalExpand="True" />
        <PanelContainer StyleClasses="LowDivider" />
        <Label Name="VersionLabel" Access="Public" HorizontalAlignment="Right" StyleClasses="LabelSubText" Margin="4 0" />
    </BoxContainer>
</ui:ChangelogWindow>
