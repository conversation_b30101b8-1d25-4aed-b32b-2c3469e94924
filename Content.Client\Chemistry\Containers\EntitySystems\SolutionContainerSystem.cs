// SPDX-FileCopyrightText: 2023 ElectroJr <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Chemistry.EntitySystems;

namespace Content.Client.Chemistry.Containers.EntitySystems;

public sealed partial class SolutionContainerSystem : SharedSolutionContainerSystem
{
}