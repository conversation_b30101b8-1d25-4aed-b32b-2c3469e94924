<!--
SPDX-FileCopyrightText: 2024 Brandon Li <48413902+<PERSON><PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io" HorizontalExpand="True">
    <BoxContainer Name="MainContainer"
                  Orientation="Horizontal"
                  HorizontalExpand="True">
        <PanelContainer Name="ColorPanel"
                        VerticalExpand="True"
                        SetWidth="7"
                        Margin="0 1 0 0" />
        <Button Name="MainButton"
                HorizontalExpand="True"
                VerticalExpand="True"
                StyleClasses="ButtonSquare"
                Margin="-1 0 0 0">
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                <BoxContainer Orientation="Vertical"
                              VerticalExpand="True"
                              HorizontalExpand="True"
                              Margin="-5 0 0 0">
                    <Label Name="ReagentNameLabel" />
                    <Label Name="FillLabel"
                           StyleClasses="LabelSubText"
                           Margin="0 -5 0 0" />
                </BoxContainer>
            </BoxContainer>
        </Button>
        <Button Name="EjectButton"
                StyleClasses="OpenLeft"
                VerticalExpand="True"
                SetWidth="20">
            <Label Name="EjectButtonIcon"
                   VerticalAlignment="Center"
                   HorizontalAlignment="Center"
                   Margin="-7 -4 0 0" />
        </Button>
    </BoxContainer>
</Control>
