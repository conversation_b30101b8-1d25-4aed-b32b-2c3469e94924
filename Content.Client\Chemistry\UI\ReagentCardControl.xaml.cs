// SPDX-FileCopyrightText: 2024 <PERSON> <48413902+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Chemistry;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Chemistry.UI;

[GenerateTypedNameReferences]
public sealed partial class ReagentCardControl : Control
{
    public string StorageSlotId { get; }
    public Action<string>? OnPressed;
    public Action<string>? OnEjectButtonPressed;

    public ReagentCardControl(ReagentInventoryItem item)
    {
        RobustXamlLoader.Load(this);

        StorageSlotId = item.StorageSlotId;
        ColorPanel.PanelOverride = new StyleBoxFlat { BackgroundColor = item.ReagentColor };
        ReagentNameLabel.Text = item.ReagentLabel;
        FillLabel.Text = Loc.GetString("reagent-dispenser-window-quantity-label-text", ("quantity", item.Quantity));;
        EjectButtonIcon.Text = Loc.GetString("reagent-dispenser-window-eject-container-button");

        if (item.Quantity == 0.0)
            MainButton.Disabled = true;

        MainButton.OnPressed += args => OnPressed?.Invoke(StorageSlotId);
        EjectButton.OnPressed += args => OnEjectButtonPressed?.Invoke(StorageSlotId);
    }
}