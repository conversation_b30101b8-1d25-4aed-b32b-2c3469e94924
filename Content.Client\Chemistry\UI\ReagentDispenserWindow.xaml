<!--
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Ygg01 <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Itzbenz <<EMAIL>>
SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 GitHubUser53123 <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Tayrtahn <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                      xmlns:ui="clr-namespace:Content.Client.Chemistry.UI"
                      Title="{Loc 'reagent-dispenser-bound-user-interface-title'}"
                      MinSize="600 300"
                      SetSize="800 500">
    <BoxContainer Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" MinWidth="170">
            <Label Text="{Loc 'reagent-dispenser-window-amount-to-dispense-label'}" HorizontalAlignment="Center" />
            <ui:ButtonGrid
                Name="AmountGrid"
                Access="Public"
                Columns="3"
                HorizontalAlignment="Center"
                Margin="5"
                ButtonList="1,5,10,15,20,25,30,50,100"
                RadioGroup="True">
            </ui:ButtonGrid>
            <Control VerticalExpand="True" />
            <Label Name="ContainerInfoName"
                   Access="Public"
                   Text=""
                   HorizontalAlignment="Center" />
            <Label Name="ContainerInfoFill"
                   Access="Public"
                   Text=""
                   HorizontalAlignment="Center"
                   StyleClasses="LabelSubText" />
            <SpriteView Name="View"
                        Scale="4 4"
                        HorizontalAlignment="Center" />
            <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="ClearButton"
                        Access="Public"
                        Text="{Loc 'reagent-dispenser-window-clear-button'}"
                        StyleClasses="OpenRight" />
                <Button Name="EjectButton"
                        Access="Public"
                        Text="{Loc 'reagent-dispenser-window-eject-button'}"
                        StyleClasses="OpenLeft" />
            </BoxContainer>
        </BoxContainer>
        <SplitContainer Orientation="Vertical"
                        HorizontalExpand="True"
                        VerticalExpand="True">
            <ScrollContainer HScrollEnabled="False"
                             HorizontalExpand="True"
                             VerticalExpand="True"
                             MinHeight="50"
                             SizeFlagsStretchRatio="2.5">
                <GridContainer Name="ReagentList"
                               HorizontalExpand="True"
                               VerticalExpand="True"
                               Access="Public"
                               Columns="3" />
            </ScrollContainer>
            <ScrollContainer HScrollEnabled="False"
                             HorizontalExpand="True"
                             VerticalExpand="True"
                             MinHeight="50">
                <PanelContainer VerticalExpand="True">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1b1b1e" />
                    </PanelContainer.PanelOverride>
                    <BoxContainer Name="ContainerInfo"
                                  Orientation="Vertical"
                                  HorizontalExpand="True"
                                  VerticalExpand="True"
                                  Margin="5">
                        <Label Text="{Loc 'reagent-dispenser-window-no-container-loaded-text'}" />
                    </BoxContainer>
                </PanelContainer>
            </ScrollContainer>
        </SplitContainer>
    </BoxContainer>
</controls:FancyWindow>
