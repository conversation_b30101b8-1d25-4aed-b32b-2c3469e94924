<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
               Title="{Loc 'chameleon-component-ui-window-name'}"
               MinSize="250 300"
               SetSize="250 300">
    <BoxContainer Orientation="Vertical">
        <LineEdit Name="Search" PlaceHolder = "{Loc 'chameleon-component-ui-search-placeholder'}"/>
        <ScrollContainer VerticalExpand="True">
            <GridContainer Name="Grid" Columns="3" Margin="0 5" >
            </GridContainer>
        </ScrollContainer>
    </BoxContainer>
</DefaultWindow>
