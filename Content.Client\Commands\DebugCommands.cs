// SPDX-FileCopyrightText: 2019 Silver <<EMAIL>>
// SPDX-FileCopyrightText: 2020 <PERSON>-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2020 <PERSON>-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2020 chairbender <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Metal Gear Sloth <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PrPleGoo <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX-7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SpaceManiac <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Client.Markers;
using Content.Client.Popups;
using Content.Client.SubFloor;
using Content.Shared.SubFloor;
using Robust.Client.GameObjects;
using Robust.Shared.Console;
using DrawDepth = Content.Shared.DrawDepth.DrawDepth;

namespace Content.Client.Commands;

internal sealed class ShowMarkersCommand : LocalizedCommands
{
    [Dependency] private readonly IEntitySystemManager _entitySystemManager = default!;

    public override string Command => "showmarkers";

    public override string Help => LocalizationManager.GetString($"cmd-{Command}-help", ("command", Command));

    public override void Execute(IConsoleShell shell, string argStr, string[] args)
    {
        _entitySystemManager.GetEntitySystem<MarkerSystem>().MarkersVisible ^= true;
    }
}

internal sealed class ShowSubFloor : LocalizedCommands
{
    [Dependency] private readonly IEntitySystemManager _entitySystemManager = default!;

    public override string Command => "showsubfloor";

    public override string Help => LocalizationManager.GetString($"cmd-{Command}-help", ("command", Command));

    public override void Execute(IConsoleShell shell, string argStr, string[] args)
    {
        _entitySystemManager.GetEntitySystem<SubFloorHideSystem>().ShowAll ^= true;
    }
}

internal sealed class NotifyCommand : LocalizedCommands
{
    [Dependency] private readonly IEntitySystemManager _entitySystemManager = default!;

    public override string Command => "notify";

    public override string Help => LocalizationManager.GetString($"cmd-{Command}-help", ("command", Command));

    public override void Execute(IConsoleShell shell, string argStr, string[] args)
    {
        var message = args[0];

        _entitySystemManager.GetEntitySystem<PopupSystem>().PopupCursor(message);
    }
}