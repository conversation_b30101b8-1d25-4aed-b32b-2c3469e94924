// SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Communications;

namespace Content.Client.Communications;

/// <summary>
/// Does nothing special, only exists to provide a client implementation.
/// </summary>
public sealed class CommsHackerSystem : SharedCommsHackerSystem
{
}