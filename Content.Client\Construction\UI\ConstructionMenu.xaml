<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls">
    <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
        <BoxContainer Orientation="Vertical" MinWidth="243" Margin="0 0 5 0">
            <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 0 0 5">
                <LineEdit Name="SearchBar" PlaceHolder="{Loc 'construction-menu-search'}" HorizontalExpand="True"/>
                <OptionButton Name="OptionCategories" Access="Public" MinSize="130 0"/>
            </BoxContainer>
            <controls:ListContainer Name="Recipes" Access="Public" Group="True" Toggle="True" VerticalExpand="True" />
            <ScrollContainer Name="RecipesGridScrollContainer" VerticalExpand="True" Access="Public" Visible="False">
                <GridContainer Name="RecipesGrid" Columns="5" Access="Public"/>
            </ScrollContainer>
        </BoxContainer>
        <BoxContainer Orientation="Vertical" HorizontalExpand="True">
            <BoxContainer Orientation="Horizontal">
                <Button Name="MenuGridViewButton" ToggleMode="True" Text="{Loc construction-menu-grid-view}"/>
                <Button Name="FavoriteButton" Visible="false"/>
            </BoxContainer>
            <Control>
                <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="0 0 0 5">
                    <BoxContainer Orientation="Horizontal" Align="Center">
                        <EntityPrototypeView Name="TargetTexture" HorizontalAlignment="Right" Stretch="Fill" Margin="0 0 10 0"/>
                        <BoxContainer Orientation="Vertical">
                            <RichTextLabel Name="TargetName"/>
                            <RichTextLabel Name="TargetDesc"/>
                        </BoxContainer>
                    </BoxContainer>
                </BoxContainer>
            </Control>
            <ItemList Name="RecipeStepList" Access="Public" VerticalExpand="True" Margin="0 0 0 5"/>
            <BoxContainer Orientation="Vertical">
                <Button Name="BuildButton" Disabled="True" ToggleMode="True"
                        VerticalExpand="True" SizeFlagsStretchRatio="0.5"/>
                <BoxContainer Orientation="Horizontal" VerticalExpand="True" SizeFlagsStretchRatio="0.5">
                    <Button Name="EraseButton" ToggleMode="True"
                        HorizontalExpand="True" SizeFlagsStretchRatio="0.7"/>
                    <Button Name="ClearButton" HorizontalExpand="True" SizeFlagsStretchRatio="0.3"/>
                </BoxContainer>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
