// SPDX-FileCopyrightText: 2019 Ephememory <<EMAIL>>
// SPDX-FileCopyrightText: 2019 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2019 Silver <<EMAIL>>
// SPDX-FileCopyrightText: 2019 ZelteHonor <<EMAIL>>
// SPDX-FileCopyrightText: 2019 moneyl <<EMAIL>>
// SPDX-FileCopyrightText: 2020 AJCM-git <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Clyybber <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Exp <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Git-Nivrak <<EMAIL>>
// SPDX-FileCopyrightText: 2020 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Víctor <PERSON>a Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2020 zumorica <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Galactic Chimp <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Paul Ritter <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Artjom <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 qwerltaz <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Ertanic <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.UserInterface.Controls;
using Content.Shared.Construction.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Construction.UI
{
    /// <summary>
    /// This is the interface for a UI View of the construction window. The point of it is to abstract away the actual
    /// UI controls and just provide higher level operations on the entire window. This View is completely passive and
    /// just raises events to the outside world. This class is controlled by the <see cref="ConstructionMenuPresenter"/>.
    /// </summary>
    public interface IConstructionMenuView : IDisposable
    {
        // It isn't optimal to expose UI controls like this, but the UI control design is
        // questionable so it can't be helped.
        string[] Categories { get; set; }
        OptionButton OptionCategories { get; }

        bool EraseButtonPressed { get; set; }
        bool GridViewButtonPressed { get; set; }
        bool BuildButtonPressed { get; set; }

        ListContainer Recipes { get; }
        ItemList RecipeStepList { get; }


        ScrollContainer RecipesGridScrollContainer { get; }
        GridContainer RecipesGrid { get; }

        event EventHandler<(string search, string catagory)> PopulateRecipes;
        event EventHandler<ConstructionMenu.ConstructionMenuListData?> RecipeSelected;
        event EventHandler RecipeFavorited;
        event EventHandler<bool> BuildButtonToggled;
        event EventHandler<bool> EraseButtonToggled;
        event EventHandler ClearAllGhosts;

        void ClearRecipeInfo();
        void SetRecipeInfo(string name, string description, EntityPrototype? targetPrototype, bool isItem, bool isFavorite);
        void ResetPlacement();

        #region Window Control

        event Action? OnClose;

        bool IsOpen { get; }

        void OpenCentered();
        void MoveToFront();
        bool IsAtFront();
        void Close();

        #endregion
    }

    [GenerateTypedNameReferences]
    public sealed partial class ConstructionMenu : DefaultWindow, IConstructionMenuView
    {
        public bool BuildButtonPressed
        {
            get => BuildButton.Pressed;
            set => BuildButton.Pressed = value;
        }

        public string[] Categories { get; set; } = Array.Empty<string>();

        public bool EraseButtonPressed
        {
            get => EraseButton.Pressed;
            set => EraseButton.Pressed = value;
        }

        public bool GridViewButtonPressed
        {
            get => MenuGridViewButton.Pressed;
            set => MenuGridViewButton.Pressed = value;
        }

        public ConstructionMenu()
        {
            SetSize = new Vector2(560, 450);
            MinSize = new Vector2(560, 320);

            IoCManager.InjectDependencies(this);
            RobustXamlLoader.Load(this);

            Title = Loc.GetString("construction-menu-title");

            BuildButton.Text = Loc.GetString("construction-menu-place-ghost");
            Recipes.ItemPressed += (_, data) => RecipeSelected?.Invoke(this, data as ConstructionMenuListData);
            Recipes.NoItemSelected += () => RecipeSelected?.Invoke(this, null);
            Recipes.GenerateItem += (data, button) =>
            {
                if (data is not ConstructionMenuListData (var prototype, var targetPrototype))
                    return;

                var entProtoView = new EntityPrototypeView()
                {
                    SetSize = new(32f),
                    Stretch = SpriteView.StretchMode.Fill,
                    Scale = new(2),
                    Margin = new(0, 2),
                };
                entProtoView.SetPrototype(targetPrototype);

                var label = new Label()
                {
                    Text = prototype.Name,
                    Margin = new(5, 0),
                };

                var box = new BoxContainer();
                box.AddChild(entProtoView);
                box.AddChild(label);

                button.AddChild(box);
                button.ToolTip = prototype.Description;
                button.AddStyleClass(ListContainer.StyleClassListContainerButton);
            };

            SearchBar.OnTextChanged += _ =>
                PopulateRecipes?.Invoke(this, (SearchBar.Text, Categories[OptionCategories.SelectedId]));
            OptionCategories.OnItemSelected += obj =>
            {
                OptionCategories.SelectId(obj.Id);
                SearchBar.SetText(string.Empty);
                PopulateRecipes?.Invoke(this, (SearchBar.Text, Categories[obj.Id]));
            };

            BuildButton.Text = Loc.GetString("construction-menu-place-ghost");
            BuildButton.OnToggled += args => BuildButtonToggled?.Invoke(this, args.Pressed);
            ClearButton.Text = Loc.GetString("construction-menu-clear-all");
            ClearButton.OnPressed += _ => ClearAllGhosts?.Invoke(this, EventArgs.Empty);
            EraseButton.Text = Loc.GetString("construction-menu-eraser-mode");
            EraseButton.OnToggled += args => EraseButtonToggled?.Invoke(this, args.Pressed);

            FavoriteButton.OnPressed += args => RecipeFavorited?.Invoke(this, EventArgs.Empty);

            MenuGridViewButton.OnPressed += _ =>
                PopulateRecipes?.Invoke(this, (SearchBar.Text, Categories[OptionCategories.SelectedId]));
        }

        public event EventHandler? ClearAllGhosts;
        public event EventHandler<(string search, string catagory)>? PopulateRecipes;
        public event EventHandler<ConstructionMenuListData?>? RecipeSelected;
        public event EventHandler? RecipeFavorited;
        public event EventHandler<bool>? BuildButtonToggled;
        public event EventHandler<bool>? EraseButtonToggled;

        public void ResetPlacement()
        {
            BuildButton.Pressed = false;
            EraseButton.Pressed = false;
        }

        public void SetRecipeInfo(
            string name,
            string description,
            EntityPrototype? targetPrototype,
            bool isItem,
            bool isFavorite)
        {
            BuildButton.Disabled = false;
            BuildButton.Text = Loc.GetString(isItem ? "construction-menu-place-ghost" : "construction-menu-craft");
            TargetName.SetMessage(name);
            TargetDesc.SetMessage(description);
            TargetTexture.SetPrototype(targetPrototype?.ID);
            FavoriteButton.Visible = true;
            FavoriteButton.Text = Loc.GetString(
                            isFavorite ? "construction-add-favorite-button" : "construction-remove-from-favorite-button");
        }

        public void ClearRecipeInfo()
        {
            BuildButton.Disabled = true;
            TargetName.SetMessage(string.Empty);
            TargetDesc.SetMessage(string.Empty);
            TargetTexture.SetPrototype(null);
            FavoriteButton.Visible = false;
            RecipeStepList.Clear();
        }

        public sealed record ConstructionMenuListData(ConstructionPrototype Prototype, EntityPrototype TargetPrototype) : ListData;
    }
}