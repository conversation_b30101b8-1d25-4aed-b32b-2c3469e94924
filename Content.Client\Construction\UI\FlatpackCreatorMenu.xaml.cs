// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SpeltIncorrectyl <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Client.Materials;
using Content.Client.Materials.UI;
using Content.Client.Message;
using Content.Client.UserInterface.Controls;
using Content.Shared.Construction.Components;
using Content.Shared.Containers.ItemSlots;
using Content.Shared.Materials;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;
using Robust.Shared.Utility;

namespace Content.Client.Construction.UI;

[GenerateTypedNameReferences]
public sealed partial class FlatpackCreatorMenu : FancyWindow
{
    [Dependency] private readonly IEntityManager _entityManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;

    private readonly ItemSlotsSystem _itemSlots;
    private readonly FlatpackSystem _flatpack;
    private readonly MaterialStorageSystem _materialStorage;

    private EntityUid _owner;

    [ValidatePrototypeId<EntityPrototype>]
    public const string NoBoardEffectId = "FlatpackerNoBoardEffect";

    private EntityUid? _currentBoard = EntityUid.Invalid;

    public event Action? PackButtonPressed;

    public FlatpackCreatorMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _itemSlots = _entityManager.System<ItemSlotsSystem>();
        _flatpack = _entityManager.System<FlatpackSystem>();
        _materialStorage = _entityManager.System<MaterialStorageSystem>();

        PackButton.OnPressed += _ => PackButtonPressed?.Invoke();

        InsertLabel.SetMarkup(Loc.GetString("flatpacker-ui-insert-board"));
    }

    public void SetEntity(EntityUid uid)
    {
        _owner = uid;
        MaterialStorageControl.SetOwner(uid);
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (!_entityManager.TryGetComponent<FlatpackCreatorComponent>(_owner, out var flatpacker) ||
            !_itemSlots.TryGetSlot(_owner, flatpacker.SlotId, out var itemSlot))
            return;

        if (flatpacker.Packing)
        {
            PackButton.Disabled = true;
        }
        else if (_currentBoard != null)
        {
            Dictionary<string, int> cost;
            if (_entityManager.TryGetComponent<MachineBoardComponent>(_currentBoard, out var machineBoardComp))
                cost = _flatpack.GetFlatpackCreationCost((_owner, flatpacker), (_currentBoard.Value, machineBoardComp));
            else
                cost = _flatpack.GetFlatpackCreationCost((_owner, flatpacker), null);

            PackButton.Disabled = !_materialStorage.CanChangeMaterialAmount(_owner, cost);
        }

        if (_currentBoard == itemSlot.Item)
            return;

        _currentBoard = itemSlot.Item;
        CostHeaderLabel.Visible = _currentBoard != null;
        InsertLabel.Visible = _currentBoard == null;

        if (_currentBoard is not null)
        {
            string? prototype = null;
            Dictionary<string, int>? cost = null;

            if (_entityManager.TryGetComponent<MachineBoardComponent>(_currentBoard, out var newMachineBoardComp))
            {
                prototype = newMachineBoardComp.Prototype;
                cost = _flatpack.GetFlatpackCreationCost((_owner, flatpacker), (_currentBoard.Value, newMachineBoardComp));
            }
            else if (_entityManager.TryGetComponent<ComputerBoardComponent>(_currentBoard, out var computerBoard))
            {
                prototype = computerBoard.Prototype;
                cost = _flatpack.GetFlatpackCreationCost((_owner, flatpacker), null);
            }

            if (prototype is not null && cost is not null)
            {
                var proto = _prototypeManager.Index<EntityPrototype>(prototype);
                MachineSprite.SetPrototype(prototype);
                MachineNameLabel.SetMessage(proto.Name);
                CostLabel.SetMarkup(GetCostString(cost));
            }
        }
        else
        {
            MachineSprite.SetPrototype(NoBoardEffectId);
            CostLabel.SetMessage(Loc.GetString("flatpacker-ui-no-board-label"));
            MachineNameLabel.SetMessage(" ");
            PackButton.Disabled = true;
        }
    }

    private string GetCostString(Dictionary<string, int> costs)
    {
        var orderedCosts = costs.OrderBy(p => p.Value).ToArray();
        var msg = new FormattedMessage();
        for (var i = 0; i < orderedCosts.Length; i++)
        {
            var (mat, amount) = orderedCosts[i];

            var matProto = _prototypeManager.Index<MaterialPrototype>(mat);

            var sheetVolume = _materialStorage.GetSheetVolume(matProto);
            var sheets = (float) -amount / sheetVolume;
            var amountText = Loc.GetString("lathe-menu-material-amount",
                ("amount", sheets),
                ("unit", Loc.GetString(matProto.Unit)));
            var text = Loc.GetString("lathe-menu-tooltip-display",
                ("amount", amountText),
                ("material", Loc.GetString(matProto.Name)));

            msg.TryAddMarkup(text, out _);

            if (i != orderedCosts.Length - 1)
                msg.PushNewline();
        }

        return msg.ToMarkup();
    }
}