<!--
SPDX-FileCopyrightText: 2017 PJB3005 <<EMAIL>>
SPDX-FileCopyrightText: 2018 Acruid <<EMAIL>>
SPDX-FileCopyrightText: 2018 Víctor Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2018 clusterfack <<EMAIL>>
SPDX-FileCopyrightText: 2018 clusterfack <<EMAIL>>
SPDX-FileCopyrightText: 2019 Injazz <<EMAIL>>
SPDX-FileCopyrightText: 2019 Pieter-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2019 Silver <<EMAIL>>
SPDX-FileCopyrightText: 2019 Víctor Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2020 Paul <<EMAIL>>
SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Paul Ritter <<EMAIL>>
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2022 Leeroy <<EMAIL>>
SPDX-FileCopyrightText: 2022 Steven K <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
SPDX-FileCopyrightText: 2024 username <<EMAIL>>
SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>..\bin\Content.Client\</OutputPath>
    <OutputType Condition="'$(FullRelease)' != 'True'">Exe</OutputType>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release;Tools;DebugOpt</Configurations>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Nett" />
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Content.Goobstation.Shared\Content.Goobstation.Shared.csproj" />
    <ProjectReference Include="..\Content.Goobstation.UIKit\Content.Goobstation.UIKit.csproj" />
    <ProjectReference Include="..\Content.Pirate.Shared\Content.Pirate.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
    <ProjectReference Include="..\Content.Pirate.Shared\Content.Pirate.Shared.csproj" />
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
  <Import Project="..\RobustToolbox\MSBuild\XamlIL.targets" />
</Project>
