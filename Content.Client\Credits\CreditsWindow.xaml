<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
               Title="{Loc 'credits-window-title'}"
               SetSize="650 650">
    <TabContainer Name="MasterTabContainer">
        <ScrollContainer Name="Ss14ContributorsTab"
                         HScrollEnabled="False">
            <BoxContainer Name="Ss14ContributorsContainer"
                          Orientation="Vertical"
                          Margin="2 2 0 0">
                <!-- Contributors get added here by code -->
            </BoxContainer>
        </ScrollContainer>
        <ScrollContainer Name="PatronsTab"
                         HScrollEnabled="False">
            <BoxContainer Name="PatronsContainer"
                          Orientation="Vertical"
                          Margin="2 2 0 0">
                <!-- Patrons get added here by code -->
            </BoxContainer>
        </ScrollContainer>
        <ScrollContainer Name="LicensesTab"
                         HScrollEnabled="False">
            <BoxContainer Name="LicensesContainer"
                          Orientation="Vertical"
                          Margin="2 2 0 0">
                <!-- Licenses get added here by code -->
            </BoxContainer>
        </ScrollContainer>
        <ScrollContainer Name="AttributionsTab"
                         HScrollEnabled="False">
            <BoxContainer Name="AttributionsContainer"
                          Orientation="Vertical"
                          Margin="2 2 0 0" />
        </ScrollContainer>
    </TabContainer>
</DefaultWindow>
