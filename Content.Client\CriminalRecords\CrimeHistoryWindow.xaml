<!--
SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               Title="{Loc 'criminal-records-console-crime-history'}"
               MinSize="660 400">
    <BoxContainer Orientation="Vertical" HorizontalExpand="True" Margin="5">
        <BoxContainer Name="Editing" Orientation="Horizontal" HorizontalExpand="True" Align="Center" Margin="5">
            <Button Name="AddButton" Text="{Loc 'criminal-records-add-history'}"/>
            <Button Name="DeleteButton" Text="{Loc 'criminal-records-delete-history'}" Disabled="True"/>
        </BoxContainer>
        <Label Name="NoHistory" Text="{Loc 'criminal-records-no-history'}" HorizontalExpand="True" HorizontalAlignment="Center"/>
        <ScrollContainer VerticalExpand="True">
            <ItemList Name="History"/> <!-- Populated when window opened -->
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
