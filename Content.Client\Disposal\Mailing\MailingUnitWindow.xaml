<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:disposal="clr-namespace:Content.Client.Disposal"
                      MinSize="300 400"
            SetSize="300 400"
            Resizable="False">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal" SeparationOverride="8">
            <Label Text="{Loc 'ui-mailing-unit-target-label'}" />
            <Label Name="Target"
                   Access="Public"
                   Text="" />
        </BoxContainer>
        <ItemList Name="TargetListContainer"
                  Access="Public"
                  SelectMode="Single"
                  VerticalExpand="True"
                  HorizontalExpand="True"
                  Margin="0 0 0 16">
        </ItemList>
        <BoxContainer Orientation="Horizontal" SeparationOverride="4">
            <Label Text="{Loc 'ui-disposal-unit-label-state'}" />
            <Label Name="UnitState" Access="Public"
                   Text="{Loc 'ui-disposal-unit-label-status'}" />
        </BoxContainer>
        <Control MinSize="0 5" />
        <BoxContainer Orientation="Horizontal"
                      SeparationOverride="4">
            <Label Text="{Loc 'ui-disposal-unit-label-pressure'}" />
            <disposal:PressureBar Name="PressureBar"
                            Access="Public"
                         MinSize="190 20"
                         HorizontalAlignment="Right"
                         MinValue="0"
                         MaxValue="1"
                         Page="0"
                         Value="0.5" />
        </BoxContainer>
        <Control MinSize="0 10" />
        <BoxContainer Orientation="Horizontal">
            <Button Name="Engage"
                    Access="Public"
                    Text="{Loc 'ui-mailing-unit-button-flush'}"
                    StyleClasses="OpenRight"
                    ToggleMode="True" />
            <Button Name="Eject"
                    Access="Public"
                    Text="{Loc 'ui-disposal-unit-button-eject'}"
                    StyleClasses="OpenBoth" />
            <CheckButton Name="Power"
                         Access="Public"
                         Text="{Loc 'ui-disposal-unit-button-power'}"
                         StyleClasses="OpenLeft" />
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
