using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client.Disposal.Mailing
{
    /// <summary>
    /// Client-side UI used to control a <see cref="Shared.Disposal.Components.MailingUnitComponent"/>
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class MailingUnitWindow : FancyWindow
    {
        public TimeSpan FullPressure;

        public MailingUnitWindow()
        {
            RobustXamlLoader.Load(this);
        }

        protected override void FrameUpdate(FrameEventArgs args)
        {
            base.FrameUpdate(args);
            PressureBar.UpdatePressure(FullPressure);
        }
    }
}
