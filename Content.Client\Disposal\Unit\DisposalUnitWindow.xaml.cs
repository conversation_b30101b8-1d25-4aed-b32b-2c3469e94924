using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Client.Disposal.Unit
{
    /// <summary>
    /// Client-side UI used to control a <see cref="Shared.Disposal.Components.DisposalUnitComponent"/>
    /// </summary>
    [GenerateTypedNameReferences]
    public sealed partial class DisposalUnitWindow : FancyWindow
    {
        public TimeSpan FullPressure;

        public DisposalUnitWindow()
        {
            IoCManager.InjectDependencies(this);
            RobustXamlLoader.Load(this);
        }

        protected override void FrameUpdate(FrameEventArgs args)
        {
            base.FrameUpdate(args);
            PressureBar.UpdatePressure(FullPressure);
        }
    }
}
