// SPDX-FileCopyrightText: 2022 ElectroJr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 T-Stalker <43253663+<PERSON><PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2022 T-Stalker <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

namespace Content.Client.Effects;

/// <summary>
/// Deletes the attached entity whenever any animation completes. Used for temporary client-side entities.
/// </summary>
[RegisterComponent]
public sealed partial class EffectVisualsComponent : Component {}