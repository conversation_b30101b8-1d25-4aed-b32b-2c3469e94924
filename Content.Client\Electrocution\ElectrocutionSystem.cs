// SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <6766154+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Electrocution;

namespace Content.Client.Electrocution
{
    public sealed class ElectrocutionSystem : SharedElectrocutionSystem
    { }
}