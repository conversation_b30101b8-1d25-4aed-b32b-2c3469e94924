// SPDX-FileCopyrightText: 2024 Aexxie <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Explosion.EntitySystems;

namespace Content.Client.Explosion;

public sealed class SmokeOnTriggerSystem : SharedSmokeOnTriggerSystem
{
}