// SPDX-FileCopyrightText: 2022 Veritius <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.FlavorText
{
    [GenerateTypedNameReferences]
    public sealed partial class FlavorText : Control
    {
        public Action<string>? OnFlavorTextChanged;

        public FlavorText()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            var loc = IoCManager.Resolve<ILocalizationManager>();
            CFlavorTextInput.Placeholder = new Rope.Leaf(loc.GetString("flavor-text-placeholder"));
            CFlavorTextInput.OnTextChanged  += _ => FlavorTextChanged();
        }

        public void FlavorTextChanged()
        {
            OnFlavorTextChanged?.Invoke(Rope.Collapse(CFlavorTextInput.TextRope).Trim());
        }
    }
}