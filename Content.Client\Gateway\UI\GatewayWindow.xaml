<!--
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'gateway-window-title'}"
                      MinSize="800 360">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <!-- This is wide as shit but makes it consistent with the cooldown label +
                   handles localisations a bit better -->
            <Label Name="NextUnlockLabel"
                   Text="{Loc 'gateway-window-portal-unlock'}"
                   Margin="5"
                   SetWidth="128"/>
            <ProgressBar Name="NextUnlockBar"
                         HorizontalExpand="True"
                         MinValue="0"
                         MaxValue="1"
                         SetHeight="25"/>
            <Label Name="NextUnlockText" Text="0" Margin="5"/>
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
            <Label Name="NextReadyLabel"
                   Text="{Loc 'gateway-window-portal-cooldown'}"
                   Margin="5"
                   SetWidth="128"/>
	        <ProgressBar Name="NextReadyBar"
	                     HorizontalExpand="True"
	                     MinValue="0"
	                     MaxValue="1"
	                     SetHeight="25"/>
	        <Label Name="NextCloseText" Text="0" Margin="5"/>
	    </BoxContainer>
	    <controls:HLine Color="#404040" Thickness="2" Margin="0 5 0 5"/>
	    <BoxContainer Name="Container"
	                  Orientation="Vertical"
	                  Margin="5 0 5 0"/>
    </BoxContainer>
</controls:FancyWindow>
