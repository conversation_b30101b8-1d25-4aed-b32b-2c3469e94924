// SPDX-FileCopyrightText: 2023 AJCM-git <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> "Arimah" <PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Boaz1111 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ghagliiarghii <<EMAIL>>
// SPDX-FileCopyrightText: 2024 HS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MilenVolf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Redfire1331 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rouge2t7 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Tayrtahn <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Truoizys <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 neutrino <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 redfire1331 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Арт <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Shared.Gravity;
using Robust.Client.GameObjects;
using Robust.Client.Animations;
using Robust.Shared.Animations;

namespace Content.Client.Gravity;

/// <inheritdoc/>
public sealed class FloatingVisualizerSystem : SharedFloatingVisualizerSystem
{
    [Dependency] private readonly AnimationPlayerSystem AnimationSystem = default!;

    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<FloatingVisualsComponent, AnimationCompletedEvent>(OnAnimationCompleted);
    }

    /// <inheritdoc/>
    public override void FloatAnimation(EntityUid uid, Vector2 offset, string animationKey, float animationTime, bool stop = false)
    {
        if (stop)
        {
            AnimationSystem.Stop(uid, animationKey);
            return;
        }

        var animation = new Animation
        {
            // We multiply by the number of extra keyframes to make time for them
            Length = TimeSpan.FromSeconds(animationTime*2),
            AnimationTracks =
            {
                new AnimationTrackComponentProperty
                {
                    ComponentType = typeof(SpriteComponent),
                    Property = nameof(SpriteComponent.Offset),
                    InterpolationMode = AnimationInterpolationMode.Linear,
                    KeyFrames =
                    {
                        new AnimationTrackProperty.KeyFrame(Vector2.Zero, 0f),
                        new AnimationTrackProperty.KeyFrame(offset, animationTime),
                        new AnimationTrackProperty.KeyFrame(Vector2.Zero, animationTime),
                    }
                }
            }
        };

        if (!AnimationSystem.HasRunningAnimation(uid, animationKey))
            AnimationSystem.Play(uid, animation, animationKey);
    }

    private void OnAnimationCompleted(EntityUid uid, FloatingVisualsComponent component, AnimationCompletedEvent args)
    {
        if (args.Key != component.AnimationKey)
            return;

        FloatAnimation(uid, component.Offset, component.AnimationKey, component.AnimationTime, stop: !component.CanFloat);
    }
}