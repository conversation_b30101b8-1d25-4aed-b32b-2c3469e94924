// SPDX-FileCopyrightText: 2023 AJCM-git <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <60421075+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2023 moonheart08 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

namespace Content.Client.Guidebook.Components;

/// <summary>
/// This is used for the guidebook monkey.
/// </summary>
[RegisterComponent]
public sealed partial class GuidebookControlsTestComponent : Component
{

}