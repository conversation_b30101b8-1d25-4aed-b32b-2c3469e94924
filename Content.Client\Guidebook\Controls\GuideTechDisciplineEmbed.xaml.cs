// SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using System.Diagnostics.CodeAnalysis;
using System.Linq;
using Content.Client.Guidebook.Richtext;
using Content.Shared.Research.Prototypes;
using JetBrains.Annotations;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Guidebook.Controls;

/// <summary>
///     Control for embedding all the technologies in a discipline into a guidebook.
/// </summary>
[UsedImplicitly, GenerateTypedNameReferences]
public sealed partial class GuideTechDisciplineEmbed : BoxContainer, IDocumentTag
{
    [Dependency] private readonly IPrototypeManager _prototype = default!;

    public GuideTechDisciplineEmbed()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        MouseFilter = MouseFilterMode.Stop;
    }

    public GuideTechDisciplineEmbed(string group) : this()
    {
        var prototypes = _prototype.EnumeratePrototypes<TechnologyPrototype>()
            .Where(p => p.Discipline.Equals(group)).OrderBy(p => p.Tier).ThenBy(p => Loc.GetString(p.Name));
        foreach (var tech in prototypes)
        {
            var embed = new GuideTechnologyEmbed(tech);
            DisciplineContainer.AddChild(embed);
        }
    }

    public bool TryParseTag(Dictionary<string, string> args, [NotNullWhen(true)] out Control? control)
    {
        control = null;
        if (!args.TryGetValue("Discipline", out var group))
        {
            Logger.Error("Technology discipline embed tag is missing discipline argument");
            return false;
        }

        var prototypes = _prototype.EnumeratePrototypes<TechnologyPrototype>()
            .Where(p => p.Discipline.Equals(group)).OrderBy(p => p.Tier).ThenBy(p => Loc.GetString(p.Name));
        foreach (var tech in prototypes)
        {
            var embed = new GuideTechnologyEmbed(tech);
            DisciplineContainer.AddChild(embed);
        }

        control = this;
        return true;
    }
}