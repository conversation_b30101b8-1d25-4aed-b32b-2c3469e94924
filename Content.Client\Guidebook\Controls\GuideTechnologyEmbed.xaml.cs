// SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using System.Diagnostics.CodeAnalysis;
using Content.Client.Guidebook.Richtext;
using Content.Client.Message;
using Content.Client.Research;
using Content.Client.UserInterface.ControlExtensions;
using Content.Shared.Research.Prototypes;
using JetBrains.Annotations;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.Guidebook.Controls;

/// <summary>
///     Control for embedding a research technology into a guidebook.
/// </summary>
[UsedImplicitly, GenerateTypedNameReferences]
public sealed partial class GuideTechnologyEmbed : Box<PERSON>ontainer, IDocumentTag, ISearchableControl
{
    [Dependency] private readonly IEntitySystemManager _systemManager = default!;
    [Dependency] private readonly IPrototypeManager _prototype = default!;

    private readonly ResearchSystem _research;
    private readonly SpriteSystem _sprite;

    public GuideTechnologyEmbed()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _research = _systemManager.GetEntitySystem<ResearchSystem>();
        _sprite = _systemManager.GetEntitySystem<SpriteSystem>();
        MouseFilter = MouseFilterMode.Stop;
    }

    public GuideTechnologyEmbed(string technology) : this()
    {
        GenerateControl(_prototype.Index<TechnologyPrototype>(technology));
    }

    public GuideTechnologyEmbed(TechnologyPrototype technology) : this()
    {
        GenerateControl(technology);
    }

    public bool CheckMatchesSearch(string query)
    {
        return this.ChildrenContainText(query);
    }

    public void SetHiddenState(bool state, string query)
    {
        Visible = CheckMatchesSearch(query) ? state : !state;
    }

    public bool TryParseTag(Dictionary<string, string> args, [NotNullWhen(true)] out Control? control)
    {
        control = null;
        if (!args.TryGetValue("Technology", out var id))
        {
            Logger.Error("Technology embed tag is missing technology prototype argument");
            return false;
        }

        if (!_prototype.TryIndex<TechnologyPrototype>(id, out var technology))
        {
            Logger.Error($"Specified technology prototype \"{id}\" is not a valid technology prototype");
            return false;
        }

        GenerateControl(technology);

        control = this;
        return true;
    }

    private void GenerateControl(TechnologyPrototype technology)
    {
        var discipline = _prototype.Index(technology.Discipline);

        NameLabel.SetMarkup($"[bold]{Loc.GetString(technology.Name)}[/bold]");
        DescriptionLabel.SetMessage(_research.GetTechnologyDescription(technology, includePrereqs: true, disciplinePrototype: discipline));
        TechTexture.Texture = _sprite.Frame0(technology.Icon);

        DisciplineColorBackground.PanelOverride = new StyleBoxFlat
        {
            BackgroundColor = discipline.Color
        };
    }
}