// SPDX-FileCopyrightText: 2023 He<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2023 moonheart08 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 AJCM-git <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON>-<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Sk1tch <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Your Name <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Client.Guidebook.RichText;
using Content.Client.UserInterface.ControlExtensions;
using Content.Client.UserInterface.Controls;
using Content.Client.UserInterface.Controls.FancyTree;
using Content.Client.UserInterface.Systems.Info;
using Content.Shared.Guidebook;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.ContentPack;
using Robust.Shared.Prototypes;

namespace Content.Client.Guidebook.Controls;

[GenerateTypedNameReferences]
public sealed partial class GuidebookWindow : FancyWindow, ILinkClickHandler
{
    [Dependency] private readonly DocumentParsingManager _parsingMan = default!;
    [Dependency] private readonly IResourceManager _resourceManager = default!;

    private Dictionary<ProtoId<GuideEntryPrototype>, GuideEntry> _entries = new();

    private readonly ISawmill _sawmill;

    public ProtoId<GuideEntryPrototype> LastEntry;

    public GuidebookWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _sawmill = Logger.GetSawmill("Guidebook");

        Tree.OnSelectedItemChanged += OnSelectionChanged;

        SearchBar.OnTextChanged += _ =>
        {
            HandleFilter();
        };
    }

    public void HandleClick(string link)
    {
        if (!_entries.TryGetValue(link, out var entry))
            return;

        if (Tree.TryGetIndexFromMetadata(entry, out var index))
        {
            Tree.ExpandParentEntries(index.Value);
            Tree.SetSelectedIndex(index);
        }
        else
            ShowGuide(entry);
    }

    private void OnSelectionChanged(TreeItem? item)
    {
        if (item != null && item.Metadata is GuideEntry entry)
        {
            ShowGuide(entry);

            var isRulesEntry = entry.RuleEntry;
            ReturnContainer.Visible = isRulesEntry;
            HomeButton.OnPressed += _ => ShowGuide(entry);
        }
        else
            ClearSelectedGuide();
    }

    public void ClearSelectedGuide()
    {
        Placeholder.Visible = true;
        EntryContainer.Visible = false;
        SearchContainer.Visible = false;
        EntryContainer.RemoveAllChildren();
    }

    private void ShowGuide(GuideEntry entry)
    {
        Scroll.SetScrollValue(default);
        Placeholder.Visible = false;
        EntryContainer.Visible = true;
        SearchBar.Text = "";
        EntryContainer.RemoveAllChildren();
        using var file = _resourceManager.ContentFileReadText(entry.Text);

        SearchContainer.Visible = entry.FilterEnabled;

        if (!_parsingMan.TryAddMarkup(EntryContainer, file.ReadToEnd()))
        {
            // The guidebook will automatically display the in-guidebook error if it fails

            _sawmill.Error($"Failed to parse contents of guide document {entry.Id}.");
        }

        LastEntry = entry.Id;
    }

    public void UpdateGuides(
        Dictionary<ProtoId<GuideEntryPrototype>, GuideEntry> entries,
        List<ProtoId<GuideEntryPrototype>>? rootEntries = null,
        ProtoId<GuideEntryPrototype>? forceRoot = null,
        ProtoId<GuideEntryPrototype>? selected = null)
    {
        _entries = entries;
        RepopulateTree(rootEntries, forceRoot);
        ClearSelectedGuide();

        Split.State = SplitContainer.SplitState.Auto;
        if (entries.Count == 1)
        {
            TreeBox.Visible = false;
            Split.ResizeMode = SplitContainer.SplitResizeMode.NotResizable;
            selected = entries.Keys.First();
        }
        else
        {
            TreeBox.Visible = true;
            Split.ResizeMode = SplitContainer.SplitResizeMode.RespectChildrenMinSize;
        }

        if (selected != null)
        {
            var item = Tree.Items.FirstOrDefault(x => x.Metadata is GuideEntry entry && entry.Id == selected);
            Tree.SetSelectedIndex(item?.Index);
        }
    }

    private IEnumerable<GuideEntry> GetSortedEntries(List<ProtoId<GuideEntryPrototype>>? rootEntries)
    {
        if (rootEntries == null)
        {
            HashSet<ProtoId<GuideEntryPrototype>> entries = new(_entries.Keys);
            foreach (var entry in _entries.Values)
            {
                if (entry.Children.Count > 0)
                {
                    var sortedChildren = entry.Children
                        .Select(childId => _entries[childId])
                        .OrderBy(childEntry => childEntry.Priority)
                        .ThenBy(childEntry => Loc.GetString(childEntry.Name))
                        .Select(childEntry => new ProtoId<GuideEntryPrototype>(childEntry.Id))
                        .ToList();

                    entry.Children = sortedChildren;
                }

                entries.ExceptWith(entry.Children);
            }

            rootEntries = entries.ToList();
        }

        return rootEntries
            .Select(rootEntryId => _entries[rootEntryId])
            .OrderBy(rootEntry => rootEntry.Priority)
            .ThenBy(rootEntry => Loc.GetString(rootEntry.Name));
    }

    private void RepopulateTree(List<ProtoId<GuideEntryPrototype>>? roots = null,
        ProtoId<GuideEntryPrototype>? forcedRoot = null)
    {
        Tree.Clear();

        HashSet<ProtoId<GuideEntryPrototype>> addedEntries = new();

        var parent = forcedRoot == null ? null : AddEntry(forcedRoot.Value, null, addedEntries);
        foreach (var entry in GetSortedEntries(roots))
        {
            AddEntry(entry.Id, parent, addedEntries);
        }

        Tree.SetAllExpanded(true);
    }

    private TreeItem? AddEntry(ProtoId<GuideEntryPrototype> id,
        TreeItem? parent,
        HashSet<ProtoId<GuideEntryPrototype>> addedEntries)
    {
        if (!_entries.TryGetValue(id, out var entry))
            return null;

        if (!addedEntries.Add(id))
        {
            // TODO GUIDEBOOK Maybe allow duplicate entries?
            // E.g., for adding medicine under both chemicals & the chemist job
            Logger.Error($"Adding duplicate guide entry: {id}");
            return null;
        }

        var rulesProto = UserInterfaceManager.GetUIController<InfoUIController>().GetCoreRuleEntry();
        if (entry.RuleEntry && entry.Id != rulesProto.Id)
            return null;

        var item = Tree.AddItem(parent);
        item.Metadata = entry;
        var name = Loc.GetString(entry.Name);
        item.Label.Text = name;

        foreach (var child in entry.Children)
        {
            AddEntry(child, item, addedEntries);
        }

        return item;
    }

    private void HandleFilter()
    {
        var emptySearch = SearchBar.Text.Trim().Length == 0;

        if (Tree.SelectedItem != null && Tree.SelectedItem.Metadata is GuideEntry entry && entry.FilterEnabled)
        {
            var foundElements = EntryContainer.GetSearchableControls();

            foreach (var element in foundElements)
            {
                element.SetHiddenState(true, SearchBar.Text.Trim());
            }
        }
    }
}