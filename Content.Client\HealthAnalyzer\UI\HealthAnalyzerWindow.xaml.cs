// SPDX-FileCopyrightText: 2022 Fishfish458 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Rane <60792108+<PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2022 Rinkashikachi <<EMAIL>>
// SPDX-FileCopyrightText: 2022 fishfish458 <fishfish458>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Artjom <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 MilenVolf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 KrasnoshchekovPavel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Krunklehorn <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rainfey <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Whisper <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 goet <<EMAIL>>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 August Eymann <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Kayzel <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Roudenn <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Spatison <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Trest <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 kurokoTurbo <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Shared.Atmos;
using Content.Client.UserInterface.Controls;
using Content.Shared.Damage;
using Content.Shared.Damage.Prototypes;
using Content.Goobstation.Maths.FixedPoint;
using Content.Shared.Humanoid;
using Content.Shared.Humanoid.Prototypes;
using Content.Shared.IdentityManagement;
using Content.Shared.MedicalScanner;
using Content.Shared.Mobs;
using Content.Shared.Mobs.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.ResourceManagement;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;

// Shitmed Change
using Content.Shared._Shitmed.Targeting;
using Content.Shared._Shitmed.Medical.HealthAnalyzer;
using Content.Shared._Shitmed.Medical.Surgery.Wounds;
using Content.Shared._Shitmed.Medical.Surgery.Wounds.Systems;
using Content.Shared.Atmos.Rotting;
using Content.Shared.Body.Part;
using Content.Shared.Chemistry.Components;
using Content.Shared.Chemistry.Reagent;
using System.Globalization;

namespace Content.Client.HealthAnalyzer.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class HealthAnalyzerWindow : FancyWindow
    {
        private readonly IEntityManager _entityManager;
        private readonly SpriteSystem _spriteSystem;
        private readonly IPrototypeManager _prototypes;
        private readonly IResourceCache _cache;

        // Shitmed Change Start
        private readonly WoundSystem _wound;
        public event Action<TargetBodyPart?, EntityUid>? OnBodyPartSelected;
        public event Action<HealthAnalyzerMode, EntityUid>? OnModeChanged;
        private EntityUid _spriteViewEntity;

        [ValidatePrototypeId<EntityPrototype>]
        private readonly EntProtoId _bodyView = "AlertSpriteView";

        private readonly Dictionary<TargetBodyPart, TextureButton> _bodyPartControls;
        private EntityUid? _target;
        // Shitmed Change End

        public HealthAnalyzerWindow()
        {
            RobustXamlLoader.Load(this);

            var dependencies = IoCManager.Instance!;
            _entityManager = dependencies.Resolve<IEntityManager>();
            _spriteSystem = _entityManager.System<SpriteSystem>();
            _prototypes = dependencies.Resolve<IPrototypeManager>();
            _cache = dependencies.Resolve<IResourceCache>();
            // Shitmed Change Start
            _wound = _entityManager.System<WoundSystem>();
            _bodyPartControls = new Dictionary<TargetBodyPart, TextureButton>
            {
                { TargetBodyPart.Head, HeadButton },
                { TargetBodyPart.Chest, ChestButton },
                { TargetBodyPart.Groin, GroinButton },
                { TargetBodyPart.LeftArm, LeftArmButton },
                { TargetBodyPart.LeftHand, LeftHandButton },
                { TargetBodyPart.RightArm, RightArmButton },
                { TargetBodyPart.RightHand, RightHandButton },
                { TargetBodyPart.LeftLeg, LeftLegButton },
                { TargetBodyPart.LeftFoot, LeftFootButton },
                { TargetBodyPart.RightLeg, RightLegButton },
                { TargetBodyPart.RightFoot, RightFootButton },
            };

            foreach (var bodyPartButton in _bodyPartControls)
            {
                bodyPartButton.Value.MouseFilter = MouseFilterMode.Stop;
                bodyPartButton.Value.OnPressed += _ => SetActiveBodyPart(bodyPartButton.Key, bodyPartButton.Value);
            }
            ReturnButton.OnPressed += _ => ResetBodyPart();
            BodyButton.OnPressed += _ => SetMode(HealthAnalyzerMode.Body);
            OrgansButton.OnPressed += _ => SetMode(HealthAnalyzerMode.Organs);
            ChemicalsButton.OnPressed += _ => SetMode(HealthAnalyzerMode.Chemicals);
            // Shitmed Change End
        }

        // Shitmed Change Start
        public void SetActiveBodyPart(TargetBodyPart part, TextureButton button)
        {
            if (_target == null)
                return;

            OnBodyPartSelected?.Invoke(part, _target.Value);
        }

        public void SetMode(HealthAnalyzerMode mode)
        {
            if (_target == null)
                return;

            OnModeChanged?.Invoke(mode, _target.Value);
        }

        public void ResetBodyPart()
        {
            if (_target == null)
                return;

            OnBodyPartSelected?.Invoke(null, _target.Value);
        }

        public void SetActiveButtons(bool isHumanoid)
        {
            foreach (var button in _bodyPartControls)
                button.Value.Visible = isHumanoid;
        }

        public bool TrySetupEntity(HealthAnalyzerBaseMessage msg)
        {
            if (_target is null)
            {
                NoPatientDataText.Visible = true;
                return false;
            }

            SetActiveButtons(_entityManager.HasComponent<TargetingComponent>(_target.Value));

            NoPatientDataText.Visible = false;

            // Scan Mode

            ScanModeLabel.Text = msg.ScanMode.HasValue
                ? msg.ScanMode.Value
                    ? Loc.GetString("health-analyzer-window-scan-mode-active")
                    : Loc.GetString("health-analyzer-window-scan-mode-inactive")
                : Loc.GetString("health-analyzer-window-entity-unknown-text");

            ScanModeLabel.FontColorOverride = msg.ScanMode.HasValue && msg.ScanMode.Value ? Color.Green : Color.Red;

            // Patient Information

            SpriteView.SetEntity(_entityManager.HasComponent<HumanoidAppearanceComponent>(_target.Value)
                ? SetupIcon(msg.Body, msg.Bleeding)
                : _target.Value);
            SpriteView.Visible = msg.ScanMode.HasValue && msg.ScanMode.Value;
            PartView.Visible = SpriteView.Visible;
            NoDataTex.Visible = !SpriteView.Visible;

            var name = new FormattedMessage();
            name.PushColor(Color.White);
            name.AddText(_entityManager.HasComponent<MetaDataComponent>(_target.Value)
                ? Identity.Name(_target.Value, _entityManager)
                : Loc.GetString("health-analyzer-window-entity-unknown-text"));
            NameLabel.SetMessage(name);

            SpeciesLabel.Text =
                _entityManager.TryGetComponent<HumanoidAppearanceComponent>(_target.Value,
                    out var humanoidAppearanceComponent)
                    ? Loc.GetString(_prototypes.Index<SpeciesPrototype>(humanoidAppearanceComponent.Species).Name)
                    : Loc.GetString("health-analyzer-window-entity-unknown-species-text");

            // Basic Diagnostic

            TemperatureLabel.Text = !float.IsNaN(msg.Temperature)
                ? $"{msg.Temperature - Atmospherics.T0C:F1} °C ({msg.Temperature:F1} K)"
                : Loc.GetString("health-analyzer-window-entity-unknown-value-text");

            BloodLabel.Text = !float.IsNaN(msg.BloodLevel)
                ? $"{msg.BloodLevel * 100:F1} %"
                : Loc.GetString("health-analyzer-window-entity-unknown-value-text");

            StatusLabel.Text =
                _entityManager.TryGetComponent<MobStateComponent>(_target.Value, out var mobStateComponent)
                    ? GetStatus(mobStateComponent.CurrentState)
                    : Loc.GetString("health-analyzer-window-entity-unknown-text");

            return true;
        }

        // All of this shit got fucked with, we're cooked hometh :wilted_rose: shitmod when
        public void Populate(HealthAnalyzerBodyMessage msg)
        {
            _target = _entityManager.GetEntity(msg.TargetEntity);
            EntityUid? part = msg.SelectedPart != null ? _entityManager.GetEntity(msg.SelectedPart.Value) : null;
            var isPart = part != null;

            if (!TrySetupEntity(msg)
                || _target is null
                || !_entityManager.TryGetComponent<DamageableComponent>(isPart ? part : _target, out var damageable))
                return;

            ReturnButton.Visible = isPart;
            PartNameLabel.Visible = isPart;
            DamageLabelHeading.Visible = true;
            DamageLabel.Visible = true;
            DamageLabel.Text = damageable.TotalDamage.ToString();

            if (part != null)
                PartNameLabel.Text = _entityManager.HasComponent<MetaDataComponent>(part)
                    ? Identity.Name(part.Value, _entityManager)
                    : Loc.GetString("health-analyzer-window-entity-unknown-value-text");

            var damageSortedGroups =
                damageable.DamagePerGroup.OrderByDescending(damage => damage.Value)
                    .ToDictionary(x => x.Key, x => x.Value);

            IReadOnlyDictionary<string, FixedPoint2> damagePerType = damageable.Damage.DamageDict;

            DrawDiagnosticGroups(damageSortedGroups, damagePerType);

            ConditionsListContainer.RemoveAllChildren();

            if (msg.Unrevivable == true)
                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = Loc.GetString("condition-body-unrevivable", ("entity", Identity.Name(_target.Value, _entityManager))),
                    Margin = new Thickness(0, 4),
                });

            foreach (var (bodyPart, isBleeding) in msg.Bleeding)
            {
                if (!isBleeding)
                    continue;

                var locString = Loc.GetString($"condition-body-bleeding-{bodyPart.ToString()}", ("entity", Identity.Name(_target.Value, _entityManager)));

                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = locString,
                    Margin = new Thickness(0, 4),
                });
            }

            foreach (var (woundableTrauma, traumas) in msg.Traumas)
            {
                if (!TryGetEntityName(woundableTrauma, out var woundableName)
                    || isPart
                    && woundableTrauma != msg.SelectedPart)
                    continue;

                foreach (var trauma in traumas)
                {
                    // TODO: Once these string conditionals are better defined, rewrite to use a switch case based on trauma types.
                    string locString;
                    if (trauma.TargetType.HasValue)
                        locString = Loc.GetString($"condition-body-trauma-{trauma.TraumaType}",
                            ("targetSymmetry", trauma.TargetType.Value.Item2 != BodyPartSymmetry.None
                                ? $"{trauma.TargetType.Value.Item2.ToString().ToLower()} " // This is so fucking ugly.
                                : ""),
                            ("targetType", trauma.TargetType.Value.Item1.ToString().ToLower()));
                    else
                        locString = trauma.SeverityString != null
                            ? Loc.GetString($"condition-body-trauma-{trauma.TraumaType}-{trauma.SeverityString}", ("woundable", woundableName))
                            : Loc.GetString($"condition-body-trauma-{trauma.TraumaType}", ("woundable", woundableName));

                    ConditionsListContainer.AddChild(new RichTextLabel
                    {
                        Text = locString,
                        Margin = new Thickness(0, 4),
                    });
                }
            }

            /*foreach (var (woundablePain, pain) in msg.NervePainFeels)
            {
                if (pain == 1.0
                    || !TryGetEntityName(woundablePain, out var woundableName)
                    || isPart
                    && woundablePain != msg.SelectedPart)
                    continue;

                var painString = pain > 1.0 ? "increased" : "decreased";
                var locString = Loc.GetString($"condition-body-pain-{painString}", ("woundable", woundableName));

                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = locString,
                    Margin = new Thickness(0, 4),
                });
            }*/

            if (ConditionsListContainer.ChildCount == 0)
            {
                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = Loc.GetString("condition-none"),
                    Margin = new Thickness(0, 4),
                });
            }
        }
        public void Populate(HealthAnalyzerOrgansMessage msg)
        {
            _target = _entityManager.GetEntity(msg.TargetEntity);

            if (!TrySetupEntity(msg))
                return;

            ReturnButton.Visible = false;
            PartNameLabel.Visible = false;
            DamageLabelHeading.Visible = false;
            DamageLabel.Visible = false;

            ConditionsListContainer.RemoveAllChildren();
            GroupsContainer.RemoveAllChildren();
            foreach (var (organ, data) in msg.Organs)
            {
                var organEnt = _entityManager.GetEntity(organ);

                if (!TryGetEntityName(organEnt, out var organName)
                    || data.IntegrityCap == 0) // avoid division by zero
                    continue;

                DrawOrganDiagnostics(organEnt, organName, data.Integrity / data.IntegrityCap * 100);

                if (_entityManager.HasComponent<RottingComponent>(organEnt))
                {
                    ConditionsListContainer.AddChild(new RichTextLabel
                    {
                        Text = Loc.GetString("condition-organ-rotting", ("organ", organName)),
                        Margin = new Thickness(0, 4),
                    });
                }

                /*if (data.Integrity > data.IntegrityCap * 0.90) // Organs without at LEAST some significant damage wont be shown.
                    return;
*/
                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = Loc.GetString($"condition-organ-damage-{data.Severity.ToString()}", ("organ", organName)),
                    Margin = new Thickness(0, 4),
                });
            }

            if (ConditionsListContainer.ChildCount == 0)
            {
                ConditionsListContainer.AddChild(new RichTextLabel
                {
                    Text = Loc.GetString("condition-none"),
                    Margin = new Thickness(0, 4),
                });
            }
        }

        public void Populate(HealthAnalyzerChemicalsMessage msg)
        {
            _target = _entityManager.GetEntity(msg.TargetEntity);

            if (!TrySetupEntity(msg))
                return;

            ReturnButton.Visible = false;
            PartNameLabel.Visible = false;
            DamageLabelHeading.Visible = false;
            DamageLabel.Visible = false;

            ConditionsListContainer.RemoveAllChildren();
            GroupsContainer.RemoveAllChildren();

            DrawSolutionDiagnostics(msg.Solutions);

            ConditionsListContainer.AddChild(new RichTextLabel
            {
                Text = Loc.GetString("condition-none"),
                Margin = new Thickness(0, 4),
            });
        }

        private bool TryGetEntityName(NetEntity ent, out string name)
        {
            name = Loc.GetString("health-analyzer-window-entity-unknown-value-text");
            var targetedEnt = _entityManager.GetEntity(ent);

            if (!_entityManager.HasComponent<MetaDataComponent>(targetedEnt))
                return false;

            name = Identity.Name(targetedEnt, _entityManager);
            return true;
        }

        private bool TryGetEntityName(EntityUid ent, out string name)
        {
            name = Loc.GetString("health-analyzer-window-entity-unknown-value-text");

            if (!_entityManager.HasComponent<MetaDataComponent>(ent))
                return false;

            name = Identity.Name(ent, _entityManager);
            return true;
        }
        // Shitmed Change End
        private static string GetStatus(MobState mobState)
        {
            return mobState switch
            {
                MobState.Alive => Loc.GetString("health-analyzer-window-entity-alive-text"),
                MobState.Critical => Loc.GetString("health-analyzer-window-entity-critical-text"),
                MobState.Dead => Loc.GetString("health-analyzer-window-entity-dead-text"),
                _ => Loc.GetString("health-analyzer-window-entity-unknown-text"),
            };
        }

        private void DrawDiagnosticGroups(
            Dictionary<string, FixedPoint2> groups,
            IReadOnlyDictionary<string, FixedPoint2> damageDict)
        {
            GroupsContainer.RemoveAllChildren();

            foreach (var (damageGroupId, damageAmount) in groups)
            {
                if (damageAmount == 0)
                    continue;

                var groupTitleText = $"{Loc.GetString(
                    "health-analyzer-window-damage-group-text",
                    ("damageGroup", _prototypes.Index<DamageGroupPrototype>(damageGroupId).LocalizedName),
                    ("amount", damageAmount)
                )}";

                var groupContainer = new BoxContainer
                {
                    Align = BoxContainer.AlignMode.Begin,
                    Orientation = BoxContainer.LayoutOrientation.Vertical,
                };

                groupContainer.AddChild(CreateDiagnosticGroupTitle(groupTitleText, damageGroupId));

                GroupsContainer.AddChild(groupContainer);

                // Show the damage for each type in that group.
                var group = _prototypes.Index<DamageGroupPrototype>(damageGroupId);

                foreach (var type in group.DamageTypes)
                {
                    if (!damageDict.TryGetValue(type, out var typeAmount) || typeAmount <= 0)
                        continue;

                    var damageString = Loc.GetString(
                        "health-analyzer-window-damage-type-text",
                        ("damageType", _prototypes.Index<DamageTypePrototype>(type).LocalizedName),
                        ("amount", typeAmount)
                    );

                    groupContainer.AddChild(CreateDiagnosticItemLabel(damageString.Insert(0, " · ")));
                }
            }
        }

        private void DrawOrganDiagnostics(EntityUid ent, string name, FixedPoint2 damage)
        {
            TextInfo textInfo = new CultureInfo("en-US", false).TextInfo;
            var groupTitleText = $"{Loc.GetString(
                "group-organ-status",
                ("organ", textInfo.ToTitleCase(name)),
                ("capacity", damage)
            )}";

            var groupContainer = new BoxContainer
            {
                Align = BoxContainer.AlignMode.Begin,
                Orientation = BoxContainer.LayoutOrientation.Vertical,
            };

            groupContainer.AddChild(CreateDiagnosticGroupTitle(groupTitleText, ent));

            GroupsContainer.AddChild(groupContainer);
        }

        private void DrawSolutionDiagnostics(Dictionary<NetEntity, Solution> solutions)
        {
            TextInfo textInfo = new CultureInfo("en-US", false).TextInfo;
            foreach (var (ent, data) in solutions)
            {
                var groupTitleText = $"{Loc.GetString(
                    "group-solution-name",
                    ("solution", data.Name ?? Loc.GetString("group-solution-unknown"))
                )}";

                var groupContainer = new BoxContainer
                {
                    Align = BoxContainer.AlignMode.Begin,
                    Orientation = BoxContainer.LayoutOrientation.Vertical,
                };

                groupContainer.AddChild(CreateDiagnosticGroupTitle(textInfo.ToTitleCase(groupTitleText), "metaphysical"));

                GroupsContainer.AddChild(groupContainer);

                foreach (var reagent in data.Contents)
                {
                    if (reagent.Quantity == 0)
                        continue;

                    var reagentName = Loc.GetString("chem-master-window-unknown-reagent-text");
                    if (_prototypes.TryIndex(reagent.Reagent.Prototype, out ReagentPrototype? proto))
                        reagentName = proto.LocalizedName;

                    var reagentString = $"{Loc.GetString(
                        "group-solution-contents",
                        ("reagent", textInfo.ToTitleCase(reagentName)),
                        ("quantity", reagent.Quantity)
                    )}";

                    groupContainer.AddChild(CreateDiagnosticItemLabel(reagentString.Insert(0, " · ")));
                }
            }
        }

        private Texture GetTexture(string texture)
        {
            var rsiPath = new ResPath("/Textures/Objects/Devices/health_analyzer.rsi");
            var rsiSprite = new SpriteSpecifier.Rsi(rsiPath, texture);

            var rsi = _cache.GetResource<RSIResource>(rsiSprite.RsiPath).RSI;
            if (!rsi.TryGetState(rsiSprite.RsiState, out var state))
            {
                rsiSprite = new SpriteSpecifier.Rsi(rsiPath, "unknown");
            }

            return _spriteSystem.Frame0(rsiSprite);
        }

        private static Label CreateDiagnosticItemLabel(string text)
        {
            return new Label
            {
                Text = text,
            };
        }

        private BoxContainer CreateDiagnosticGroupTitle(string text, string id)
        {
            var rootContainer = new BoxContainer
            {
                Margin = new Thickness(0, 6, 0, 0),
                VerticalAlignment = VAlignment.Bottom,
                Orientation = BoxContainer.LayoutOrientation.Horizontal,
            };

            rootContainer.AddChild(new TextureRect
            {
                SetSize = new Vector2(30, 30),
                Texture = GetTexture(id.ToLower())
            });

            rootContainer.AddChild(CreateDiagnosticItemLabel(text));

            return rootContainer;
        }

        private BoxContainer CreateDiagnosticGroupTitle(string text, EntityUid ent, string? TextureOverride = null)
        {
            var rootContainer = new BoxContainer
            {
                Margin = new Thickness(0, 6, 0, 0),
                VerticalAlignment = VAlignment.Bottom,
                Orientation = BoxContainer.LayoutOrientation.Horizontal,
            };

            if (TextureOverride != null)
            {
                rootContainer.AddChild(new TextureRect
                {
                    SetSize = new Vector2(30, 30),
                    Texture = GetTexture(TextureOverride.ToLower())
                });
            }
            else
            {
                var spriteView = new SpriteView
                {
                    SetSize = new Vector2(30, 30),
                    OverrideDirection = Direction.South,
                };

                spriteView.SetEntity(ent);

                rootContainer.AddChild(spriteView);
            }

            rootContainer.AddChild(CreateDiagnosticItemLabel(text));

            return rootContainer;
        }

        // Shitmed Change Start
        /// <summary>
        /// Sets up the Body Doll using Alert Entity to use in Health Analyzer.
        /// </summary>
        private EntityUid? SetupIcon(Dictionary<TargetBodyPart, WoundableSeverity>? body,
            Dictionary<TargetBodyPart, bool> bleeding)
        {
            if (body is null)
                return null;

            if (!_entityManager.Deleted(_spriteViewEntity))
                _entityManager.QueueDeleteEntity(_spriteViewEntity);

            _spriteViewEntity = _entityManager.Spawn(_bodyView);

            if (!_entityManager.TryGetComponent<SpriteComponent>(_spriteViewEntity, out var sprite))
                return null;

            int layer = 0;
            foreach (var (bodyPart, integrity) in body)
            {
                // TODO: PartStatusUIController and make it use layers instead of TextureRects when EE refactors alerts.
                string enumName = Enum.GetName(typeof(TargetBodyPart), bodyPart) ?? "Unknown";
                int enumValue = (int) integrity;
                var baseRsiPath = new ResPath($"/Textures/_Shitmed/Interface/Targeting/Status/{enumName.ToLowerInvariant()}.rsi");
                var rsi = new SpriteSpecifier.Rsi(baseRsiPath, $"{enumName.ToLowerInvariant()}_{enumValue}");
                // Shitcode with love from Russia :)
                CreateOrAddToLayer(sprite, rsi, layer);
                layer++;

                if (bleeding.TryGetValue(bodyPart, out var isBleeding) && isBleeding)
                {
                    var bleedRsi = new SpriteSpecifier.Rsi(baseRsiPath, $"{enumName.ToLowerInvariant()}_bleed");
                    CreateOrAddToLayer(sprite, bleedRsi, layer);
                    layer++;
                }
            }
            return _spriteViewEntity;
        }

        private void CreateOrAddToLayer(SpriteComponent sprite, SpriteSpecifier rsi, int layer)
        {
            if (!sprite.TryGetLayer(layer, out _))
                sprite.AddLayer(_spriteSystem.Frame0(rsi));
            else
                sprite.LayerSetTexture(layer, _spriteSystem.Frame0(rsi));

            sprite.LayerSetScale(layer, new Vector2(3f, 3f));
        }
        // Shitmed Change End
    }
}
