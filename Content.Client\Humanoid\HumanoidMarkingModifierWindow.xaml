<!--
SPDX-FileCopyrightText: 2022 Flipp <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:humanoid="clr-namespace:Content.Client.Humanoid">
    <ScrollContainer MinHeight="500" MinWidth="700">
        <BoxContainer Orientation="Vertical" HorizontalExpand="True">
            <humanoid:MarkingPicker Name="MarkingPickerWidget" />
            <BoxContainer>
                <CheckBox Name="MarkingForced" Text="Force" Pressed="True" />
                <CheckBox Name="MarkingIgnoreSpecies" Text="Ignore Species" Pressed="True" />
            </BoxContainer>
            <Collapsible HorizontalExpand="True">
                <CollapsibleHeading Title="Base layers" />
                <CollapsibleBody HorizontalExpand="True">
                    <BoxContainer Name="BaseLayersContainer" Orientation="Vertical" HorizontalExpand="True" />
                </CollapsibleBody>
            </Collapsible>
        </BoxContainer>
    </ScrollContainer>
</DefaultWindow>
