<!--
SPDX-FileCopyrightText: 2025 <PERSON> <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 SlamBamActionman <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               Title="{Loc 'implanter-set-draw-window'}"
               MinSize="5 30">
    <BoxContainer Orientation="Vertical" Margin="10 5">
        <Label Text="{Loc 'implanter-set-draw-info'}" Margin="0 0 0 5"/>
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'implanter-set-draw-type'}" Margin="0 0 5 0"/>
            <OptionButton Name="ImplantSelector"/> <!-- Populated in LoadVerbs -->
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
