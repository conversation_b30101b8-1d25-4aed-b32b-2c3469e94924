// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 SlamBamActionman <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using System.Linq;

namespace Content.Client.Implants.UI;

[GenerateTypedNameReferences]
public sealed partial class DeimplantChoiceWindow : FancyWindow
{
    public Action<string?>? OnImplantChange;

    private Dictionary<string, string> _implants = new();

    private string? _chosenImplant;

    public DeimplantChoiceWindow()
    {
        RobustXamlLoader.Load(this);

        ImplantSelector.OnItemSelected += args =>
        {
            OnImplantChange?.Invoke(_implants.ElementAt(args.Id).Key);
            ImplantSelector.SelectId(args.Id);
        };
    }

    public void UpdateImplantList(Dictionary<string, string> implants)
    {
        _implants = implants;
        int i = 0;
        ImplantSelector.Clear();
        foreach (var implantDict in _implants)
        {
            ImplantSelector.AddItem(implantDict.Value, i);
            i++;
        }
    }

    public void UpdateState(string? implant)
    {
        _chosenImplant = implant;

        for (int id = 0; id < ImplantSelector.ItemCount; id++)
        {
            if (_implants.ElementAt(id).Key.Equals(_chosenImplant))
            {
                ImplantSelector.SelectId(id);
                break;
            }
        }
    }
}