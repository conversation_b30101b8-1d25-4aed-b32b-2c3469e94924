// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;

namespace Content.Client.Info;

[GenerateTypedNameReferences]
public sealed partial class InfoSection : BoxContainer
{
    public InfoSection(string title, string text, bool markup = false)
    {
        RobustXamlLoader.Load(this);
        SetText(title, text, markup);
    }

    public void SetText(string title, string text, bool markup = false)
    {
        TitleLabel.Text = title;
        if (markup)
            Content.SetMessage(FormattedMessage.FromMarkupOrThrow(text.Trim()));
        else
            Content.SetMessage(text);
    }
}