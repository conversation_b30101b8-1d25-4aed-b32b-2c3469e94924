<!--
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2023 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<ContainerButton xmlns="https://spacestation14.io"
                 xmlns:customControls1="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <PanelContainer Name="BackgroundColorPanel"/>
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="4">
        <Label Name="RoleLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Margin="5,5,5,5"/>
        <customControls1:VSeparator/>
        <Label Name="PlaytimeLabel"
               SizeFlagsStretchRatio="3"
               HorizontalExpand="True"
               ClipText="True"
               Margin="5,5,5,5"/>
    </BoxContainer>
</ContainerButton>
