<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 Morb <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> A<PERSON>ilera Puerto <6766154+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client">
    <BoxContainer Orientation="Vertical" SeparationOverride="5">
        <BoxContainer Orientation="Horizontal" VerticalExpand="True">
            <Button Name="InputButton" ToggleMode="True" Text="{Loc 'instruments-component-menu-input-button'}" TextAlign="Center"
                    HorizontalExpand="True" SizeFlagsStretchRatio="1" />
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.5" />
            <Button Name="BandButton" Text="{Loc 'instruments-component-menu-band-button'}" TextAlign="Center" HorizontalExpand="True"
                    SizeFlagsStretchRatio="1"/>
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.5" />
            <Button Name="FileButton" Text="{Loc 'instruments-component-menu-play-button'}" TextAlign="Center" HorizontalExpand="True"
                    SizeFlagsStretchRatio="1" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" VerticalExpand="True">
            <Button Name="LoopButton" ToggleMode="True" Text="{Loc 'instruments-component-menu-loop-button'}" TextAlign="Center" HorizontalExpand="True"
                    SizeFlagsStretchRatio="1" />
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.5" />
            <Button Name="ChannelsButton" Text="{Loc 'instruments-component-menu-channels-button'}" TextAlign="Center" HorizontalExpand="True"
                    SizeFlagsStretchRatio="1"/>
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.5" />
            <Button Name="StopButton" Text="{Loc 'instruments-component-menu-stop-button'}" TextAlign="Center" HorizontalExpand="True"
                    SizeFlagsStretchRatio="1" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" VerticalExpand="True">
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.125" />
            <Slider Name="PlaybackSlider" HorizontalExpand="True" />
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="0.125" />
        </BoxContainer>
    </BoxContainer>
    <PanelContainer Name="UnavailableOverlay" Visible="False" MouseFilter="Stop">
        <PanelContainer.PanelOverride>
            <gfx:StyleBoxFlat BackgroundColor="#000000E5" />
        </PanelContainer.PanelOverride>
        <Label VerticalAlignment="Center" HorizontalAlignment="Center" Align="Center"
               StyleClasses="LabelBig"
               Text="{Loc instruments-component-menu-no-midi-support}" />
    </PanelContainer>
</DefaultWindow>
