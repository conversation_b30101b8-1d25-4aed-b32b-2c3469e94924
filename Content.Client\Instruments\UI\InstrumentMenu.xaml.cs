// SPDX-FileCopyrightText: 2019 Víctor <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2020 DTanxxx <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Radrark <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Radrark <null>
// SPDX-FileCopyrightText: 2020 Víctor Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <andrea<PERSON>@kaemper.tech>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2024 0x6273 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hannah Giovanna Dawson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.IO;
using System.Numerics;
using Content.Client.Interactable;
using Content.Shared.ActionBlocker;
using Robust.Client.AutoGenerated;
using Robust.Client.Player;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Containers;
using Robust.Shared.Input;
using Robust.Shared.Timing;
using static Robust.Client.UserInterface.Controls.BaseButton;
using Range = Robust.Client.UserInterface.Controls.Range;

namespace Content.Client.Instruments.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class InstrumentMenu : DefaultWindow
    {
        [Dependency] private readonly IEntityManager _entManager = default!;
        [Dependency] private readonly IFileDialogManager _dialogs = default!;
        [Dependency] private readonly IPlayerManager _player = default!;

        private bool _isMidiFileDialogueWindowOpen;

        public event Action? OnOpenBand;
        public event Action? OnOpenChannels;
        public event Action? OnCloseBands;
        public event Action? OnCloseChannels;

        public EntityUid Entity;

        public InstrumentMenu()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            InputButton.OnToggled += MidiInputButtonOnOnToggled;
            BandButton.OnPressed += BandButtonOnPressed;
            BandButton.OnToggled += BandButtonOnToggled;
            FileButton.OnPressed += MidiFileButtonOnOnPressed;
            LoopButton.OnToggled += MidiLoopButtonOnOnToggled;
            ChannelsButton.OnPressed += ChannelsButtonOnPressed;
            StopButton.OnPressed += MidiStopButtonOnPressed;
            PlaybackSlider.OnValueChanged += PlaybackSliderSeek;
            PlaybackSlider.OnKeyBindUp += PlaybackSliderKeyUp;

            MinSize = SetSize = new Vector2(400, 150);
        }

        public void SetInstrument(Entity<InstrumentComponent> entity)
        {
            Entity = entity;
            var component = entity.Comp;
            component.OnMidiPlaybackEnded += InstrumentOnMidiPlaybackEnded;
            LoopButton.Disabled = !component.IsMidiOpen;
            LoopButton.Pressed = component.LoopMidi;
            ChannelsButton.Disabled = !component.IsRendererAlive;
            StopButton.Disabled = !component.IsMidiOpen;
            PlaybackSlider.MouseFilter = component.IsMidiOpen ? MouseFilterMode.Pass : MouseFilterMode.Ignore;
        }

        public void RemoveInstrument(InstrumentComponent component)
        {
            component.OnMidiPlaybackEnded -= InstrumentOnMidiPlaybackEnded;
        }

        public void SetMIDI(bool available)
        {
            UnavailableOverlay.Visible = !available;
        }

        private void BandButtonOnPressed(ButtonEventArgs obj)
        {
            if (!PlayCheck())
                return;

            OnOpenBand?.Invoke();
        }

        private void BandButtonOnToggled(ButtonToggledEventArgs obj)
        {
            if (obj.Pressed)
                return;

            if (_entManager.TryGetComponent(Entity, out InstrumentComponent? instrument))
            {
                _entManager.System<InstrumentSystem>().SetMaster(Entity, instrument.Master);
            }
        }

        private void ChannelsButtonOnPressed(ButtonEventArgs obj)
        {
            OnOpenChannels?.Invoke();
        }

        private void InstrumentOnMidiPlaybackEnded()
        {
            MidiPlaybackSetButtonsDisabled(true);
        }

        public void MidiPlaybackSetButtonsDisabled(bool disabled)
        {
            if (disabled)
            {
                OnCloseChannels?.Invoke();
            }

            LoopButton.Disabled = disabled;
            StopButton.Disabled = disabled;

            // Whether to allow the slider to receive events..
            PlaybackSlider.MouseFilter = !disabled ? MouseFilterMode.Pass : MouseFilterMode.Ignore;
        }

        private async void MidiFileButtonOnOnPressed(ButtonEventArgs obj)
        {
            if (_isMidiFileDialogueWindowOpen)
                return;

            OnCloseBands?.Invoke();

            var filters = new FileDialogFilters(new FileDialogFilters.Group("mid", "midi"));

            // TODO: Once the file dialogue manager can handle focusing or closing windows, improve this logic to close
            // or focus the previously-opened window.
            _isMidiFileDialogueWindowOpen = true;

            await using var file = await _dialogs.OpenFile(filters);

            _isMidiFileDialogueWindowOpen = false;

            // did the instrument menu get closed while waiting for the user to select a file?
            if (Disposed)
                return;

            // The following checks are only in place to prevent players from playing MIDI songs locally.
            // There are equivalents for these checks on the server.

            if (file == null)
                return;

            if (!PlayCheck())
                return;

            await using var memStream = new MemoryStream((int) file.Length);

            await file.CopyToAsync(memStream);

            if (!_entManager.TryGetComponent<InstrumentComponent>(Entity, out var instrument))
            {
                return;
            }

            if (!_entManager.System<InstrumentSystem>()
                    .OpenMidi(Entity,
                    memStream.GetBuffer().AsSpan(0, (int) memStream.Length),
                    instrument))
            {
                return;
            }

            MidiPlaybackSetButtonsDisabled(false);
            if (InputButton.Pressed)
                InputButton.Pressed = false;
        }

        private void MidiInputButtonOnOnToggled(ButtonToggledEventArgs obj)
        {
            OnCloseBands?.Invoke();

            if (obj.Pressed)
            {
                if (!PlayCheck())
                    return;

                MidiStopButtonOnPressed(null);

                if (_entManager.TryGetComponent(Entity, out InstrumentComponent? instrument))
                    _entManager.System<InstrumentSystem>().OpenInput(Entity, instrument);
            }
            else
            {
                _entManager.System<InstrumentSystem>().CloseInput(Entity, false);
                OnCloseChannels?.Invoke();
            }
        }

        private bool PlayCheck()
        {
            // TODO all of these checks should also be done server-side.
            if (!_entManager.TryGetComponent(Entity, out InstrumentComponent? instrument))
                return false;

            var localEntity = _player.LocalEntity;

            // If we don't have a player or controlled entity, we return.
            if (localEntity == null)
                return false;

            // By default, allow an instrument to play itself and skip all other checks
            if (localEntity == Entity)
                return true;

            var container = _entManager.System<SharedContainerSystem>();
            // If we're a handheld instrument, we might be in a container. Get it just in case.
            container.TryGetContainingContainer((Entity, null, null), out var conMan);

            // If the instrument is handheld and we're not holding it, we return.
            if (instrument.Handheld && (conMan == null || conMan.Owner != localEntity))
                return false;

            if (!_entManager.System<ActionBlockerSystem>().CanInteract(localEntity.Value, Entity))
                return false;

            // We check that we're in range unobstructed just in case.
            return _entManager.System<InteractionSystem>().InRangeUnobstructed(localEntity.Value, Entity);
        }

        private void MidiStopButtonOnPressed(ButtonEventArgs? obj)
        {
            MidiPlaybackSetButtonsDisabled(true);

            _entManager.System<InstrumentSystem>().CloseMidi(Entity, false);
            OnCloseChannels?.Invoke();
        }

        private void MidiLoopButtonOnOnToggled(ButtonToggledEventArgs obj)
        {
            var instrument = _entManager.System<InstrumentSystem>();

            if (_entManager.TryGetComponent(Entity, out InstrumentComponent? instrumentComp))
            {
                instrumentComp.LoopMidi = obj.Pressed;
            }

            instrument.UpdateRenderer(Entity);
        }

        private void PlaybackSliderSeek(Range _)
        {
            // Do not seek while still grabbing.
            if (PlaybackSlider.Grabbed)
                return;

            _entManager.System<InstrumentSystem>().SetPlayerTick(Entity, (int)Math.Ceiling(PlaybackSlider.Value));
        }

        private void PlaybackSliderKeyUp(GUIBoundKeyEventArgs args)
        {
            if (args.Function != EngineKeyFunctions.UIClick)
                return;

            _entManager.System<InstrumentSystem>().SetPlayerTick(Entity, (int)Math.Ceiling(PlaybackSlider.Value));
        }

        protected override void FrameUpdate(FrameEventArgs args)
        {
            base.FrameUpdate(args);

            if (!_entManager.TryGetComponent(Entity, out InstrumentComponent? instrument))
                return;

            var hasMaster = instrument.Master != null;
            BandButton.ToggleMode = hasMaster;
            BandButton.Pressed = hasMaster;
            BandButton.Disabled = instrument.IsMidiOpen || instrument.IsInputOpen;
            ChannelsButton.Disabled = !instrument.IsRendererAlive;

            if (!instrument.IsMidiOpen)
            {
                PlaybackSlider.MaxValue = 1;
                PlaybackSlider.SetValueWithoutEvent(0);
                return;
            }

            if (PlaybackSlider.Grabbed)
                return;

            PlaybackSlider.MaxValue = instrument.PlayerTotalTick;
            PlaybackSlider.SetValueWithoutEvent(instrument.PlayerTick);
        }
    }
}