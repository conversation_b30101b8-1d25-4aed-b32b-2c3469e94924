// SPDX-FileCopyrightText: 2019 DamianX <<EMAIL>>
// SPDX-FileCopyrightText: 2019 moneyl <<EMAIL>>
// SPDX-FileCopyrightText: 2020 Exp <<EMAIL>>
// SPDX-FileCopyrightText: 2020 PrPleGoo <<EMAIL>>
// SPDX-FileCopyrightText: 2020 PrPleGoo <<EMAIL>>
// SPDX-FileCopyrightText: 2020 chairbender <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 20kdc <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Veritius <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Miro Kavaliou <<EMAIL>>
// SPDX-FileCopyrightText: 2023 moonheart08 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Firewatch <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Ichaie <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 JORJ949 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 MortalBaguette <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Panela <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Poips <<EMAIL>>
// SPDX-FileCopyrightText: 2025 PuroSlavKing <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Solstice <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Whisper <<EMAIL>>
// SPDX-FileCopyrightText: 2025 blobadoodle <<EMAIL>>
// SPDX-FileCopyrightText: 2025 coderabbitai[bot] <136622811+coderabbitai[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 github-actions[bot] <********+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 kamkoi <<EMAIL>>
// SPDX-FileCopyrightText: 2025 shibe <<EMAIL>>
// SPDX-FileCopyrightText: 2025 tetra <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client._durkcode.ServerCurrency;
using Content.Client._RMC14.LinkAccount;
using Content.Client.Administration.Managers;
using Content.Client.Changelog;
using Content.Client.Chat.Managers;
using Content.Client.Clickable;
using Content.Client.DebugMon;
using Content.Client.Eui;
using Content.Client.Fullscreen;
using Content.Client.GameTicking.Managers;
using Content.Client.GhostKick;
using Content.Client.Guidebook;
using Content.Client.Launcher;
using Content.Client.Mapping;
using Content.Client.Parallax.Managers;
using Content.Client.Players.PlayTimeTracking;
using Content.Client.Replay;
using Content.Client.Screenshot;
using Content.Client.Stylesheets;
using Content.Client.Viewport;
using Content.Client.Voting;
using Content.Shared.Administration.Logs;
using Content.Client.Lobby;
using Content.Client.Players.RateLimiting;
using Content.Shared.Administration.Managers;
using Content.Shared.Chat;
using Content.Shared.Players.PlayTimeTracking;
using Content.Shared.Players.RateLimiting;

namespace Content.Client.IoC
{
    internal static class ClientContentIoC
    {
        public static void Register()
        {
            var collection = IoCManager.Instance!;

            collection.Register<IParallaxManager, ParallaxManager>();
            collection.Register<IChatManager, ChatManager>();
            collection.Register<ISharedChatManager, ChatManager>();
            collection.Register<IClientPreferencesManager, ClientPreferencesManager>();
            collection.Register<IStylesheetManager, StylesheetManager>();
            collection.Register<IScreenshotHook, ScreenshotHook>();
            collection.Register<FullscreenHook, FullscreenHook>();
            collection.Register<IClickMapManager, ClickMapManager>();
            collection.Register<IClientAdminManager, ClientAdminManager>();
            collection.Register<ISharedAdminManager, ClientAdminManager>();
            collection.Register<EuiManager, EuiManager>();
            collection.Register<IVoteManager, VoteManager>();
            collection.Register<ChangelogManager, ChangelogManager>();
            collection.Register<ViewportManager, ViewportManager>();
            collection.Register<ISharedAdminLogManager, SharedAdminLogManager>();
            collection.Register<GhostKickManager>();
            collection.Register<ExtendedDisconnectInformationManager>();
            collection.Register<JobRequirementsManager>();
            collection.Register<DocumentParsingManager>();
            collection.Register<ContentReplayPlaybackManager>();
            collection.Register<ISharedPlaytimeManager, JobRequirementsManager>();
            collection.Register<MappingManager>();
            collection.Register<DebugMonitorManager>();
            collection.Register<PlayerRateLimitManager>();
            collection.Register<SharedPlayerRateLimitManager, PlayerRateLimitManager>();
            collection.Register<TitleWindowManager>();
            collection.Register<ServerCurrencySystem>(); // Goob Station - Goob Coin
            collection.Register<LinkAccountManager>(); // RMC14
        }
    }
}