<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 DEATHB4DEFEAT <<EMAIL>>
SPDX-FileCopyrightText: 2024 Crotalus <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
            xmlns:ui="clr-namespace:Content.Client.Kitchen.UI"
            xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
            Title="{Loc grinder-menu-title}" MinSize="768 256">
    <BoxContainer Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" VerticalAlignment="Top" Margin="8" MinWidth="100">
            <Label Text="{Loc grinder-menu-auto-label}" HorizontalAlignment="Center"/>
            <Button Name="AutoModeButton" Text="{Loc grinder-menu-auto-button}" TextAlign="Center" MinSize="64 48" Margin="0 0 0 16" />
            <Label Text="{Loc grinder-menu-manual-label}" HorizontalAlignment="Center"/>
            <Button Name="GrindButton" Text="{Loc grinder-menu-grind-button}" TextAlign="Center" MinSize="64 48" Margin="0 0 0 16" />
            <Button Name="JuiceButton" Text="{Loc grinder-menu-juice-button}" TextAlign="Center" MinSize="64 48" />
        </BoxContainer>

        <ui:LabelledContentBox Name="ChamberContentBox" LabelText="{Loc grinder-menu-chamber-content-box-label}" ButtonText="{Loc grinder-menu-chamber-content-box-button}" VerticalExpand="True" HorizontalExpand="True" Margin="8" SizeFlagsStretchRatio="2"/>

        <ui:LabelledContentBox Name="BeakerContentBox" LabelText="{Loc grinder-menu-beaker-content-box-label}" ButtonText="{Loc grinder-menu-beaker-content-box-button}" VerticalExpand="True" HorizontalExpand="True" Margin="8" SizeFlagsStretchRatio="2"/>
    </BoxContainer>
</controls:FancyWindow>
