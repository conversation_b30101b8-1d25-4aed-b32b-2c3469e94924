// SPDX-FileCopyrightText: 2021 Paul <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON>k<PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;

namespace Content.Client.Kitchen.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class LabelledContentBox : BoxContainer
    {
        public string? LabelText { get => Label.Text; set => Label.Text = value; }
        public string? ButtonText { get => EjectButton.Text; set => EjectButton.Text = value; }
    }
}