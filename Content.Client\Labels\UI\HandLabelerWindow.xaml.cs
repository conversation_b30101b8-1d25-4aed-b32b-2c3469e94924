// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Watermelon914 <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Watermelon914 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Vordenburg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ElectroJr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ShadowCommander <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.UserInterface.CustomControls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Labels.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class HandLabelerWindow : DefaultWindow
    {
        public event Action<string>? OnLabelChanged;

        /// <summary>
        /// Is the user currently entering text into the control?
        /// </summary>
        private bool _focused;
        // TODO LineEdit Make this a bool on the LineEdit control

        private string _label = string.Empty;

        public HandLabelerWindow()
        {
            RobustXamlLoader.Load(this);

            LabelLineEdit.OnTextChanged += e =>
            {
                _label = e.Text;
                OnLabelChanged?.Invoke(_label);
            };

            LabelLineEdit.OnFocusEnter += _ => _focused = true;
            LabelLineEdit.OnFocusExit += _ =>
            {
                _focused = false;
                LabelLineEdit.Text = _label;
            };
        }

        protected override void Opened()
        {
            base.Opened();
            
            // Give the editor keyboard focus, since that's the only
            // thing the user will want to be doing with this UI
            LabelLineEdit.GrabKeyboardFocus();
        }

        public void SetCurrentLabel(string label)
        {
            if (label == _label)
                return;

            _label = label;
            if (!_focused)
                LabelLineEdit.Text = label;
        }

        public void SetMaxLabelLength(int maxLength)
        {
            LabelLineEdit.IsValid = s => s.Length <= maxLength;
        }
    }
}