<!--
SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
SPDX-FileCopyrightText: 2023 Thom <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2023 chromiumboy <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
SPDX-FileCopyrightText: 2024 eoineoi<PERSON><PERSON>n <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Rouden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:ui="clr-namespace:Content.Client.Materials.UI"
    Title="{Loc 'lathe-menu-title'}"
    MinSize="550 450"
    SetSize="750 500">
    <BoxContainer
        Orientation="Horizontal"
        VerticalExpand="True"
        HorizontalExpand="True"
        SeparationOverride="5">
        <!-- Left Col-->
        <BoxContainer
            VerticalExpand="True"
            HorizontalExpand="True">
            <BoxContainer
                Orientation="Vertical"
                VerticalExpand="True"
                HorizontalExpand="True"
                SeparationOverride="5">
                <BoxContainer Orientation="Horizontal"
                    HorizontalExpand="True">
                    <LineEdit
                        Name="SearchBar"
                        PlaceHolder="{Loc 'lathe-menu-search-designs'}"
                        HorizontalExpand="True">
                    </LineEdit>
                    <OptionButton
                        Name="FilterOption"
                        MinWidth="100"
                        StyleClasses="ButtonSquare"/>
                </BoxContainer>
                <BoxContainer Orientation="Vertical"
                    MinHeight="225"
                    VerticalExpand="True"
                    HorizontalExpand="True"
                    SizeFlagsStretchRatio="4">
                    <PanelContainer VerticalExpand="True">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                        </PanelContainer.PanelOverride>
                        <ScrollContainer VerticalExpand="True" HScrollEnabled="False">
                            <BoxContainer
                                Name="RecipeList"
                                Orientation="Vertical"
                                HorizontalExpand="True"
                                VerticalExpand="True"
                                RectClipContent="True">
                            </BoxContainer>
                        </ScrollContainer>
                    </PanelContainer>
                </BoxContainer>
                <BoxContainer Orientation="Horizontal"
                    HorizontalExpand="True">
                    <Label Margin="8 0 8 0" Text="{Loc 'lathe-menu-amount'}" />
                    <LineEdit
                        Name="AmountLineEdit"
                        PlaceHolder="0"
                        Text="1"
                        HorizontalExpand="True" />
                    <Label Name="RecipeCount" Margin="8 0 8 0" MinWidth="90" Align="Right" />
                </BoxContainer>
            </BoxContainer>
        </BoxContainer>
        <BoxContainer MinWidth="5"/>
        <!-- Right Col-->
        <BoxContainer
            Orientation="Vertical"
            VerticalExpand="True"
            HorizontalExpand="True"
            SeparationOverride="5">
            <BoxContainer Orientation="Vertical" MinHeight="225">
                <BoxContainer
                    Orientation="Horizontal"
                    Align="End"
                    HorizontalExpand="True">
                    <Button
                        Name="ServerListButton"
                        Text="{Loc 'lathe-menu-server-list'}"
                        TextAlign="Center"
                        Mode="Press">
                    </Button>
                </BoxContainer>
                <BoxContainer
                    Orientation="Horizontal"
                    Align="Begin"
                    HorizontalExpand="True">
                    <Label Text="{Loc 'lathe-menu-queue-title'}" Margin="5 5 5 5" HorizontalAlignment="Left"/>
                    <Button
                        Name="ResetQueueList"
                        Margin="5 5 5 5"
                        Text="{Loc 'lathe-menu-queue-reset-title'}"
                        HorizontalAlignment="Right"
                        Mode="Press">
                    </Button>
                </BoxContainer>
                <BoxContainer
                    Name="FabricatingContainer"
                    Orientation="Horizontal"
                    HorizontalExpand="True"
                    SizeFlagsStretchRatio="2">
                    <PanelContainer
                        VerticalExpand="True"
                        HorizontalExpand="True"
                        SizeFlagsStretchRatio="3">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BackgroundColor="#408040" />
                        </PanelContainer.PanelOverride>
                        <Label
                            RectClipContent="False"
                            HorizontalAlignment="Left"
                            Margin="5 0 0 0"
                            Text="{Loc 'lathe-menu-fabricating-message'}">
                        </Label>
                        <BoxContainer Name="FabricatingDisplayContainer"
                                      HorizontalAlignment="Left"
                                      Margin="100 0 0 0"/>
                        <Label
                            Name="NameLabel"
                            RectClipContent="True"
                            HorizontalAlignment="Left"
                            Margin="130 0 0 0">
                        </Label>
                    </PanelContainer>
                </BoxContainer>
                <ScrollContainer VerticalExpand="True" HScrollEnabled="False">
                    <BoxContainer
                        Name="QueueList"
                        Orientation="Vertical"
                        HorizontalExpand="True"
                        VerticalExpand="True"
                        RectClipContent="True">
                    </BoxContainer>
                </ScrollContainer>
            </BoxContainer>
            <BoxContainer
            VerticalExpand="True"
            HorizontalExpand="True"
            Orientation="Vertical">
            <Label Text="{Loc 'lathe-menu-materials-title'}" Margin="5 5 5 5" HorizontalAlignment="Center"/>
            <BoxContainer
                Orientation="Vertical"
                VerticalExpand="True"
                HorizontalExpand="True">
                <ui:MaterialStorageControl Name="MaterialsList" SizeFlagsStretchRatio="8"/>
            </BoxContainer>
            <!-- Begin DeltaV Additions: Mining points -->
            <BoxContainer Orientation="Vertical" Name="MiningPointsContainer" Visible="False">
                <BoxContainer Orientation="Horizontal">
                    <Label Name="MiningPointsLabel" HorizontalExpand="True"/>
                    <Button Name="MiningPointsClaimButton" Text="{Loc 'lathe-menu-mining-points-claim-button'}"/>
                </BoxContainer>
                <RichTextLabel Name="MiningPointsNoConnectionWarning"></RichTextLabel>
            </BoxContainer>
            <!-- End DeltaV Additions: Mining points -->
        </BoxContainer>
        </BoxContainer>

    </BoxContainer>

</DefaultWindow>
