// SPDX-FileCopyrightText: 2022 Eoi<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Justin <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Thom <<EMAIL>>
// SPDX-FileCopyrightText: 2023 chromiumboy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alice "Arimah" He<PERSON>lin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Crotalus <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 HS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rouge2t7 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Tayrtahn <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Truoizys <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Арт <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aineias1 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 FaDeOkno <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 McBosserson <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Rouden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX-7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Soup-Byte07 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TheBorzoiMustConsume <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Tobias Berger <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Unlumination <<EMAIL>>
// SPDX-FileCopyrightText: 2025 coderabbitai[bot] <136622811+coderabbitai[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 username <<EMAIL>>
// SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
// SPDX-FileCopyrightText: 2025 āda <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Text;
using Content.Client.Materials;
using Content.Shared._DV.Salvage.Components; // DeltaV
using Content.Shared._DV.Salvage.Systems; // DeltaV
using Content.Client._Shitcode.Silo; // Goobstation
using Content.Shared.Lathe;
using Content.Shared.Lathe.Prototypes;
using Content.Shared.Research.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Player; // DeltaV
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;
using Robust.Shared.Physics; // DeltaV
using Robust.Shared.Utility;
namespace Content.Client.Lathe.UI;

[GenerateTypedNameReferences]
public sealed partial class LatheMenu : DefaultWindow
{
    [Dependency] private readonly IEntityManager _entityManager = default!;
    [Dependency] private readonly IPlayerManager _player = default!; // DeltaV
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;

    private readonly SpriteSystem _spriteSystem;
    private readonly LatheSystem _lathe;
    private readonly MaterialStorageSystem _materialStorage;
    private readonly MiningPointsSystem _miningPoints; // DeltaV
    private readonly SiloSystem _silo; // Goobstation
    public event Action<BaseButton.ButtonEventArgs>? OnServerListButtonPressed;
    public event Action<string, int>? RecipeQueueAction;
    public event Action<BaseButton.ButtonEventArgs>? OnResetQueueListButtonPressed; // Goobstation
    public event Action? OnClaimMiningPoints; // DeltaV

    public List<ProtoId<LatheRecipePrototype>> Recipes = new();

    public List<ProtoId<LatheCategoryPrototype>>? Categories;

    public ProtoId<LatheCategoryPrototype>? CurrentCategory;

    public EntityUid Entity;

    private uint? _lastMiningPoints; // DeltaV: used to avoid Loc.GetString every frame

    public LatheMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _spriteSystem = _entityManager.System<SpriteSystem>();
        _lathe = _entityManager.System<LatheSystem>();
        _materialStorage = _entityManager.System<MaterialStorageSystem>();
        _miningPoints = _entityManager.System<MiningPointsSystem>(); // DeltaV
        _silo = _entityManager.System<SiloSystem>(); // Goobstation

        SearchBar.OnTextChanged += _ =>
        {
            PopulateRecipes();
        };
        AmountLineEdit.OnTextChanged += _ =>
        {
            PopulateRecipes();
        };

        FilterOption.OnItemSelected += OnItemSelected;

        ServerListButton.OnPressed += a => OnServerListButtonPressed?.Invoke(a);
        ResetQueueList.OnPressed += a => OnResetQueueListButtonPressed?.Invoke(a); // Goobstation

    }

    public void SetEntity(EntityUid uid)
    {
        Entity = uid;

        if (_entityManager.TryGetComponent<LatheComponent>(Entity, out var latheComponent))
        {
            if (!latheComponent.DynamicPacks.Any())
            {
                ServerListButton.Visible = false;
            }

            AmountLineEdit.SetText(latheComponent.DefaultProductionAmount.ToString());
        }

        // Begin DeltaV Additions: Mining points UI
        MiningPointsContainer.Visible = _entityManager.TryGetComponent<MiningPointsComponent>(Entity, out var points);
        MiningPointsClaimButton.OnPressed += _ => OnClaimMiningPoints?.Invoke();

        if (points != null)
        {
            UpdateMiningPoints(points.Points);
            if (!IsSiloConnected(Entity, out var warning, true))
            {
                MiningPointsNoConnectionWarning.Visible = true;

                if (warning != null)
                    MiningPointsNoConnectionWarning.SetMessage(FormattedMessage.FromMarkupOrThrow(warning));
            }
        }

        MaterialsList.SetOwner(Entity);
        // End DeltaV Additions
    }

    /// <summary>
    /// DeltaV: Updates the UI elements for mining points.
    /// </summary>
    private void UpdateMiningPoints(uint points)
    {
        MiningPointsClaimButton.Disabled = points == 0 ||
            _player.LocalSession?.AttachedEntity is not { } player ||
            _miningPoints.TryFindIdCard(player) == null;
        if (points == _lastMiningPoints)
            return;

        _lastMiningPoints = points;
        MiningPointsLabel.Text = Loc.GetString("lathe-menu-mining-points", ("points", points));
    }

    /// <summary>
    /// Goobstation: Check if the lathe is connected to a silo.
    /// </summary>
    private bool IsSiloConnected(EntityUid uid, out string? warning, bool checkGrid = false)
    {
        warning = null;
        var silo = _silo.GetSilo(uid);
        if (silo != null
            && checkGrid)
        {
            if (_entityManager.TryGetComponent<TransformComponent>(uid, out var uidTransform)
                && _entityManager.TryGetComponent<TransformComponent>(silo.Value, out var siloTransform))
            {
                if (uidTransform.MapID != siloTransform.MapID)
                {
                    warning = Loc.GetString("lathe-menu-mining-points-silo-not-on-same-grid");
                    return false;
                }

                return true;
            }

            warning = Loc.GetString("lathe-menu-mining-points-silo-not-on-same-grid");
            return false;
        }

        if (silo == null)
            warning = Loc.GetString("lathe-menu-mining-points-no-connection-warning");

        return silo != null;
    }

    /// <summary>
    /// DeltaV: Update mining points UI whenever it changes.
    /// </summary>
    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_entityManager.TryGetComponent<MiningPointsComponent>(Entity, out var points))
            UpdateMiningPoints(points.Points);
    }

    /// <summary>
    /// Populates the list of all the recipes
    /// </summary>
    public void PopulateRecipes()
    {
        var recipesToShow = new List<LatheRecipePrototype>();
        foreach (var recipe in Recipes)
        {
            if (!_prototypeManager.TryIndex(recipe, out var proto))
                continue;

            // Category filtering
            if (CurrentCategory != null)
            {
                if (proto.Categories.Count <= 0)
                    continue;

                var validRecipe = proto.Categories.Any(category => category == CurrentCategory);

                if (!validRecipe)
                    continue;
            }

            if (SearchBar.Text.Trim().Length != 0)
            {
                if (_lathe.GetRecipeName(recipe).ToLowerInvariant().Contains(SearchBar.Text.Trim().ToLowerInvariant()))
                    recipesToShow.Add(proto);
            }
            else
            {
                recipesToShow.Add(proto);
            }
        }

        if (!int.TryParse(AmountLineEdit.Text, out var quantity) || quantity <= 0)
            quantity = 1;

        RecipeCount.Text = Loc.GetString("lathe-menu-recipe-count", ("count", recipesToShow.Count));

        var sortedRecipesToShow = recipesToShow.OrderBy(_lathe.GetRecipeName);
        RecipeList.Children.Clear();
        _entityManager.TryGetComponent(Entity, out LatheComponent? lathe);

        foreach (var prototype in sortedRecipesToShow)
        {
            var canProduce = _lathe.CanProduce(Entity, prototype, quantity, component: lathe);

            var control = new RecipeControl(_lathe, prototype, () => GenerateTooltipText(prototype), canProduce, GetRecipeDisplayControl(prototype));
            control.OnButtonPressed += s =>
            {
                if (!int.TryParse(AmountLineEdit.Text, out var amount) || amount <= 0)
                    amount = 1;
                RecipeQueueAction?.Invoke(s, amount);
            };
            RecipeList.AddChild(control);
        }
    }

    private string GenerateTooltipText(LatheRecipePrototype prototype)
    {
        StringBuilder sb = new();
        var multiplier = _entityManager.GetComponent<LatheComponent>(Entity).MaterialUseMultiplier;

        foreach (var (id, amount) in prototype.Materials)
        {
            if (!_prototypeManager.TryIndex(id, out var proto))
                continue;

            var adjustedAmount = SharedLatheSystem.AdjustMaterial(amount, prototype.ApplyMaterialDiscount, multiplier);
            var sheetVolume = _materialStorage.GetSheetVolume(proto);

            var unit = Loc.GetString(proto.Unit);
            var sheets = adjustedAmount / (float) sheetVolume;

            var availableAmount = _materialStorage.GetMaterialAmount(Entity, id);
            var missingAmount = Math.Max(0, adjustedAmount - availableAmount);
            var missingSheets = missingAmount / (float) sheetVolume;

            var name = Loc.GetString(proto.Name);

            string tooltipText;
            if (missingSheets > 0)
            {
                tooltipText = Loc.GetString("lathe-menu-material-amount-missing", ("amount", sheets), ("missingAmount", missingSheets), ("unit", unit), ("material", name));
            }
            else
            {
                var amountText = Loc.GetString("lathe-menu-material-amount", ("amount", sheets), ("unit", unit));
                tooltipText = Loc.GetString("lathe-menu-tooltip-display", ("material", name), ("amount", amountText));
            }

            sb.AppendLine(tooltipText);
        }

        var desc = _lathe.GetRecipeDescription(prototype);
        if (!string.IsNullOrWhiteSpace(desc))
            sb.AppendLine(Loc.GetString("lathe-menu-description-display", ("description", desc)));

        // Remove last newline
        if (sb.Length > 0)
            sb.Remove(sb.Length - 1, 1);

        return sb.ToString();
    }

    public void UpdateCategories()
    {
        // Get categories from recipes
        var currentCategories = new List<ProtoId<LatheCategoryPrototype>>();
        foreach (var recipeId in Recipes)
        {
            var recipe = _prototypeManager.Index(recipeId);

            if (recipe.Categories.Count <= 0)
                continue;

            foreach (var category in recipe.Categories)
            {
                if (currentCategories.Contains(category))
                    continue;

                currentCategories.Add(category);
            }
        }

        if (Categories != null && (Categories.Count == currentCategories.Count || !Categories.All(currentCategories.Contains)))
            return;

        Categories = currentCategories;
        var sortedCategories = currentCategories
            .Select(p => _prototypeManager.Index(p))
            .OrderBy(p => Loc.GetString(p.Name))
            .ToList();

        FilterOption.Clear();
        FilterOption.AddItem(Loc.GetString("lathe-menu-category-all"), -1);
        foreach (var category in sortedCategories)
        {
            FilterOption.AddItem(Loc.GetString(category.Name), Categories.IndexOf(category.ID));
        }

        FilterOption.SelectId(-1);
    }

    /// <summary>
    /// Populates the build queue list with all queued items
    /// </summary>
    /// <param name="queue"></param>
    public void PopulateQueueList(List<LatheRecipePrototype> queue)
    {
        QueueList.DisposeAllChildren();

        var idx = 1;
        foreach (var recipe in queue)
        {
            var queuedRecipeBox = new BoxContainer();
            queuedRecipeBox.Orientation = BoxContainer.LayoutOrientation.Horizontal;

            queuedRecipeBox.AddChild(GetRecipeDisplayControl(recipe));

            var queuedRecipeLabel = new Label();
            queuedRecipeLabel.Text = $"{idx}. {_lathe.GetRecipeName(recipe)}";
            queuedRecipeBox.AddChild(queuedRecipeLabel);
            QueueList.AddChild(queuedRecipeBox);
            idx++;
        }
    }

    public void SetQueueInfo(LatheRecipePrototype? recipe)
    {
        FabricatingContainer.Visible = recipe != null;
        if (recipe == null)
            return;

        FabricatingDisplayContainer.Children.Clear();
        FabricatingDisplayContainer.AddChild(GetRecipeDisplayControl(recipe));

        NameLabel.Text = _lathe.GetRecipeName(recipe);
    }

    public Control GetRecipeDisplayControl(LatheRecipePrototype recipe)
    {
        if (recipe.Icon != null)
        {
            var textRect = new TextureRect();
            textRect.Texture = _spriteSystem.Frame0(recipe.Icon);
            return textRect;
        }

        if (recipe.Result is { } result)
        {
            var entProtoView = new EntityPrototypeView();
            entProtoView.SetPrototype(result);
            return entProtoView;
        }

        return new Control();
    }

    private void OnItemSelected(OptionButton.ItemSelectedEventArgs obj)
    {
        FilterOption.SelectId(obj.Id);
        if (obj.Id == -1)
        {
            CurrentCategory = null;
        }
        else
        {
            CurrentCategory = Categories?[obj.Id];
        }
        PopulateRecipes();
    }
}
