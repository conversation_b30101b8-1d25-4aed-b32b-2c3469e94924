// SPDX-FileCopyrightText: 2023 Justin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> "<PERSON>mah" <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Crotalus <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 HS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rouge2t7 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Tayrtahn <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Truoizys <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Арт <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Research.Prototypes;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lathe.UI;

[GenerateTypedNameReferences]
public sealed partial class RecipeControl : Control
{
    public Action<string>? OnButtonPressed;
    public Func<string> TooltipTextSupplier;

    public RecipeControl(LatheSystem latheSystem, LatheRecipePrototype recipe, Func<string> tooltipTextSupplier, bool canProduce, Control displayControl)
    {
        RobustXamlLoader.Load(this);

        RecipeName.Text = latheSystem.GetRecipeName(recipe);
        RecipeDisplayContainer.AddChild(displayControl);
        Button.Disabled = !canProduce;
        TooltipTextSupplier = tooltipTextSupplier;
        Button.TooltipSupplier = SupplyTooltip;

        Button.OnPressed += (_) =>
        {
            OnButtonPressed?.Invoke(recipe.ID);
        };
    }

    private Control? SupplyTooltip(Control sender)
    {
        return new RecipeTooltip(TooltipTextSupplier());
    }
}