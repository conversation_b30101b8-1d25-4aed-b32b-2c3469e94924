// SPDX-FileCopyrightText: 2023 Justin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Client.Message;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lathe.UI;

[GenerateTypedNameReferences]
public sealed partial class RecipeTooltip : Control
{
    public RecipeTooltip(string tooltip)
    {
        RobustXamlLoader.Load(this);

        RecipeTooltipLabel.SetMarkup(tooltip);
    }
}