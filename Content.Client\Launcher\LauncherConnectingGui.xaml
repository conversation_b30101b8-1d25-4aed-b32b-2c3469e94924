<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:parallax="clr-namespace:Content.Client.Parallax"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls">
    <parallax:ParallaxControl SpeedX="20"/>
    <Control HorizontalAlignment="Center" VerticalAlignment="Center">
        <PanelContainer StyleClasses="AngleRect" />
        <BoxContainer Orientation="Vertical" MinSize="300 200">
            <BoxContainer Orientation="Horizontal">
                <Label Margin="8 0 0 0" Text="{Loc 'connecting-title'}"
                       StyleClasses="LabelHeading" VAlign="Center" />
                <Button Name="ExitButton" Text="{Loc 'connecting-exit'}"
                        HorizontalAlignment="Right" HorizontalExpand="True" />
            </BoxContainer>
            <controls:HighDivider />
            <BoxContainer Orientation="Vertical" VerticalExpand="True" Margin="4 4 4 0">
                <Control VerticalExpand="True" Margin="0 0 0 8">
                    <BoxContainer Orientation="Vertical" Name="ConnectingStatus">
                        <Label Text="{Loc 'connecting-in-progress'}" Align="Center" />
                        <Label Name="ConnectStatus" StyleClasses="LabelSubText" Align="Center" />
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" Name="ConnectFail" Visible="False" SeparationOverride="10">
                        <RichTextLabel Name="ConnectFailReason" VerticalAlignment="Stretch"/>
                        <BoxContainer Orientation="Horizontal" Align="Center">
                            <Button Name="RetryButton"
                                    Text="{Loc 'connecting-retry'}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Bottom"
                                    StyleClasses="OpenRight"/>
                            <Button Name="CopyButton"
                                    Text="{Loc 'connecting-copy'}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Bottom"
                                    StyleClasses="OpenLeft"/>
                        </BoxContainer>
                    </BoxContainer>
                    <BoxContainer Orientation="Vertical" Name="Disconnected" Visible="False" SeparationOverride="10">
                        <Label Text="{Loc 'connecting-disconnected'}" Align="Center" />
                        <Label Name="DisconnectReason" Align="Center" />
                        <BoxContainer Orientation="Horizontal" Align="Center" VerticalAlignment="Bottom">
                            <Button Name="ReconnectButton"
                                    Text="{Loc 'connecting-reconnect'}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Bottom"
                                    StyleClasses="OpenRight"/>
                            <Button Name="CopyButtonDisconnected"
                                    Text="{Loc 'connecting-copy'}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Bottom"
                                    StyleClasses="OpenLeft"/>
                        </BoxContainer>
                    </BoxContainer>
                </Control>
                <Label Name="ConnectingAddress" StyleClasses="LabelSubText" HorizontalAlignment="Center" />
            </BoxContainer>
            <PanelContainer>
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#444" ContentMarginTopOverride="2" />
                </PanelContainer.PanelOverride>
            </PanelContainer>

            <BoxContainer Orientation="Horizontal" Margin="12 0 4 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'connecting-tip'}" StyleClasses="LabelSubText" />
                <Label Text="{Loc 'connecting-version'}" StyleClasses="LabelSubText"
                       HorizontalAlignment="Right" HorizontalExpand="True" />
            </BoxContainer>
        </BoxContainer>
    </Control>
    <!-- Bottom window for tips -->
    <PanelContainer Name="LoginTips" StyleClasses="AngleRect" Margin="0 10" MaxWidth="600" VerticalExpand="True" VerticalAlignment="Bottom">
        <BoxContainer Orientation="Vertical" VerticalExpand="True">
            <controls:StripeBack>
                <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center">
                    <Label Name="LoginTipTitle" Text="Tip" StyleClasses="LabelHeading" Align="Center"/>
                </BoxContainer>
            </controls:StripeBack>
            <BoxContainer Orientation="Vertical" Margin="5 5 5 5" >
                <RichTextLabel Name="LoginTip" VerticalExpand="True" />
            </BoxContainer>
        </BoxContainer>
    </PanelContainer>
</Control>
