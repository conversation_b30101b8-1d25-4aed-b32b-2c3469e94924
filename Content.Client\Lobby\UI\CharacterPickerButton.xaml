<!--
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Absotively <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<ContainerButton xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:style="clr-namespace:Content.Client.Stylesheets">
    <BoxContainer Orientation="Horizontal"
                  HorizontalExpand="True"
                  SeparationOverride="0"
                  Name="InternalHBox">
        <SpriteView Scale="2 2"
                    Margin="0 4 4 4"
                    OverrideDirection="South"
                    Name="View"
                    SetSize="64 64"/>
        <Label Name="DescriptionLabel"
               ClipText="True"
               HorizontalExpand="True"/>
        <Button Name="DeleteButton"
                Text="{Loc 'character-setup-gui-character-picker-button-delete-button'}"/>
        <Button Name="ConfirmDeleteButton"
                Text="{Loc 'character-setup-gui-character-picker-button-confirm-delete-button'}"
                Visible="False"
                ModulateSelfOverride="{x:Static style:StyleNano.ButtonColorCautionDefault}"/>

    </BoxContainer>
</ContainerButton>
