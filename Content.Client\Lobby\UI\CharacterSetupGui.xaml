<!--
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flip<PERSON>yder <<EMAIL>>
SPDX-FileCopyrightText: 2023 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         xmlns:style="clr-namespace:Content.Client.Stylesheets"
         xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
         VerticalExpand="True">
    <Control>
        <PanelContainer Name="BackgroundPanel" />
        <BoxContainer Orientation="Vertical" SeparationOverride="0">
            <BoxContainer Orientation="Horizontal" MinSize="0 40">
                <Label Text="{Loc 'character-setup-gui-character-setup-label'}"
                       Margin="8 0 0 0" VAlign="Center"
                       StyleClasses="LabelHeadingBigger" />

                <Button Name="StatsButton" HorizontalExpand="True"
                        Text="{Loc 'character-setup-gui-character-setup-stats-button'}"
                        StyleClasses="ButtonBig"
                        HorizontalAlignment="Right" />
                <cc:CommandButton Name="AdminRemarksButton"
                                  Command="adminremarks"
                                  Text="{Loc 'character-setup-gui-character-setup-adminremarks-button'}"
                                  StyleClasses="ButtonBig" />
                <Button Name="RulesButton"
                        Text="{Loc 'character-setup-gui-character-setup-rules-button'}"
                        StyleClasses="ButtonBig"/>
                <Button Name="CloseButton"
                        Access="Public"
                        Text="{Loc 'character-setup-gui-character-setup-close-button'}"
                        StyleClasses="ButtonBig"/>
            </BoxContainer>
            <PanelContainer>
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="{x:Static style:StyleNano.NanoGold}" ContentMarginTopOverride="2" />
                </PanelContainer.PanelOverride>
            </PanelContainer>
            <BoxContainer Orientation="Horizontal" VerticalExpand="True" SeparationOverride="0">
                <ScrollContainer MinSize="325 0" Margin="5 5 0 0">
                    <BoxContainer Name="Characters" Orientation="Vertical" />
                </ScrollContainer>
                <PanelContainer MinSize="2 0">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="{x:Static style:StyleNano.NanoGold}" ContentMarginTopOverride="2" />
                    </PanelContainer.PanelOverride>
                </PanelContainer>
                <BoxContainer Name="CharEditor" HorizontalExpand="True" />
            </BoxContainer>
        </BoxContainer>
    </Control>
</Control>
