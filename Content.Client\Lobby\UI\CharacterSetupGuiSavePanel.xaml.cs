// SPDX-FileCopyrightText: 2024 Winkarst-cpu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lobby.UI;

[GenerateTypedNameReferences]
public sealed partial class CharacterSetupGuiSavePanel : DefaultWindow
{
    public CharacterSetupGuiSavePanel()
    {
        RobustXamlLoader.Load(this);

        CancelButton.OnPressed += _ =>
        {
            Close();
        };

        CloseButton.Visible = false;
    }
}