// SPDX-FileCopyrightText: 2021 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Firewatch <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lobby.UI;

[GenerateTypedNameReferences]
public sealed partial class HighlightedContainer : PanelContainer
{
    public HighlightedContainer()
    {
        RobustXamlLoader.Load(this);
    }
}