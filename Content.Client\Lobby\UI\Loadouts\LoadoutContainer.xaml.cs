// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Firewatch <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Clothing;
using Content.Shared.Preferences.Loadouts;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Map;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;

namespace Content.Client.Lobby.UI.Loadouts;

[GenerateTypedNameReferences]
public sealed partial class LoadoutContainer : BoxContainer
{
    [Dependency] private readonly IEntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _protoManager = default!;

    private readonly EntityUid? _entity;

    public Button Select => SelectButton;

    public LoadoutContainer(ProtoId<LoadoutPrototype> proto, bool disabled, FormattedMessage? reason)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        SelectButton.Disabled = disabled;

        if (disabled && reason != null)
        {
            var tooltip = new Tooltip();
            tooltip.SetMessage(reason);
            SelectButton.TooltipSupplier = _ => tooltip;
        }

        if (_protoManager.TryIndex(proto, out var loadProto))
        {
            var ent = loadProto.DummyEntity ?? _entManager.System<LoadoutSystem>().GetFirstOrNull(loadProto);

            if (ent == null)
                return;

            _entity = _entManager.SpawnEntity(ent, MapCoordinates.Nullspace);
            Sprite.SetEntity(_entity);

            var spriteTooltip = new Tooltip();
            spriteTooltip.SetMessage(FormattedMessage.FromUnformatted(_entManager.GetComponent<MetaDataComponent>(_entity.Value).EntityDescription));

            TooltipSupplier = _ => spriteTooltip;
        }
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);

        if (!disposing)
            return;

        _entManager.DeleteEntity(_entity);
    }

    public bool Pressed
    {
        get => SelectButton.Pressed;
        set => SelectButton.Pressed = value;
    }

    public string? Text
    {
        get => SelectButton.Text;
        set => SelectButton.Text = value;
    }
}