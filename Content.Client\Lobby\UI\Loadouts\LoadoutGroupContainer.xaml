<!--
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<BoxContainer xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         Orientation="Vertical">
    <PanelContainer StyleClasses="AngleRect" HorizontalExpand="True">
        <BoxContainer Name="LoadoutsContainer" Orientation="Vertical"/>
    </PanelContainer>
    <!-- Buffer space so we have 10 margin between controls but also 10 to the borders -->
    <Label Text="{Loc 'loadout-restrictions'}" Margin="5 0 5 5"/>
    <BoxContainer Name="RestrictionsContainer" Orientation="Vertical" HorizontalExpand="True" />
</BoxContainer>
