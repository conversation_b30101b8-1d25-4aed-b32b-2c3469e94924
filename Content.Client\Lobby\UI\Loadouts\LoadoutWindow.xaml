<!--
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Tayrtahn <<EMAIL>>
SPDX-FileCopyrightText: 2025 lzk <<EMAIL>>
SPDX-FileCopyrightText: 2025 metalgearsloth <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
         xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
         SetSize="800 800"
         MinSize="800 128">
    <BoxContainer Orientation="Vertical" VerticalExpand="True">
        <BoxContainer Name="RoleNameBox" Orientation="Vertical" Margin="10">
            <Label Name="LoadoutNameLabel"/>
            <PanelContainer HorizontalExpand="True" SetHeight="24">
                <PanelContainer.PanelOverride>
                    <graphics:StyleBoxFlat BackgroundColor="#1B1B1E" />
                </PanelContainer.PanelOverride>
                <LineEdit Name="RoleNameEdit" VerticalExpand="True" HorizontalExpand="True"/>
            </PanelContainer>
        </BoxContainer>
        <VerticalTabContainer Name="LoadoutGroupsContainer"
                              VerticalExpand="True"
                              HorizontalExpand="True">
        </VerticalTabContainer>
    </BoxContainer>
</controls:FancyWindow>
