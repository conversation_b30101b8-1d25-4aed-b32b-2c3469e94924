// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alice "Arimah" <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Firewatch <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 HS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rouge2t7 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Truoizys <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 coolboy911 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 lunarcomets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Арт <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Tayrtahn <<EMAIL>>
// SPDX-FileCopyrightText: 2025 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2025 metalgearsloth <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.UserInterface.Controls;
using Content.Shared.Dataset;
using Content.Shared.Preferences;
using Content.Shared.Preferences.Loadouts;
using Content.Shared.Random.Helpers;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Player;
using Robust.Shared.Prototypes;

namespace Content.Client.Lobby.UI.Loadouts;

[GenerateTypedNameReferences]
public sealed partial class LoadoutWindow : FancyWindow
{
    public event Action<string>? OnNameChanged;
    public event Action<ProtoId<LoadoutGroupPrototype>, ProtoId<LoadoutPrototype>>? OnLoadoutPressed;
    public event Action<ProtoId<LoadoutGroupPrototype>, ProtoId<LoadoutPrototype>>? OnLoadoutUnpressed;

    private List<LoadoutGroupContainer> _groups = new();

    public HumanoidCharacterProfile Profile;

    public LoadoutWindow(HumanoidCharacterProfile profile, RoleLoadout loadout, RoleLoadoutPrototype proto, ICommonSession session, IDependencyCollection collection)
    {
        RobustXamlLoader.Load(this);
        Profile = profile;
        var protoManager = collection.Resolve<IPrototypeManager>();
        RoleNameEdit.IsValid = text => text.Length <= HumanoidCharacterProfile.MaxLoadoutNameLength;

        // Hide if we can't edit the name.
        if (!proto.CanCustomizeName)
        {
            RoleNameBox.Visible = false;
        }
        else
        {
            var name = loadout.EntityName;

            LoadoutNameLabel.Text = proto.NameDataset == null ?
                Loc.GetString("loadout-name-edit-label") :
                Loc.GetString("loadout-name-edit-label-dataset");

            RoleNameEdit.ToolTip = Loc.GetString(
                "loadout-name-edit-tooltip",
                ("max", HumanoidCharacterProfile.MaxLoadoutNameLength));
            RoleNameEdit.Text = name ?? string.Empty;
            RoleNameEdit.OnTextChanged += args => OnNameChanged?.Invoke(args.Text);
        }

        // Hide if no groups
        if (proto.Groups.Count == 0)
        {
            LoadoutGroupsContainer.Visible = false;
            SetSize = Vector2.Zero;
        }
        else
        {
            foreach (var group in proto.Groups)
            {
                if (!protoManager.TryIndex(group, out var groupProto))
                    continue;

                if (groupProto.Hidden)
                    continue;

                var container = new LoadoutGroupContainer(profile, loadout, protoManager.Index(group), session, collection);
                LoadoutGroupsContainer.AddTab(container, Loc.GetString(groupProto.Name));
                _groups.Add(container);

                container.OnLoadoutPressed += args =>
                {
                    OnLoadoutPressed?.Invoke(group, args);
                };

                container.OnLoadoutUnpressed += args =>
                {
                    OnLoadoutUnpressed?.Invoke(group, args);
                };
            }
        }
    }

    public void RefreshLoadouts(RoleLoadout loadout, ICommonSession session, IDependencyCollection collection)
    {
        foreach (var group in _groups)
        {
            group.RefreshLoadouts(Profile, loadout, session, collection);
        }
    }
}