<!--
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <BoxContainer Name="VBox" Orientation="Vertical">
        <controls:NanoHeading Name="Header" Text="{Loc 'lobby-character-preview-panel-header'}">

        </controls:NanoHeading>
        <BoxContainer Name="Loaded" Orientation="Vertical"
                      Visible="False">
            <Label Name="Summary" HorizontalAlignment="Center" Margin="3 3"/>
            <BoxContainer Name="ViewBox" Orientation="Horizontal" HorizontalAlignment="Center">

            </BoxContainer>
            <controls:VSpacer/>
            <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="CharacterSetup" Text="{Loc 'lobby-character-preview-panel-character-setup-button'}"
                        HorizontalAlignment="Center"
                        Margin="0 5 0 0" />
                <Button Name="PatronPerks" Access="Public" Text="{Loc 'rmc-ui-patron-perks'}"
                        Margin="0 5 0 0" Visible="False" />
                <cc:CommandButton Name="Currency"
                    Command="balanceui"
                    Text="{Loc 'gs-balanceui-shop-label'}"
                    Margin="0 5 0 0"/>
            </BoxContainer>
        </BoxContainer>
        <Label Name="Unloaded" Text="{Loc 'lobby-character-preview-panel-unloaded-preferences-label'}"/>
    </BoxContainer>
</Control>
