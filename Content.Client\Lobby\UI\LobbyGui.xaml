<!--
SPDX-FileCopyrightText: 2021 Vera <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flipp <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jessica M <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>rotter <<EMAIL>>
SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Morbo <<EMAIL>>
SPDX-FileCopyrightText: 2022 Pieter-Jan Briers <<EMAIL>>
SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 theashtronaut <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 AJCM-git <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vordenburg <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>
SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<lobbyUi:LobbyGui
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:maths="clr-namespace:Robust.Shared.Maths;assembly=Robust.Shared.Maths"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:vote="clr-namespace:Content.Client.Voting.UI"
    xmlns:style="clr-namespace:Content.Client.Stylesheets"
    xmlns:lobbyUi="clr-namespace:Content.Client.Lobby.UI"
    xmlns:info="clr-namespace:Content.Client.Info"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Chat.Widgets"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client">
    <!-- Background -->
    <TextureRect Access="Public" VerticalExpand="True" HorizontalExpand="True" Name="Background"
                 Stretch="KeepAspectCovered" />
    <BoxContainer Name="MainContainer" VerticalExpand="True" HorizontalExpand="True" Orientation="Horizontal"
                  Margin="10 10 10 10" SeparationOverride="2">
        <SplitContainer State="Auto" ResizeMode="NotResizable" HorizontalExpand="True">
            <!-- LHS Controls -->
            <BoxContainer Name="LeftSide" Orientation="Vertical" SeparationOverride="4" HorizontalExpand="True">
                <Control Name="DefaultState" VerticalExpand="True">
                    <BoxContainer Name="TopLeft" Orientation="Vertical">
                        <!-- Left Top Panel -->
                        <PanelContainer StyleClasses="AngleRect" HorizontalAlignment="Left" Name="LeftSideTop"
                                        VerticalAlignment="Top">
                            <BoxContainer Orientation="Vertical" HorizontalAlignment="Center" MaxWidth="1200">
                                <info:LinkBanner Name="LinkBanner" VerticalExpand="false" HorizontalAlignment="Center"
                                                 Margin="3 3 3 3" />
                                <controls:StripeBack>
                                    <BoxContainer Orientation="Horizontal" SeparationOverride="6" Margin="3 3 3 3">
                                        <cc:UICommandButton Command="observe" Name="ObserveButton" Access="Public"
                                                            Text="{Loc 'ui-lobby-observe-button'}"
                                                            StyleClasses="ButtonBig"
                                                            WindowType="{x:Type lobbyUi:ObserveWarningWindow}" />
                                        <Label Name="StartTime"
                                               Access="Public"
                                               Align="Left"
                                               FontColorOverride="{x:Static maths:Color.DarkGray}"
                                               StyleClasses="LabelBig" HorizontalExpand="True" />
                                        <Button Name="ReadyButton" Access="Public" ToggleMode="True"
                                                Text="{Loc 'ui-lobby-ready-up-button'}"
                                                StyleClasses="ButtonBig" MinWidth="137" />
                                    </BoxContainer>
                                </controls:StripeBack>
                            </BoxContainer>
                        </PanelContainer>
                        <!-- Voting Popups -->
                        <BoxContainer Orientation="Vertical" SeparationOverride="4" Name="VoteContainer"
                                      Access="Public" HorizontalAlignment="Left" VerticalAlignment="Top" Margin="0 8" />
                    </BoxContainer>
                    <!-- Vertical Padding-->
                    <Control VerticalExpand="True" />
                    <!-- Left Bot Panel -->
                    <PanelContainer VerticalAlignment="Bottom" HorizontalAlignment="Left"
                                    Margin="0 75" StyleClasses="AngleRect">
                        <RichTextLabel Name="LobbyMessageLabel" Access="Public" Margin="5" />
                    </PanelContainer>
                    <BoxContainer Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Bottom" SeparationOverride="4"> <!-- Goobstation - Lobby Background Credits -->
                        <info:DevInfoBanner Name="DevInfoBanner" VerticalExpand="false" Margin="3 3 3 3" />
                        <PanelContainer StyleClasses="AngleRect">
                            <RichTextLabel Name="LobbySong" Access="Public" HorizontalAlignment="Center" />
                        </PanelContainer>
                        <!-- Goobstation start -->
                        <PanelContainer StyleClasses="AngleRect">
                            <RichTextLabel Name="LobbyBackground" Access="Public" HorizontalAlignment="Center" />
                        </PanelContainer>
                        <!-- Goobstation end -->
                    </BoxContainer>
                </Control>
                <!-- Character setup state -->
                <!-- This is injected on startup. Funky! -->
                <Control Access="Public" Visible="False" Name="CharacterSetupState" VerticalExpand="True" />
            </BoxContainer>
            <!-- Right Panel -->
            <PanelContainer Name="RightSide" Access="Public" StyleClasses="AngleRect" HorizontalAlignment="Right" VerticalExpand="True"
                            VerticalAlignment="Stretch">
                <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                    <!-- Top row -->
                    <BoxContainer Orientation="Horizontal" MinSize="0 40" Name="HeaderContainer" Access="Public"
                                  SeparationOverride="4">
                        <Label Name="ServerName" Access="Public" StyleClasses="LabelHeadingBigger" VAlign="Center"
                               HorizontalExpand="True" HorizontalAlignment="Center" />
                    </BoxContainer>
                    <!-- Gold line -->
                    <controls:HLine Color="{x:Static style:StyleNano.NanoGold}" Thickness="2" />
                    <controls:HSpacer Spacing="10" />
                    <!-- Voting & misc button bar -->
                    <BoxContainer Orientation="Horizontal" MinSize="0 40" HorizontalAlignment="Right">
                        <Button Name="AHelpButton" Access="Public" Text="{Loc 'ui-lobby-ahelp-button'}"
                                StyleClasses="ButtonBig" />
                        <vote:VoteCallMenuButton Name="CallVoteButton" StyleClasses="ButtonBig" />
                        <Button Name="OptionsButton" Access="Public" StyleClasses="ButtonBig"
                                Text="{Loc 'ui-lobby-options-button'}" />
                        <Button Name="LeaveButton" Access="Public" StyleClasses="ButtonBig"
                                Text="{Loc 'ui-lobby-leave-button'}" />
                    </BoxContainer>
                    <controls:HSpacer Spacing="10" />
                    <!-- Server info -->
                    <controls:NanoHeading Text="{Loc 'ui-lobby-server-info-block'}" />
                    <info:ServerInfo Name="ServerInfo" Access="Public" MinSize="0 30" VerticalExpand="false"
                                     Margin="3 3 3 3" MaxWidth="400" HorizontalAlignment="Left" />
                    <Label Name="StationTime" Access="Public" FontColorOverride="{x:Static maths:Color.LightGray}"
                           Margin="3 3 3 3" HorizontalAlignment="Left" />
                    <controls:HSpacer Spacing="5" />
                    <lobbyUi:LobbyCharacterPreviewPanel Name="CharacterPreview" Access="Public" />
                    <controls:HSpacer Spacing="5" />
                    <BoxContainer MinHeight="10" />
                    <!-- Server Currency -->
                    <Label Name="Balance" Access="Public" StyleClasses="LabelHeadingBigger" VAlign="Center"
                            HorizontalExpand="True" HorizontalAlignment="Center" />
                    <!-- Gold line -->
                    <controls:HLine Color="{x:Static style:StyleNano.NanoGold}" Thickness="2" Access="Public" />
                    <controls:HSpacer Spacing="10" />
                    <widgets:ChatBox Name="Chat" Access="Public" VerticalExpand="True" Margin="3 3 3 3" MinHeight="50" />
                </BoxContainer>
                <TextureButton Name="CollapseButton"
                                TexturePath="filled_right_arrow.svg.192dpi"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                Margin="0 2 2 0"
                                ModulateSelfOverride="#DEDEDE"
                                Scale="0.5 0.5"/>
            </PanelContainer>
        </SplitContainer>
        <PanelContainer Name="ExpandPanel"
                        StyleClasses="AngleRect" 
                        HorizontalAlignment="Right"
                        VerticalAlignment="Top"
                        Margin="0 2 2 0"
                        Visible="False">
            <TextureButton Name="ExpandButton"
                        TexturePath="filled_left_arrow.svg.192dpi"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        ModulateSelfOverride="#DEDEDE"
                        Scale="0.5 0.5"/>
        </PanelContainer>
    </BoxContainer>
</lobbyUi:LobbyGui>
