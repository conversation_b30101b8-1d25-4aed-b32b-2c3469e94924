<!--
SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
SPDX-FileCopyrightText: 2022 Morbo <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <BoxContainer Orientation="Vertical">
        <Label Text="{Loc 'observe-warning-1'}"/>
        <Label Text="{Loc 'observe-warning-2'}"/>
        <BoxContainer Orientation="Horizontal">
            <Button Name="NevermindButton" Text="{Loc 'observe-nevermind'}" SizeFlagsStretchRatio="1"/>
            <Control HorizontalExpand="True" SizeFlagsStretchRatio="2" />
            <cc:CommandButton Command="observe" Name="ObserveButton" StyleClasses="Caution" Text="{Loc 'observe-confirm'}" SizeFlagsStretchRatio="1"/>
            <cc:CommandButton Command="observe admin" Name="ObserveAsAdminButton" Text="{Loc 'observe-as-admin'}" SizeFlagsStretchRatio="1" Visible="False"/>
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
