// SPDX-FileCopyrightText: 2021 moonheart08 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Administration.Managers;
using JetBrains.Annotations;
using Robust.Client.AutoGenerated;
using Robust.Client.Player;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lobby.UI;

[GenerateTypedNameReferences]
[UsedImplicitly]
public sealed partial class ObserveWarningWindow : DefaultWindow
{
    [Dependency] private readonly ISharedAdminManager _adminManager = default!;
    [Dependency] private readonly IPlayerManager _playerManager = default!;

    public ObserveWarningWindow()
    {
        Title = Loc.GetString("observe-warning-window-title");
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        var player = _playerManager.LocalSession;

        if (player != null && _adminManager.IsAdmin(player))
        {
            ObserveButton.Text = Loc.GetString("observe-as-player");
            ObserveAsAdminButton.Visible = true;
            ObserveAsAdminButton.OnPressed += _ => { this.Close(); };
        }

        ObserveButton.OnPressed += _ => { this.Close(); };
        NevermindButton.OnPressed += _ => { this.Close(); };
    }
}