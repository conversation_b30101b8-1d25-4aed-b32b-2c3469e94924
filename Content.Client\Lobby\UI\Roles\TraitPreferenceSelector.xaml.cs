// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Traits;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Lobby.UI.Roles;

[GenerateTypedNameReferences]
public sealed partial class TraitPreferenceSelector : Control
{
    public int Cost;

    public bool Preference
    {
        get => Checkbox.Pressed;
        set => Checkbox.Pressed = value;
    }

    public event Action<bool>? PreferenceChanged;

    public TraitPreferenceSelector(TraitPrototype trait)
    {
        RobustXamlLoader.Load(this);

        var text = trait.Cost != 0 ? $"[{trait.Cost}] " : "";
        text += Loc.GetString(trait.Name);

        Cost = trait.Cost;
        Checkbox.Text = text;
        Checkbox.OnToggled += OnCheckBoxToggled;

        if (trait.Description is { } desc)
        {
            Checkbox.ToolTip = Loc.GetString(desc);
        }
    }

    private void OnCheckBoxToggled(BaseButton.ButtonToggledEventArgs args)
    {
        PreferenceChanged?.Invoke(Preference);
    }
}