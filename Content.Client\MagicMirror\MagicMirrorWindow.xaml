<!--
SPDX-FileCopyrightText: 2022 Flipp <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:humanoid="clr-namespace:Content.Client.Humanoid"
               Title="{Loc 'magic-mirror-window-title'}"
               MinSize="600 400">
    <BoxContainer>
        <humanoid:SingleMarkingPicker Name="HairPicker" Category="Hair" />
        <humanoid:SingleMarkingPicker Name="FacialHairPicker" Category="FacialHair" />
    </BoxContainer>
</DefaultWindow>
