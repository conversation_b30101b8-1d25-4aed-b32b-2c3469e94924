// SPDX-FileCopyrightText: 2024 AJCM <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ActiveMammmoth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ActiveMammmoth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 keronshb <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Magic;

namespace Content.Client.Magic;

public sealed class MagicSystem : SharedMagicSystem;