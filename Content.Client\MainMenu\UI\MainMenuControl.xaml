<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<Control xmlns="https://spacestation14.io"
         xmlns:pllax="clr-namespace:Content.Client.Parallax"
         xmlns:clog="clr-namespace:Content.Client.Changelog">
    <pllax:ParallaxControl
        ParallaxPrototype="TrainStation"
        SpeedX="5"
        SpeedY="20"
        ScaleX="3"
        ScaleY="3"/>
    <BoxContainer Name="VBox"
                  Orientation="Vertical"
                  HorizontalAlignment="Center"
                  VerticalAlignment="Center"
                  HorizontalExpand="True"
                  VerticalExpand="True"
                  StyleIdentifier="mainMenuVBox"
                  SeparationOverride="3">
        <TextureRect Name="Logo"
                     Stretch="KeepCentered"/>
        <GridContainer Columns="2">
            <Label Text="{Loc 'main-menu-username-label'}" />
            <LineEdit Name="UsernameBox"
                      Access="Public"
                      PlaceHolder="{Loc 'main-menu-username-text'}"
                      HorizontalExpand="True" />
            <Label Text="{Loc 'main-menu-address-label'}"/>
            <LineEdit Name="AddressBox"
                      Access="Public"
                      Text="localhost"
                      PlaceHolder="server address:port"
                      HorizontalExpand="True" />
        </GridContainer>
        <Button Name="DirectConnectButton"
                Access="Public"
                Text="{Loc 'main-menu-direct-connect-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <Button Name="OptionsButton"
                Access="Public"
                Text="{Loc 'main-menu-options-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <Button Name="QuitButton"
                Access="Public"
                Text="{Loc 'main-menu-quit-button'}"
                TextAlign="Center"
                StyleIdentifier="mainMenu"/>
        <clog:ChangelogButton
            Name="ChangelogButton"
            Access="Public"/>
    </BoxContainer>
</Control>
