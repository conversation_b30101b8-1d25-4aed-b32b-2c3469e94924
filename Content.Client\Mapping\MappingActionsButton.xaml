<!--
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<mapping:MappingActionsButton
    xmlns="https://spacestation14.io"
    xmlns:mapping="clr-namespace:Content.Client.Mapping"
    StyleClasses="ButtonSquare" ToggleMode="True" SetSize="32 32" Margin="0 0 5 0"
    TooltipDelay="0">
    <TextureRect Name="Texture" Access="Public" Stretch="Scale" SetSize="16 16"
                 HorizontalAlignment="Center" VerticalAlignment="Center" />
</mapping:MappingActionsButton>
