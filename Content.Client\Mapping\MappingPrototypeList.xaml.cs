// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <103440971+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using static Robust.Client.UserInterface.Controls.BaseButton;

namespace Content.Client.Mapping;

[GenerateTypedNameReferences]
public sealed partial class MappingPrototypeList : Control
{
    private (int start, int end) _lastIndices;
    private readonly List<MappingPrototype> _prototypes = new();
    private readonly List<Texture> _insertTextures = new();
    private readonly List<MappingPrototype> _search = new();

    public MappingSpawnButton? Selected;
    public Action<IPrototype, List<Texture>>? GetPrototypeData;
    public event Action<MappingSpawnButton, IPrototype?>? SelectionChanged;
    public event Action<MappingSpawnButton, ButtonToggledEventArgs>? CollapseToggled;

    public MappingPrototypeList()
    {
        RobustXamlLoader.Load(this);

        MeasureButton.Measure(Vector2Helpers.Infinity);

        ScrollContainer.OnScrolled += UpdateSearch;
        OnResized += UpdateSearch;
    }

    public void UpdateVisible(List<MappingPrototype> prototypes)
    {
        _prototypes.Clear();

        PrototypeList.DisposeAllChildren();

        _prototypes.AddRange(prototypes);

        Selected = null;
        ScrollContainer.SetScrollValue(new Vector2(0, 0));

        foreach (var prototype in _prototypes)
        {
            Insert(PrototypeList, prototype, true);
        }
    }

    public MappingSpawnButton Insert(Container list, MappingPrototype mapping, bool includeChildren)
    {
        var prototype = mapping.Prototype;

        _insertTextures.Clear();

        if (prototype != null)
            GetPrototypeData?.Invoke(prototype, _insertTextures);

        var button = new MappingSpawnButton { Prototype = mapping };
        button.Label.Text = mapping.Name;

        if (_insertTextures.Count > 0)
        {
            button.Texture.Textures.AddRange(_insertTextures);
            button.Texture.InvalidateMeasure();
        }
        else
        {
            button.Texture.Visible = false;
        }

        if (prototype != null && button.Prototype == Selected?.Prototype)
        {
            Selected = button;
            button.Button.Pressed = true;
        }

        list.AddChild(button);

        button.Button.OnToggled += _ => SelectionChanged?.Invoke(button, prototype);

        if (includeChildren && mapping.Children?.Count > 0)
        {
            button.CollapseButton.Visible = true;
            button.CollapseButton.OnToggled += args => CollapseToggled?.Invoke(button, args);
        }
        else
        {
            button.CollapseButtonWrapper.Visible = false;
            button.CollapseButton.Visible = false;
        }

        return button;
    }

    public void Search(List<MappingPrototype> prototypes)
    {
        _search.Clear();
        SearchList.DisposeAllChildren();
        _lastIndices = (0, -1);

        _search.AddRange(prototypes);
        SearchList.TotalItemCount = _search.Count;
        ScrollContainer.SetScrollValue(new Vector2(0, 0));

        UpdateSearch();
    }

    /// <summary>
    ///     Constructs a virtual list where not all buttons exist at one time, since there may be thousands of them.
    /// </summary>
    private void UpdateSearch()
    {
        if (!SearchList.Visible)
            return;

        var height = MeasureButton.DesiredSize.Y + PrototypeListContainer.Separation;
        var offset = Math.Max(-SearchList.Position.Y, 0);
        var startIndex = (int) Math.Floor(offset / height);
        SearchList.ItemOffset = startIndex;

        var (prevStart, prevEnd) = _lastIndices;
        var endIndex = startIndex - 1;
        var spaceUsed = -height;

        // calculate how far down we are scrolled
        while (spaceUsed < SearchList.Parent!.Height)
        {
            spaceUsed += height;
            endIndex += 1;
        }

        endIndex = Math.Min(endIndex, _search.Count - 1);

        // nothing changed in terms of which buttons are visible now and before
        if (endIndex == prevEnd && startIndex == prevStart)
            return;

        _lastIndices = (startIndex, endIndex);

        // remove previously seen but now unseen buttons from the top
        for (var i = prevStart; i < startIndex && i <= prevEnd; i++)
        {
            var control = SearchList.GetChild(0);
            SearchList.RemoveChild(control);
        }

        // remove previously seen but now unseen buttons from the bottom
        for (var i = prevEnd; i > endIndex && i >= prevStart; i--)
        {
            var control = SearchList.GetChild(SearchList.ChildCount - 1);
            SearchList.RemoveChild(control);
        }

        // insert buttons that can now be seen, from the start
        for (var i = Math.Min(prevStart - 1, endIndex); i >= startIndex; i--)
        {
            Insert(SearchList, _search[i], false).SetPositionInParent(0);
        }

        // insert buttons that can now be seen, from the end
        for (var i = Math.Max(prevEnd + 1, startIndex); i <= endIndex; i++)
        {
            Insert(SearchList, _search[i], false);
        }
    }
}