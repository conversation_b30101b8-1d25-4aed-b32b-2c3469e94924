<!--
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<mapping:MappingSpawnButton
    xmlns="https://spacestation14.io"
    xmlns:mapping="clr-namespace:Content.Client.Mapping">
    <BoxContainer Orientation="Vertical">
        <Control>
            <Button Name="Button" Access="Public" ToggleMode="True" StyleClasses="ButtonSquare" />
            <BoxContainer Orientation="Horizontal">
                <LayeredTextureRect Name="Texture" Access="Public" MinSize="48 48"
                                    HorizontalAlignment="Center" VerticalAlignment="Center"
                                    Stretch="KeepAspectCentered" CanShrink="True" />
                <Control SetSize="48 48" Access="Public" Name="CollapseButtonWrapper">
                    <Button Name="CollapseButton" Access="Public" Text="▶"
                            ToggleMode="True" StyleClasses="ButtonSquare" SetSize="48 48" />
                </Control>
                <Label Name="Label" Access="Public"
                       VAlign="Center"
                       VerticalExpand="True"
                       MinHeight="48"
                       Margin="5 0"
                       HorizontalExpand="True" ClipText="True" />
            </BoxContainer>
        </Control>
        <BoxContainer Name="ChildrenPrototypes" Access="Public" Orientation="Vertical"
                      Margin="24 0 0 0" />
    </BoxContainer>
</mapping:MappingSpawnButton>
