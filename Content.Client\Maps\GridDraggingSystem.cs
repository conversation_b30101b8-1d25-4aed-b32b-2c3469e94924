// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TemporalOroboros <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Shared.Maps;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.Input;
using Robust.Shared.Input;
using Robust.Shared.Map;
using Robust.Shared.Physics.Components;
using Robust.Shared.Timing;

namespace Content.Client.Maps;

/// <inheritdoc />
public sealed class GridDraggingSystem : SharedGridDraggingSystem
{
    [Dependency] private readonly IEyeManager _eyeManager = default!;
    [Dependency] private readonly IGameTiming _gameTiming = default!;
    [Dependency] private readonly IInputManager _inputManager = default!;
    [Dependency] private readonly IMapManager _mapManager = default!;
    [Dependency] private readonly InputSystem _inputSystem = default!;
    [Dependency] private readonly SharedTransformSystem _transformSystem = default!;

    public bool Enabled { get; set; }

    private EntityUid? _dragging;
    private Vector2 _localPosition;
    private MapCoordinates? _lastMousePosition;

    public override void Initialize()
    {
        base.Initialize();
        SubscribeNetworkEvent<GridDragToggleMessage>(OnToggleMessage);
    }

    private void OnToggleMessage(GridDragToggleMessage ev)
    {
        if (Enabled == ev.Enabled)
            return;

        Enabled = ev.Enabled;

        if (!Enabled)
            StopDragging();
    }

    private void StartDragging(EntityUid grid, Vector2 localPosition)
    {
        _dragging = grid;
        _localPosition = localPosition;

        if (HasComp<PhysicsComponent>(grid))
        {
            RaiseNetworkEvent(new GridDragVelocityRequest()
            {
                Grid = GetNetEntity(grid),
                LinearVelocity = Vector2.Zero
            });
        }
    }

    private void StopDragging()
    {
        if (_dragging == null) return;

        if (_lastMousePosition != null && TryComp(_dragging.Value, out TransformComponent? xform) &&
            TryComp<PhysicsComponent>(_dragging.Value, out _) &&
            xform.MapID == _lastMousePosition.Value.MapId)
        {
            var tickTime = _gameTiming.TickPeriod;
            var distance = _lastMousePosition.Value.Position - _transformSystem.GetWorldPosition(xform);
            RaiseNetworkEvent(new GridDragVelocityRequest()
            {
                Grid = GetNetEntity(_dragging.Value),
                LinearVelocity = distance.LengthSquared() > 0f ? (distance / (float) tickTime.TotalSeconds) * 0.25f : Vector2.Zero,
            });
        }

        _dragging = null;
        _localPosition = Vector2.Zero;
        _lastMousePosition = null;
    }

    public override void Update(float frameTime)
    {
        base.Update(frameTime);

        if (!Enabled || !_gameTiming.IsFirstTimePredicted) return;

        var state = _inputSystem.CmdStates.GetState(EngineKeyFunctions.Use);

        if (state != BoundKeyState.Down)
        {
            StopDragging();
            return;
        }

        var mouseScreenPos = _inputManager.MouseScreenPosition;
        var mousePos = _eyeManager.PixelToMap(mouseScreenPos);

        if (_dragging == null)
        {
            if (!_mapManager.TryFindGridAt(mousePos, out var gridUid, out var grid))
                return;

            StartDragging(gridUid, Vector2.Transform(mousePos.Position, _transformSystem.GetInvWorldMatrix(gridUid)));
        }

        if (!TryComp(_dragging, out TransformComponent? xform))
        {
            StopDragging();
            return;
        }

        if (xform.MapID != mousePos.MapId)
        {
            StopDragging();
            return;
        }

        var localToWorld = Vector2.Transform(_localPosition, _transformSystem.GetWorldMatrix(xform));

        if (localToWorld.EqualsApprox(mousePos.Position, 0.01f)) return;

        var requestedGridOrigin = mousePos.Position - _transformSystem.GetWorldRotation(xform).RotateVec(_localPosition);
        _lastMousePosition = new MapCoordinates(requestedGridOrigin, mousePos.MapId);

        RaiseNetworkEvent(new GridDragRequestPosition()
        {
            Grid = GetNetEntity(_dragging.Value),
            WorldPosition = requestedGridOrigin,
        });
    }
}