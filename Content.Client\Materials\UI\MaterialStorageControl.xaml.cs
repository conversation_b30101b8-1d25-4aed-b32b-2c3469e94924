// SPDX-FileCopyrightText: 2024 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Deserty0 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Client._Shitcode.Silo;
using Content.Goobstation.Common.Silo;
using Content.Shared.Materials;
using Content.Shared.Tag; // Goobstation
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;
using Robust.Shared.Prototypes;
namespace Content.Client.Materials.UI;

/// <summary>
/// This widget is one row in the lathe eject menu.
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class MaterialStorageControl : ScrollContainer
{
    [Dependency] private readonly IEntityManager _entityManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!; // Goobstation
    private readonly SiloSystem _silo; // Goobstation
    private readonly TagSystem _tagSystem; // Goobstation

    private EntityUid? _owner;

    private Dictionary<string, int> _currentMaterials = new();

    public MaterialStorageControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _silo = _entityManager.System<SiloSystem>(); // Goobstation
        _tagSystem = _entityManager.System<TagSystem>(); // Goobstation
    }

    public void SetOwner(EntityUid owner)
    {
        _owner = owner;
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_owner == null)
            return;

        if (_entityManager.Deleted(_owner) || !_entityManager.TryGetComponent<MaterialStorageComponent>(_owner, out var materialStorage))
        {
            _owner = null;
            return;
        }

        var canEject = materialStorage.CanEjectStoredMaterials;
        // Goobstation start
        Dictionary<string, int> storage;
        Dictionary<string, int> mats;
        if (materialStorage.ConnectToSilo)
        {
            var silo = _silo.GetSilo(_owner.Value);
            storage = silo != null
                ? silo.Value.Comp.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary()
                : materialStorage.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary();
            SiloLabel.Visible = silo != null;
        }
        else
        {
            storage = materialStorage.Storage.Select(pair => (pair.Key.Id, pair.Value)).ToDictionary();
            SiloLabel.Visible = false;
        }

        mats = materialStorage.DisallowOreEjection
            ? FilterOutOres(storage)
            : storage;

        // Goobstation end
        if (_currentMaterials.Equals(mats))
            return;

        var missing = new List<string>();
        var extra = new List<string>();
        foreach (var (mat, amount) in mats)
        {
            if (!_currentMaterials.ContainsKey(mat) ||
                _currentMaterials[mat] == 0 && _currentMaterials[mat] != amount)
                missing.Add(mat);
        }
        foreach (var (mat, amount) in _currentMaterials)
        {
            if (!mats.ContainsKey(mat) || amount == 0)
                extra.Add(mat);
        }

        var children = new List<MaterialDisplay>();
        children.AddRange(MaterialList.Children.OfType<MaterialDisplay>());

        foreach (var display in children)
        {
            var mat = display.Material;

            if (extra.Contains(mat))
            {
                MaterialList.RemoveChild(display);
                continue;
            }

            if (!mats.TryGetValue(mat, out var newAmount))
                continue;
            display.UpdateVolume(newAmount);
        }

        foreach (var mat in missing)
        {
            var volume = mats[mat];
            MaterialList.AddChild(new MaterialDisplay(_owner.Value, mat, volume, canEject));
        }

        _currentMaterials = mats;
        NoMatsLabel.Visible = MaterialList.ChildCount == 1;
    }

    private Dictionary<string, int> FilterOutOres(Dictionary<string, int> materials)
    {
        return materials.Where(pair =>
            !(_prototypeManager.TryIndex<MaterialPrototype>(pair.Key, out var proto) &&
            proto.StackEntity != null &&
            _prototypeManager.TryIndex<EntityPrototype>(proto.StackEntity, out var entityProto) &&
            entityProto.TryGetComponent<TagComponent>(out var tag) &&
            _tagSystem.HasTag(tag, "Ore")))
            .ToDictionary(pair => pair.Key, pair => pair.Value);
    }
}