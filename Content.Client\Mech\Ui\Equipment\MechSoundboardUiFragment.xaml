<!--
SPDX-FileCopyrightText: 2022 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<equipment:MechSoundboardUiFragment
    xmlns:equipment="clr-namespace:Content.Client.Mech.Ui.Equipment"
    xmlns="https://spacestation14.io" Margin="1 0 2 0" HorizontalExpand="True" VerticalExpand="True">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True"
                  VerticalExpand="True">
        <ItemList Name="Sounds"
                  VerticalExpand="True"
                  MinHeight="160"
                  HorizontalExpand="True"
                  SelectMode="Button"/>
    </BoxContainer>
</equipment:MechSoundboardUiFragment>
