// SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Mech;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Mech.Ui.Equipment;

[GenerateTypedNameReferences]
public sealed partial class MechSoundboardUiFragment : BoxContainer
{
    public event Action<int>? OnPlayAction;

    public MechSoundboardUiFragment()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }

    public void UpdateContents(MechSoundboardUiState state)
    {
        foreach (var sound in state.Sounds)
        {
            Sounds.AddItem(Loc.GetString($"mech-soundboard-{sound}")).OnSelected += item => {
                OnPlayAction?.Invoke(Sounds.IndexOf(item));
            };
        }
    }
}