// SPDX-FileCopyrightText: 2022 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Mech.Ui;

[GenerateTypedNameReferences]
public sealed partial class MechEquipmentControl : Control
{
    public event Action? OnRemoveButtonPressed;

    public MechEquipmentControl(EntityUid entity, string itemName, Control? fragment)
    {
        RobustXamlLoader.Load(this);
        EquipmentName.SetMessage(itemName);
        EquipmentView.SetEntity(entity);
        RemoveButton.TexturePath = "/Textures/Interface/Nano/cross.svg.png";

        if (fragment != null)
        {
            Separator.Visible = true;
            CustomControlContainer.AddChild(fragment);
        }

        RemoveButton.OnPressed += _ => OnRemoveButtonPressed?.Invoke();
    }
}