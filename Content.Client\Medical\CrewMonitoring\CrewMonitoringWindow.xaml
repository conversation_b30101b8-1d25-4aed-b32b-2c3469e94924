<!--
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Ahion <<EMAIL>>
SPDX-FileCopyrightText: 2023 DEATHB4DEFEAT <<EMAIL>>
SPDX-FileCopyrightText: 2023 chromiumboy <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:ui="clr-namespace:Content.Client.Medical.CrewMonitoring"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               Title="{Loc 'crew-monitoring-user-interface-title'}"
               Resizable="False"
               SetSize="1210 700"
               MinSize="1210 700">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Horizontal" VerticalExpand="True" HorizontalExpand="True">
            <ui:CrewMonitoringNavMapControl Name="NavMap" HorizontalExpand="True" VerticalExpand="True" Margin="5 20"/>
            <BoxContainer Orientation="Vertical" Margin="0 0 10 0">
                <controls:StripeBack>
                    <PanelContainer>
                        <Label Name="StationName" Text="Unknown station" Align="Center" Margin="0 5 0 3"/>
                    </PanelContainer>
                </controls:StripeBack>

                <LineEdit Name="SearchLineEdit" HorizontalExpand="True"
                          PlaceHolder="{Loc crew-monitor-filter-line-placeholder}" />

                <ScrollContainer Name="SensorScroller"
                                 VerticalExpand="True"
                                 SetWidth="520"
                                 Margin="8, 8, 8, 8">
                    <BoxContainer Name="SensorsTable"
                                   Orientation="Vertical"
                                   HorizontalExpand="True"
                                   Margin="0 0 10 0">
                        <!-- Table rows are filled by code -->
                    </BoxContainer>
                    <Label  Name="NoServerLabel"
                            Text="{Loc 'crew-monitoring-user-interface-no-server'}"
                            StyleClasses="LabelHeading"
                            FontColorOverride="Red"
                            HorizontalAlignment="Center"
                            Visible="false"/>
                </ScrollContainer>
            </BoxContainer>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'crew-monitoring-user-interface-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'crew-monitoring-user-interface-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
