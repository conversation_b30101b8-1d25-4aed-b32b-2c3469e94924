// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 20kdc <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Ahion <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Alex <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DEATHB4DEFEAT <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Julian <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2023 chromiumboy <<EMAIL>>
// SPDX-FileCopyrightText: 2023 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2023 keronshb <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pspritechologist <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2025 pathetic meowmeow <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Numerics;
using Content.Client.Pinpointer.UI;
using Content.Client.Stylesheets;
using Content.Client.UserInterface.Controls;
using Content.Shared.Medical.SuitSensor;
using Content.Shared.StatusIcon;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Map;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;
using Robust.Shared.Utility;
using static Robust.Client.UserInterface.Controls.BoxContainer;

namespace Content.Client.Medical.CrewMonitoring;

[GenerateTypedNameReferences]
public sealed partial class CrewMonitoringWindow : FancyWindow
{
    [Dependency] private readonly IEntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    private readonly SharedTransformSystem _transformSystem;
    private readonly SpriteSystem _spriteSystem;

    private NetEntity? _trackedEntity;
    private bool _tryToScrollToListFocus;
    private Texture? _blipTexture;

    public CrewMonitoringWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _transformSystem = _entManager.System<SharedTransformSystem>();
        _spriteSystem = _entManager.System<SpriteSystem>();

        NavMap.TrackedEntitySelectedAction += SetTrackedEntityFromNavMap;
    }

    public void Set(string stationName, EntityUid? mapUid)
    {
        _blipTexture = _spriteSystem.Frame0(new SpriteSpecifier.Texture(new ResPath("/Textures/Interface/NavMap/beveled_circle.png")));

        if (_entManager.TryGetComponent<TransformComponent>(mapUid, out var xform))
            NavMap.MapUid = xform.GridUid;

        else
            NavMap.Visible = false;

        StationName.AddStyleClass("LabelBig");
        StationName.Text = stationName;
        NavMap.ForceNavMapUpdate();
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (_tryToScrollToListFocus)
            TryToScrollToFocus();
    }

    public void ShowSensors(List<SuitSensorStatus> sensors, EntityUid monitor, EntityCoordinates? monitorCoords)
    {
        ClearOutDatedData();

        // No server label
        if (sensors.Count == 0)
        {
            NoServerLabel.Visible = true;
            return;
        }

        NoServerLabel.Visible = false;

        // Collect one status per user, using the sensor with the most data available.
        Dictionary<NetEntity, SuitSensorStatus> uniqueSensorsMap = new();
        foreach (var sensor in sensors)
        {
            if (uniqueSensorsMap.TryGetValue(sensor.OwnerUid, out var existingSensor))
            {
                // Skip if we already have a sensor with more data for this mob.
                if (existingSensor.Coordinates != null && sensor.Coordinates == null)
                    continue;

                if (existingSensor.DamagePercentage != null && sensor.DamagePercentage == null)
                    continue;
            }

            uniqueSensorsMap[sensor.OwnerUid] = sensor;
        }
        var uniqueSensors = uniqueSensorsMap.Values.ToList();

        // Order sensor data
        var orderedSensors = uniqueSensors.OrderBy(n => n.Name).OrderBy(j => j.Job);
        var assignedSensors = new HashSet<SuitSensorStatus>();
        var departments = uniqueSensors.SelectMany(d => d.JobDepartments).Distinct().OrderBy(n => n);

        // Create department labels and populate lists
        foreach (var department in departments)
        {
            var departmentSensors = orderedSensors.Where(d => d.JobDepartments.Contains(department));

            if (departmentSensors == null || !departmentSensors.Any())
                continue;

            foreach (var sensor in departmentSensors)
                assignedSensors.Add(sensor);

            if (SensorsTable.ChildCount > 0)
            {
                var spacer = new Control()
                {
                    SetHeight = 20,
                };

                SensorsTable.AddChild(spacer);
            }

            var deparmentLabel = new RichTextLabel()
            {
                Margin = new Thickness(10, 0),
                HorizontalExpand = true,
            };

            deparmentLabel.SetMessage(department);
            deparmentLabel.StyleClasses.Add(StyleNano.StyleClassTooltipActionDescription);

            SensorsTable.AddChild(deparmentLabel);

            PopulateDepartmentList(departmentSensors);
        }

        // Account for any non-station users
        var remainingSensors = orderedSensors.Except(assignedSensors);

        if (remainingSensors.Any())
        {
            var spacer = new Control()
            {
                SetHeight = 20,
            };

            SensorsTable.AddChild(spacer);

            var deparmentLabel = new RichTextLabel()
            {
                Margin = new Thickness(10, 0),
                HorizontalExpand = true,
            };

            deparmentLabel.SetMessage(Loc.GetString("crew-monitoring-user-interface-no-department"));
            deparmentLabel.StyleClasses.Add(StyleNano.StyleClassTooltipActionDescription);

            SensorsTable.AddChild(deparmentLabel);

            PopulateDepartmentList(remainingSensors);
        }

        // Show monitor on nav map
        if (monitorCoords != null && _blipTexture != null)
        {
            NavMap.TrackedEntities[_entManager.GetNetEntity(monitor)] = new NavMapBlip(monitorCoords.Value, _blipTexture, Color.Cyan, true, false);
        }
    }

    private void PopulateDepartmentList(IEnumerable<SuitSensorStatus> departmentSensors)
    {
        // Populate departments
        foreach (var sensor in departmentSensors)
        {
            if (!string.IsNullOrEmpty(SearchLineEdit.Text)
                && !sensor.Name.Contains(SearchLineEdit.Text, StringComparison.CurrentCultureIgnoreCase)
                && !sensor.Job.Contains(SearchLineEdit.Text, StringComparison.CurrentCultureIgnoreCase))
                continue;

            var coordinates = _entManager.GetCoordinates(sensor.Coordinates);

            // Add a button that will hold a username and other details
            NavMap.LocalizedNames.TryAdd(sensor.SuitSensorUid, sensor.Name + ", " + sensor.Job);

            var sensorButton = new CrewMonitoringButton()
            {
                SuitSensorUid = sensor.SuitSensorUid,
                Coordinates = coordinates,
                Disabled = (coordinates == null),
                HorizontalExpand = true,
            };

            if (sensor.SuitSensorUid == _trackedEntity)
                sensorButton.AddStyleClass(StyleNano.StyleClassButtonColorGreen);

            SensorsTable.AddChild(sensorButton);

            // Primary container to hold the button UI elements
            var mainContainer = new BoxContainer()
            {
                Orientation = LayoutOrientation.Horizontal,
                HorizontalExpand = true,
            };

            sensorButton.AddChild(mainContainer);

            // User status container
            var statusContainer = new BoxContainer()
            {
                SizeFlagsStretchRatio = 1.25f,
                Orientation = LayoutOrientation.Horizontal,
                HorizontalExpand = true,
            };

            mainContainer.AddChild(statusContainer);

            // Suit coords indicator
            var suitCoordsIndicator = new TextureRect()
            {
                Texture = _blipTexture,
                TextureScale = new Vector2(0.25f, 0.25f),
                Modulate = coordinates != null ? Color.LimeGreen : Color.DarkRed,
                HorizontalAlignment = HAlignment.Center,
                VerticalAlignment = VAlignment.Center,
            };

            statusContainer.AddChild(suitCoordsIndicator);

            // Specify texture for the user status icon
            var specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Alerts/human_crew_monitoring.rsi"), "alive");

            if (!sensor.IsAlive)
            {
                specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Alerts/human_crew_monitoring.rsi"), "dead");
            }

            else if (sensor.DamagePercentage != null)
            {
                var index = MathF.Round(4f * sensor.DamagePercentage.Value);

                if (index >= 5)
                    specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Alerts/human_crew_monitoring.rsi"), "critical");

                else
                    specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Alerts/human_crew_monitoring.rsi"), "health" + index);
            }

            // Status icon
            var statusIcon = new AnimatedTextureRect
            {
                HorizontalAlignment = HAlignment.Center,
                VerticalAlignment = VAlignment.Center,
                Margin = new Thickness(0, 1, 3, 0),
            };

            statusIcon.SetFromSpriteSpecifier(specifier);
            statusIcon.DisplayRect.TextureScale = new Vector2(2f, 2f);

            statusContainer.AddChild(statusIcon);

            // User name
            var nameLabel = new Label()
            {
                Text = sensor.Name,
                HorizontalExpand = true,
                ClipText = true,
            };

            statusContainer.AddChild(nameLabel);

            // User job container
            var jobContainer = new BoxContainer()
            {
                Orientation = LayoutOrientation.Horizontal,
                HorizontalExpand = true,
            };

            mainContainer.AddChild(jobContainer);

            // Job icon
            if (_prototypeManager.TryIndex<JobIconPrototype>(sensor.JobIcon, out var proto))
            {
                var jobIcon = new TextureRect()
                {
                    TextureScale = new Vector2(2f, 2f),
                    VerticalAlignment = VAlignment.Center,
                    Texture = _spriteSystem.Frame0(proto.Icon),
                    Margin = new Thickness(5, 0, 5, 0),
                };

                jobContainer.AddChild(jobIcon);
            }

            // Job name
            var jobLabel = new Label()
            {
                Text = sensor.Job,
                HorizontalExpand = true,
                ClipText = true,
            };

            jobContainer.AddChild(jobLabel);

            // Add user coordinates to the navmap
            if (coordinates != null && NavMap.Visible && _blipTexture != null)
            {
                NavMap.TrackedEntities.TryAdd(sensor.SuitSensorUid,
                    new NavMapBlip
                    (CoordinatesToLocal(coordinates.Value),
                    _blipTexture,
                    (_trackedEntity == null || sensor.SuitSensorUid == _trackedEntity) ? Color.LimeGreen : Color.LimeGreen * Color.DimGray,
                    sensor.SuitSensorUid == _trackedEntity));

                NavMap.Focus = _trackedEntity;

                // On button up
                sensorButton.OnButtonUp += args =>
                {
                    var prevTrackedEntity = _trackedEntity;

                    if (_trackedEntity == sensor.SuitSensorUid)
                    {
                        _trackedEntity = null;
                    }

                    else
                    {
                        _trackedEntity = sensor.SuitSensorUid;
                        NavMap.CenterToCoordinates(coordinates.Value);
                    }

                    NavMap.Focus = _trackedEntity;

                    UpdateSensorsTable(_trackedEntity, prevTrackedEntity);
                };
            }
        }
    }

    private void SetTrackedEntityFromNavMap(NetEntity? netEntity)
    {
        var prevTrackedEntity = _trackedEntity;
        _trackedEntity = netEntity;

        if (_trackedEntity == prevTrackedEntity)
            prevTrackedEntity = null;

        NavMap.Focus = _trackedEntity;
        _tryToScrollToListFocus = true;

        UpdateSensorsTable(_trackedEntity, prevTrackedEntity);
    }

    private void UpdateSensorsTable(NetEntity? currTrackedEntity, NetEntity? prevTrackedEntity)
    {
        foreach (var sensor in SensorsTable.Children)
        {
            if (sensor is not CrewMonitoringButton)
                continue;

            var castSensor = (CrewMonitoringButton) sensor;

            if (castSensor.SuitSensorUid == prevTrackedEntity)
                castSensor.RemoveStyleClass(StyleNano.StyleClassButtonColorGreen);

            else if (castSensor.SuitSensorUid == currTrackedEntity)
                castSensor.AddStyleClass(StyleNano.StyleClassButtonColorGreen);

            if (castSensor?.Coordinates == null)
                continue;

            if (NavMap.TrackedEntities.TryGetValue(castSensor.SuitSensorUid, out var data))
            {
                data = new NavMapBlip
                    (CoordinatesToLocal(data.Coordinates),
                    data.Texture,
                    (currTrackedEntity == null || castSensor.SuitSensorUid == currTrackedEntity) ? Color.LimeGreen : Color.LimeGreen * Color.DimGray,
                    castSensor.SuitSensorUid == currTrackedEntity);

                NavMap.TrackedEntities[castSensor.SuitSensorUid] = data;
            }
        }
    }

    private void TryToScrollToFocus()
    {
        if (!_tryToScrollToListFocus)
            return;

        if (TryGetNextScrollPosition(out float? nextScrollPosition))
        {
            SensorScroller.VScrollTarget = nextScrollPosition.Value;

            if (MathHelper.CloseToPercent(SensorScroller.VScroll, SensorScroller.VScrollTarget))
            {
                _tryToScrollToListFocus = false;
                return;
            }
        }
    }

    private bool TryGetNextScrollPosition([NotNullWhen(true)] out float? nextScrollPosition)
    {
        nextScrollPosition = 0;

        foreach (var sensor in SensorsTable.Children)
        {
            if (sensor is CrewMonitoringButton &&
                ((CrewMonitoringButton) sensor).SuitSensorUid == _trackedEntity)
                return true;

            nextScrollPosition += sensor.Height;
        }

        // Failed to find control
        nextScrollPosition = null;

        return false;
    }

    /// <summary>
    /// Converts the input coordinates to an EntityCoordinates which are in
    /// reference to the grid that the map is displaying. This is a stylistic
    /// choice; this window deliberately limits the rate that blips update,
    /// but if the blip is attached to another grid which is moving, that
    /// blip will move smoothly, unlike the others. By converting the
    /// coordinates, we are back in control of the blip movement.
    /// </summary>
    private EntityCoordinates CoordinatesToLocal(EntityCoordinates refCoords)
    {
        if (NavMap.MapUid != null)
        {
            return _transformSystem.WithEntityId(refCoords, (EntityUid)NavMap.MapUid);
        }
        else
        {
            return refCoords;
        }
    }

    private void ClearOutDatedData()
    {
        SensorsTable.RemoveAllChildren();
        NavMap.TrackedCoordinates.Clear();
        NavMap.TrackedEntities.Clear();
        NavMap.LocalizedNames.Clear();
    }
}

public sealed class CrewMonitoringButton : Button
{
    public int IndexInTable;
    public NetEntity SuitSensorUid;
    public EntityCoordinates? Coordinates;
}