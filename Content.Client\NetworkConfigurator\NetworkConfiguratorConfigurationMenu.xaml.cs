// SPDX-FileCopyrightText: 2022 Flipp <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Stylesheets;
using Content.Client.UserInterface.Controls;
using Content.Shared.DeviceNetwork;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.NetworkConfigurator;

[GenerateTypedNameReferences]
public sealed partial class NetworkConfiguratorConfigurationMenu : FancyWindow
{
    public event Action<string>? OnRemoveAddress;

    public NetworkConfiguratorConfigurationMenu()
    {
        RobustXamlLoader.Load(this);

        Clear.StyleClasses.Add(StyleBase.ButtonOpenLeft);
        Clear.StyleClasses.Add(StyleNano.StyleClassButtonColorRed);
        DeviceList.OnRemoveAddress += args =>
        {
            OnRemoveAddress?.Invoke(args);
        };
    }

    public void UpdateState(DeviceListUserInterfaceState state)
    {
        DeviceList.UpdateState(state.DeviceList, false);

        Count.Text = Loc.GetString("network-configurator-ui-count-label", ("count", state.DeviceList.Count));
    }
}