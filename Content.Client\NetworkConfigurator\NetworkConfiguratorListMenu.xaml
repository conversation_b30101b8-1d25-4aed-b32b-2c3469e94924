<!--
SPDX-FileCopyrightText: 2022 Flip<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 AJCM-git <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                xmlns:networkConfigurator="clr-namespace:Content.Client.NetworkConfigurator"
                Title="{Loc 'network-configurator-title-saved-devices'}" MinSize="220 400">
    <BoxContainer Orientation="Vertical" VerticalExpand="True">
        <networkConfigurator:NetworkConfiguratorDeviceList Name="DeviceList" />
        <BoxContainer Orientation="Horizontal" Margin="8 8 8 8">
            <Label Name="DeviceCountLabel" Margin="16 0 0 0" MaxWidth="64" />
            <Control HorizontalExpand="True" />
            <Button Name="ClearButton" Access="Public" Text="{Loc 'network-configurator-ui-clear-button'}" />
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
