<!--
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
SPDX-FileCopyrightText: 2024 Rainfey <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
               xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
               xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
               Title="{Loc 'war-declarator-ui-header'}">
    <BoxContainer Orientation="Vertical" SeparationOverride="4" MinWidth="440">
        <TextEdit Name="MessageEdit"
                  HorizontalExpand="True"
                  MinHeight="200"
                  Access="Public" />
        <Button Name="WarButton"
                Text="{Loc 'war-declarator-ui-try-war-button'}"
                StyleClasses="Caution"
                Access="Public"/>
        <BoxContainer Orientation="Vertical" HorizontalExpand="True">
            <Label Name="StatusLabel"
                   Align="Center"
                   Access="Public"/>
            <Label Name="InfoLabel"
                   Align="Center"
                   Access="Public"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
