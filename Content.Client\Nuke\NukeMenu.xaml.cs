// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Nuke;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Nuke
{
    [GenerateTypedNameReferences]
    public sealed partial class NukeMenu : DefaultWindow
    {
        public event Action<int>? OnKeypadButtonPressed;
        public event Action? OnClearButtonPressed;
        public event Action? OnEnterButtonPressed;

        public NukeMenu()
        {
            RobustXamlLoader.Load(this);
            FillKeypadGrid();
        }

        /// <summary>
        ///     Fill keypad buttons in keypad grid
        /// </summary>
        private void FillKeypadGrid()
        {
            // add 3 rows of keypad buttons (1-9)
            for (var i = 1; i <= 9; i++)
            {
                AddKeypadButton(i);
            }

            // clear button
            var clearBtn = new Button()
            {
                Text = "C"
            };
            clearBtn.OnPressed += _ => OnClearButtonPressed?.Invoke();
            KeypadGrid.AddChild(clearBtn);

            // zero button
            AddKeypadButton(0);

            // enter button
            var enterBtn = new Button()
            {
                Text = "E"
            };
            enterBtn.OnPressed += _ => OnEnterButtonPressed?.Invoke();
            KeypadGrid.AddChild(enterBtn);
        }

        private void AddKeypadButton(int i)
        {
            var btn = new Button()
            {
                Text = i.ToString()
            };

            btn.OnPressed += _ => OnKeypadButtonPressed?.Invoke(i);
            KeypadGrid.AddChild(btn);
        }

        public void UpdateState(NukeUiState state)
        {
            string firstMsg, secondMsg;

            ArmButton.Text = Loc.GetString("nuke-user-interface-arm-button");

            switch (state.Status)
            {
                case NukeStatus.AWAIT_DISK:
                    firstMsg = Loc.GetString("nuke-user-interface-first-status-device-locked");
                    secondMsg = Loc.GetString("nuke-user-interface-second-status-await-disk");
                    break;
                case NukeStatus.AWAIT_CODE:
                    firstMsg = Loc.GetString("nuke-user-interface-first-status-input-code");
                    secondMsg = Loc.GetString("nuke-user-interface-second-status-current-code",
                        ("code", VisualizeCode(state.EnteredCodeLength, state.MaxCodeLength)));
                    break;
                case NukeStatus.AWAIT_ARM:
                    firstMsg = Loc.GetString("nuke-user-interface-first-status-device-ready");
                    secondMsg = Loc.GetString("nuke-user-interface-second-status-time",
                        ("time", state.RemainingTime));
                    break;
                case NukeStatus.ARMED:
                    firstMsg = Loc.GetString("nuke-user-interface-first-status-device-armed");
                    secondMsg = Loc.GetString("nuke-user-interface-second-status-time",
                        ("time", state.RemainingTime));
                    ArmButton.Text = Loc.GetString("nuke-user-interface-disarm-button");
                    break;
                case NukeStatus.COOLDOWN:
                    firstMsg = Loc.GetString("nuke-user-interface-first-status-device-cooldown");
                    secondMsg = Loc.GetString("nuke-user-interface-second-status-cooldown-time",
                        ("time", state.CooldownTime));
                    break;
                default:
                    // shouldn't normally be here
                    firstMsg = Loc.GetString("nuke-user-interface-status-error");
                    secondMsg = Loc.GetString("nuke-user-interface-status-error");
                    break;
            }

            FirstStatusLabel.Text = firstMsg;
            SecondStatusLabel.Text = secondMsg;

            EjectButton.Disabled = !state.DiskInserted || state.Status == NukeStatus.ARMED || !state.IsAnchored;
            AnchorButton.Disabled = state.Status == NukeStatus.ARMED;
            AnchorButton.Pressed = state.IsAnchored;
            ArmButton.Disabled = !state.AllowArm || !state.IsAnchored;
        }

        private string VisualizeCode(int codeLength, int maxLength)
        {
            var code = new string('*', codeLength);
            var blanksCount = maxLength - codeLength;
            var blanks = new string('_', blanksCount);
            return code + blanks;
        }
    }
}