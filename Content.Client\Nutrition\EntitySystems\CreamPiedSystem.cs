// SPDX-FileCopyrightText: 2021 <PERSON> <6766154+<PERSON><PERSON>ori<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Nutrition.EntitySystems;
using JetBrains.Annotations;

namespace Content.Client.Nutrition.EntitySystems
{
    [UsedImplicitly]
    public sealed class CreamPiedSystem : SharedCreamPieSystem
    {
    }
}