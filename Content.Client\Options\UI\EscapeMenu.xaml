<!--
SPDX-FileCopyrightText: 2021 AJCM-git <<EMAIL>>
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 eclips_e <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<ui1:EscapeMenu xmlns="https://spacestation14.io"
            xmlns:changelog="clr-namespace:Content.Client.Changelog"
            xmlns:ui="clr-namespace:Content.Client.Voting.UI"
            xmlns:ui1="clr-namespace:Content.Client.Options.UI"
            Title="{Loc 'ui-escape-title'}"
            Resizable="False">

    <BoxContainer Orientation="Vertical" SeparationOverride="4" MinWidth="150">
        <Button Access="Public" Name="PatronPerksButton" Text="{Loc 'rmc-ui-patron-perks'}" StyleClasses="Caution" Visible="False" />
        <changelog:ChangelogButton Access="Public" Name="ChangelogButton"/>
        <ui:VoteCallMenuButton />
        <Button Access="Public" Name="OptionsButton" Text="{Loc 'ui-escape-options'}" />
        <Button Access="Public" Name="RulesButton" Text="{Loc 'ui-escape-rules'}" />
        <Button Access="Public" Name="GuidebookButton" Text="{Loc 'ui-escape-guidebook'}" />
        <Button Access="Public" Name="WikiButton" Text="{Loc 'ui-escape-wiki'}" />
        <Button Access="Public" Name="SupportButton" Text="{Loc 'donate-menu-button'}" StyleClasses="Caution"/>
        <Button Access="Public" Name="DiscordButton" Text="{Loc 'ui-options-general-discord'}" StyleClasses="Caution"/>
        <Button Access="Public" Name="DisconnectButton" Text="{Loc 'ui-escape-disconnect'}" />
        <Button Access="Public" Name="QuitButton" Text="{Loc 'ui-escape-quit'}" />
    </BoxContainer>
</ui1:EscapeMenu>
