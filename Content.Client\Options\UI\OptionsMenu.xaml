<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aidenkrz <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
            Title="{Loc 'ui-options-title'}"
            MinSize="800 450">
    <TabContainer Name="Tabs" Access="Public">
        <tabs:MiscTab Name="MiscTab" />
        <tabs:GraphicsTab Name="GraphicsTab" />
        <tabs:KeyRebindTab Name="KeyRebindTab" />
        <tabs:AudioTab Name="AudioTab" />
        <tabs:AccessibilityTab Name="AccessibilityTab" />
        <tabs:AdminOptionsTab Name="AdminOptionsTab" />
        <tabs:NetworkTab Name="NetworkTab" />
    </TabContainer>
</DefaultWindow>
