<!--
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
SPDX-FileCopyrightText: 2025 noirogen <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ui="clr-namespace:Content.Client.Options.UI"
         xmlns:ui1="clr-namespace:Content.Client._Shitcode.Options.UI">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True" HScrollEnabled="False">
            <BoxContainer Orientation="Vertical" Margin="8">
                <CheckBox Name="ReducedMotionCheckBox" Text="{Loc 'ui-options-reduced-motion'}" />
                <CheckBox Name="EnableColorNameCheckBox" Text="{Loc 'ui-options-enable-color-name'}" />
                <CheckBox Name="ColorblindFriendlyCheckBox" Text="{Loc 'ui-options-colorblind-friendly'}" />
                <ui:OptionSlider Name="ScreenShakeIntensitySlider" Title="{Loc 'ui-options-screen-shake-intensity'}" />
                <ui:OptionSlider Name="ChatWindowOpacitySlider" Title="{Loc 'ui-options-chat-window-opacity'}" />
                <ui:OptionSlider Name="SpeechBubbleTextOpacitySlider" Title="{Loc 'ui-options-speech-bubble-text-opacity'}" />
                <ui:OptionSlider Name="SpeechBubbleSpeakerOpacitySlider" Title="{Loc 'ui-options-speech-bubble-speaker-opacity'}" />
                <ui:OptionSlider Name="SpeechBubbleBackgroundOpacitySlider" Title="{Loc 'ui-options-speech-bubble-background-opacity'}" />
                <!-- Goobstation start -->
                <CheckBox Name="AutoFillHighlightsCheckBox" Text="{Loc 'ui-options-auto-fill-highlights'}" />
                <ui1:OptionColorSlider Name="HighlightsColorSlider"
                    Title="{Loc 'ui-options-highlights-color'}"
                    Example="{Loc 'ui-options-highlights-color-example'}"/>
                <!-- Goobstation end -->
            </BoxContainer>
        </ScrollContainer>
        <ui:OptionsTabControlRow Name="Control" Access="Public" />
    </BoxContainer>
</Control>
