<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ui="clr-namespace:Content.Client.Options.UI">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True" HScrollEnabled="False">
            <BoxContainer Orientation="Vertical" Margin="8">
                <Label Text="{Loc 'ui-options-admin-player-panel'}"
                       StyleClasses="LabelKeyText"/>
                <ui:OptionDropDown Name="DropDownPlayerTabSymbolSetting" Title="{Loc 'ui-options-admin-player-tab-symbol-setting'}" />
                <ui:OptionDropDown Name="DropDownPlayerTabRoleSetting" Title="{Loc 'ui-options-admin-player-tab-role-setting'}" />
                <ui:OptionDropDown Name="DropDownPlayerTabColorSetting" Title="{Loc 'ui-options-admin-player-tab-color-setting'}" />
                <Label Text="{Loc 'ui-options-admin-overlay-title'}"
                       StyleClasses="LabelKeyText"/>
                <ui:OptionDropDown Name="DropDownOverlayAntagFormat" Title="{Loc 'ui-options-admin-overlay-antag-format'}" />
                <ui:OptionDropDown Name="DropDownOverlayAntagSymbol" Title="{Loc 'ui-options-admin-overlay-antag-symbol'}" />
                <CheckBox Name="EnableOverlayPlaytimeCheckBox" Text="{Loc 'ui-options-admin-enable-overlay-playtime'}" />
                <CheckBox Name="EnableOverlayStartingJobCheckBox" Text="{Loc 'ui-options-admin-enable-overlay-starting-job'}" />
                <CheckBox Name="EnableOverlayUsernameCheckBox" Text="{Loc 'ui-options-enable-overlay-user-name'}" />
                <CheckBox Name="EnableOverlayCharacterNameCheckBox" Text="{Loc 'ui-options-enable-overlay-character-name'}" />
                <ui:OptionSlider Name="OverlayMergeDistanceSlider"  Title="{Loc 'ui-options-admin-overlay-merge-distance'}"/>
                <ui:OptionSlider Name="OverlayGhostFadeSlider"  Title="{Loc 'ui-options-admin-overlay-ghost-fade-distance'}"/>
                <ui:OptionSlider Name="OverlayGhostHideSlider"  Title="{Loc 'ui-options-admin-overlay-ghost-hide-distance'}"/>
            </BoxContainer>
        </ScrollContainer>
        <ui:OptionsTabControlRow Name="Control" Access="Public" />
    </BoxContainer>
</Control>
