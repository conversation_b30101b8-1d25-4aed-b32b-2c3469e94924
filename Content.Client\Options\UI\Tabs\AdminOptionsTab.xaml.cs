// SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SolsticeOfTheWinter <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Client.Administration;
using Content.Client.Administration.UI.Tabs.PlayerTab;
using Content.Goobstation.Common.CCVar;
using Content.Shared.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Options.UI.Tabs;

[GenerateTypedNameReferences]
public sealed partial class AdminOptionsTab : Control
{
    private const float OverlayMergeMin = 0.05f;
    private const float OverlayMergeMax = 0.95f;
    private const int OverlayGhostFadeMin = 0;
    private const int OverlayGhostFadeMax = 10;
    private const int OverlayGhostHideMin = 0;
    private const int OverlayGhostHideMax = 5;

    public AdminOptionsTab()
    {
        RobustXamlLoader.Load(this);

        var antagFormats = new List<OptionDropDownCVar<string>.ValueOption>();
        foreach (var format in Enum.GetValues(typeof(AdminOverlayAntagFormat)))
        {
            antagFormats.Add(new OptionDropDownCVar<string>.ValueOption(format.ToString()!, Loc.GetString($"ui-options-admin-overlay-antag-format-{format.ToString()!.ToLower()}")));
        }

        var antagSymbolStyles = new List<OptionDropDownCVar<string>.ValueOption>();
        foreach (var symbol in Enum.GetValues(typeof(AdminOverlayAntagSymbolStyle)))
        {
            antagSymbolStyles.Add(new OptionDropDownCVar<string>.ValueOption(symbol.ToString()!, Loc.GetString($"ui-options-admin-overlay-antag-symbol-{symbol.ToString()!.ToLower()}")));
        }

        var playerTabColorSettings = new List<OptionDropDownCVar<string>.ValueOption>();
        foreach (var setting in Enum.GetValues(typeof(AdminPlayerTabColorOption)))
        {
            playerTabColorSettings.Add(new OptionDropDownCVar<string>.ValueOption(setting.ToString()!, Loc.GetString($"ui-options-admin-player-tab-color-setting-{setting.ToString()!.ToLower()}")));
        }

        var playerTabRoleSettings = new List<OptionDropDownCVar<string>.ValueOption>();
        foreach (var setting in Enum.GetValues(typeof(AdminPlayerTabRoleTypeOption)))
        {
            playerTabRoleSettings.Add(new OptionDropDownCVar<string>.ValueOption(setting.ToString()!, Loc.GetString($"ui-options-admin-player-tab-role-setting-{setting.ToString()!.ToLower()}")));
        }

        var playerTabSymbolSettings = new List<OptionDropDownCVar<string>.ValueOption>();
        foreach (var setting in Enum.GetValues(typeof(AdminPlayerTabSymbolOption)))
        {
            playerTabSymbolSettings.Add(new OptionDropDownCVar<string>.ValueOption(setting.ToString()!, Loc.GetString($"ui-options-admin-player-tab-symbol-setting-{setting.ToString()!.ToLower()}")));
        }

        Control.AddOptionDropDown(CCVars.AdminPlayerTabSymbolSetting, DropDownPlayerTabSymbolSetting, playerTabSymbolSettings);
        Control.AddOptionDropDown(CCVars.AdminPlayerTabRoleSetting, DropDownPlayerTabRoleSetting, playerTabRoleSettings);
        Control.AddOptionDropDown(CCVars.AdminPlayerTabColorSetting, DropDownPlayerTabColorSetting, playerTabColorSettings);

        Control.AddOptionDropDown(CCVars.AdminOverlayAntagFormat, DropDownOverlayAntagFormat, antagFormats);
        Control.AddOptionDropDown(CCVars.AdminOverlaySymbolStyle, DropDownOverlayAntagSymbol, antagSymbolStyles);

        Control.AddOptionCheckBox(CCVars.AdminOverlayPlaytime, EnableOverlayPlaytimeCheckBox);
        Control.AddOptionCheckBox(CCVars.AdminOverlayStartingJob, EnableOverlayStartingJobCheckBox);
        // Goobstation - Start
        Control.AddOptionCheckBox(GoobCVars.AdminOverlayShowUserName, EnableOverlayUsernameCheckBox);
        Control.AddOptionCheckBox(GoobCVars.AdminOverlayShowCharacterName, EnableOverlayCharacterNameCheckBox);
        // Goobstation - End

        Control.Initialize();

        Control.AddOptionPercentSlider(
            CCVars.AdminOverlayMergeDistance,
            OverlayMergeDistanceSlider,
            OverlayMergeMin,
            OverlayMergeMax);

        Control.AddOptionSlider(
            CCVars.AdminOverlayGhostFadeDistance,
            OverlayGhostFadeSlider,
            OverlayGhostFadeMin,
            OverlayGhostFadeMax);

        Control.AddOptionSlider(
            CCVars.AdminOverlayGhostHideDistance,
            OverlayGhostHideSlider,
            OverlayGhostHideMin,
            OverlayGhostHideMax);
    }
}

