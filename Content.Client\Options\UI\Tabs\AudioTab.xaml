<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2022 theashtronaut <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 c4llv07e <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ui="clr-namespace:Content.Client.Options.UI">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Vertical" Margin="8 8 8 8" VerticalExpand="True">
            <Label Text="{Loc 'ui-options-volume-label'}"
                   StyleClasses="LabelKeyText"/>
            <BoxContainer Orientation="Vertical" Margin="0 3 0 0">
                <ui:OptionSlider Name="SliderVolumeMaster" Title="{Loc 'ui-options-master-volume'}"
                                 Margin="0 0 0 8" />
                <ui:OptionSlider Name="SliderVolumeMidi" Title="{Loc 'ui-options-midi-volume'}" />
                <ui:OptionSlider Name="SliderVolumeAmbientMusic" Title="{Loc 'ui-options-ambient-music-volume'}" />
                <ui:OptionSlider Name="SliderVolumeAmbience" Title="{Loc 'ui-options-ambience-volume'}" />
                <ui:OptionSlider Name="SliderVolumeLobby" Title="{Loc 'ui-options-lobby-volume'}" />
                <ui:OptionSlider Name="SliderVolumeInterface" Title="{Loc 'ui-options-interface-volume'}" />
                <ui:OptionSlider Name="SliderVolumeVoiceChat" Title="{Loc 'ui-options-voice-chat-volume'}" />
                <ui:OptionSlider Name="SliderMaxAmbienceSounds" Title="{Loc 'ui-options-ambience-max-sounds'}"
                                 Margin="0 0 0 8" />
                <CheckBox Name="HearSelfCheckBox" Text="{Loc 'ui-options-hear-self'}" />
                <CheckBox Name="LobbyMusicCheckBox" Text="{Loc 'ui-options-lobby-music'}" />
                <CheckBox Name="RestartSoundsCheckBox" Text="{Loc 'ui-options-restart-sounds'}" />
                <CheckBox Name="EventMusicCheckBox" Text="{Loc 'ui-options-event-music'}" />
                <CheckBox Name="AdminSoundsCheckBox" Text="{Loc 'ui-options-admin-sounds'}" />
                <CheckBox Name="BwoinkSoundCheckBox" Text="{Loc 'ui-options-bwoink-sound'}" />
            </BoxContainer>
        </BoxContainer>
        <ui:OptionsTabControlRow Name="Control" Access="Public" />
    </BoxContainer>
</Control>
