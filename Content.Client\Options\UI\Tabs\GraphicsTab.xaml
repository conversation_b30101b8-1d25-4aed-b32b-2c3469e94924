<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 pointer-to-null <<EMAIL>>
SPDX-FileCopyrightText: 2022 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flipp <PERSON><PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Artjom <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<tabs:GraphicsTab xmlns="https://spacestation14.io"
          xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
          xmlns:ui="clr-namespace:Content.Client.Options.UI">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True">
            <BoxContainer Orientation="Vertical" Margin="8 8 8 8">
                <!-- Display -->
                <Label Text="{Loc 'ui-options-display-label'}" StyleClasses="LabelKeyText"/>
                <CheckBox Name="VSyncCheckBox" Text="{Loc 'ui-options-vsync'}" />
                <CheckBox Name="FullscreenCheckBox" Text="{Loc 'ui-options-fullscreen'}" />

                <!-- Quality -->
                <Label Text="{Loc 'ui-options-quality-label'}" StyleClasses="LabelKeyText"/>
                <ui:OptionDropDown Name="DropDownLightingQuality" Title="{Loc 'ui-options-lighting-label'}" />
                <CheckBox Name="ViewportLowResCheckBox" Text="{Loc 'ui-options-vp-low-res'}" />
                <CheckBox Name="ParallaxLowQualityCheckBox" Text="{Loc 'ui-options-parallax-low-quality'}" />

                <!-- Interface -->
                <Label Text="{Loc 'ui-options-interface-label'}" StyleClasses="LabelKeyText"/>
                <ui:OptionDropDown Name="DropDownUIScale" Title="{Loc 'ui-options-scale-label'}" />
                <CheckBox Name="ViewportStretchCheckBox" Text="{Loc 'ui-options-vp-stretch'}" />
                <ui:OptionSlider Name="ViewportScaleSlider" Title="{Loc ui-options-vp-scale}" />
                <ui:OptionSlider Name="ViewportWidthSlider" Title="{Loc ui-options-vp-width}" />
                <CheckBox Name="IntegerScalingCheckBox"
                          Text="{Loc 'ui-options-vp-integer-scaling'}"
                          ToolTip="{Loc 'ui-options-vp-integer-scaling-tooltip'}" />
                <CheckBox Name="ViewportVerticalFitCheckBox"
                          Text="{Loc 'ui-options-vp-vertical-fit'}"
                          ToolTip="{Loc 'ui-options-vp-vertical-fit-tooltip'}" />

                <!-- Misc -->
                <Label Text="{Loc 'ui-options-misc-label'}" StyleClasses="LabelKeyText"/>
                <CheckBox Name="FpsCounterCheckBox" Text="{Loc 'ui-options-fps-counter'}" />
            </BoxContainer>
        </ScrollContainer>
        <ui:OptionsTabControlRow Name="Control" Access="Public" />
    </BoxContainer>
</tabs:GraphicsTab>
