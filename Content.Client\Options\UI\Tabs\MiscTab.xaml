<!--
SPDX-FileCopyrightText: 2023 Vasi<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aexxie <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
SPDX-FileCopyrightText: 2024 Sk1tch <<EMAIL>>
SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<tabs:MiscTab xmlns="https://spacestation14.io"
              xmlns:tabs="clr-namespace:Content.Client.Options.UI.Tabs"
              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
              xmlns:ui="clr-namespace:Content.Client.Options.UI">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True" HorizontalExpand="True">
            <BoxContainer Orientation="Vertical" Margin="8 8 8 8" VerticalExpand="True">
                <Label Text="{Loc 'ui-options-general-ui-style'}"
                       StyleClasses="LabelKeyText"/>
                <ui:OptionDropDown Name="DropDownHudTheme" Title="{Loc 'ui-options-hud-theme'}" />
                <ui:OptionDropDown Name="DropDownHudLayout" Title="{Loc 'ui-options-hud-layout'}" />
                <Label Text="{Loc 'ui-options-general-discord'}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="DiscordRich" Text="{Loc 'ui-options-discordrich'}" />
                <Label Text="{Loc 'ui-options-general-speech'}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ShowOocPatronColor" Text="{Loc 'ui-options-show-ooc-patron-color'}" />
                <CheckBox Name="ShowLoocAboveHeadCheckBox" Text="{Loc 'ui-options-show-looc-on-head'}" />
                <CheckBox Name="FancySpeechBubblesCheckBox" Text="{Loc 'ui-options-fancy-speech'}" />
                <CheckBox Name="FancyNameBackgroundsCheckBox" Text="{Loc 'ui-options-fancy-name-background'}" />
                <CheckBox Name="LogInChatCheckBox" Text="{Loc 'ui-options-log-in-chat'}" /> <!-- WD EDIT -->
                <CheckBox Name="CoalesceIdenticalMessagesCheckBox" Text="{Loc 'ui-options-coalesce-identical-messages'}" /> <!-- WD EDIT -->
                <CheckBox Name="DetailedExamineCheckBox" Text="{Loc 'ui-options-detailed-examine'}" /> <!-- Goobstation Change -->
                <Label Text="{Loc 'ui-options-general-cursor'}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="ShowHeldItemCheckBox" Text="{Loc 'ui-options-show-held-item'}" />
                <CheckBox Name="ShowCombatModeIndicatorsCheckBox" Text="{Loc 'ui-options-show-combat-mode-indicators'}" />
                <Label Text="{Loc 'ui-options-general-storage'}"
                       StyleClasses="LabelKeyText"/>
                <CheckBox Name="OpaqueStorageWindowCheckBox" Text="{Loc 'ui-options-opaque-storage-window'}" />
                <CheckBox Name="StaticStorageUI" Text="{Loc 'ui-options-static-storage-ui'}" />
                <!-- <CheckBox Name="ToggleWalk" Text="{Loc 'ui-options-hotkey-toggle-walk'}" /> -->
            </BoxContainer>
        </ScrollContainer>
        <ui:OptionsTabControlRow Name="Control" Access="Public" />
    </BoxContainer>
</tabs:MiscTab>
