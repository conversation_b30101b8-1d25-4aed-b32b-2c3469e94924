// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Vordenburg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 0x6273 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ActiveMammmoth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ActiveMammmoth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 keronshb <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Shared.Interaction;
using Content.Shared.Whitelist;
using Robust.Client.GameObjects;
using Robust.Client.Graphics;
using Robust.Client.Input;
using Robust.Client.Player;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;

namespace Content.Client.Outline;

/// <summary>
///     System used to indicate whether an entity is a valid target based on some criteria.
/// </summary>
public sealed class TargetOutlineSystem : EntitySystem
{
    [Dependency] private readonly IEyeManager _eyeManager = default!;
    [Dependency] private readonly IGameTiming _timing = default!;
    [Dependency] private readonly EntityLookupSystem _lookup = default!;
    [Dependency] private readonly IInputManager _inputManager = default!;
    [Dependency] private readonly IPlayerManager _playerManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly SharedInteractionSystem _interactionSystem = default!;
    [Dependency] private readonly EntityWhitelistSystem _whitelistSystem = default!;
    [Dependency] private readonly SharedTransformSystem _transformSystem = default!;

    private bool _enabled = false;

    /// <summary>
    ///     Whitelist that the target must satisfy.
    /// </summary>
    public EntityWhitelist? Whitelist = null;

    /// <summary>
    ///     Blacklist that the target must satisfy.
    /// </summary>
    public EntityWhitelist? Blacklist = null;

    /// <summary>
    ///     Predicate the target must satisfy.
    /// </summary>
    public Func<EntityUid, bool>? Predicate = null;

    /// <summary>
    ///     Event to raise as targets to check whether they are valid.
    /// </summary>
    /// <remarks>
    ///     This event will be uncanceled and re-used.
    /// </remarks>
    public CancellableEntityEventArgs? ValidationEvent = null;

    /// <summary>
    ///     Minimum range for a target to be valid.
    /// </summary>
    /// <remarks>
    ///     If a target is further than this distance, they will still be highlighted in a different color.
    /// </remarks>
    public float Range = -1;

    /// <summary>
    ///     Whether to check if the player is unobstructed to the target;
    /// </summary>
    public bool CheckObstruction = true;

    /// <summary>
    ///     The size of the box around the mouse to use when looking for valid targets.
    /// </summary>
    public float LookupSize = 1;

    private Vector2 LookupVector => new(LookupSize, LookupSize);

    [ValidatePrototypeId<ShaderPrototype>]
    private const string ShaderTargetValid = "SelectionOutlineInrange";

    [ValidatePrototypeId<ShaderPrototype>]
    private const string ShaderTargetInvalid = "SelectionOutline";

    private ShaderInstance? _shaderTargetValid;
    private ShaderInstance? _shaderTargetInvalid;

    private readonly HashSet<SpriteComponent> _highlightedSprites = new();

    public override void Initialize()
    {
        base.Initialize();

        _shaderTargetValid = _prototypeManager.Index<ShaderPrototype>(ShaderTargetValid).InstanceUnique();
        _shaderTargetInvalid = _prototypeManager.Index<ShaderPrototype>(ShaderTargetInvalid).InstanceUnique();
    }

    public void Disable()
    {
        if (_enabled == false)
            return;

        _enabled = false;
        RemoveHighlights();
    }

    public void Enable(float range, bool checkObstructions, Func<EntityUid, bool>? predicate, EntityWhitelist? whitelist, EntityWhitelist? blacklist, CancellableEntityEventArgs? validationEvent)
    {
        Range = range;
        CheckObstruction = checkObstructions;
        Predicate = predicate;
        Whitelist = whitelist;
        Blacklist = blacklist;
        ValidationEvent = validationEvent;

        _enabled = Predicate != null || Whitelist != null || Blacklist != null || ValidationEvent != null;
    }

    public override void Update(float frameTime)
    {
        base.Update(frameTime);

        if (!_enabled || !_timing.IsFirstTimePredicted)
            return;

        HighlightTargets();
    }

    private void HighlightTargets()
    {
        if (_playerManager.LocalEntity is not { Valid: true } player)
            return;

        // remove current highlights
        RemoveHighlights();

        // find possible targets on screen
        // TODO: Duplicated in SpriteSystem and DragDropSystem. Should probably be cached somewhere for a frame?
        var mousePos = _eyeManager.PixelToMap(_inputManager.MouseScreenPosition).Position;
        var bounds = new Box2(mousePos - LookupVector, mousePos + LookupVector);
        var pvsEntities = _lookup.GetEntitiesIntersecting(_eyeManager.CurrentEye.Position.MapId, bounds, LookupFlags.Approximate | LookupFlags.Static);
        var spriteQuery = GetEntityQuery<SpriteComponent>();

        foreach (var entity in pvsEntities)
        {
            if (!spriteQuery.TryGetComponent(entity, out var sprite) || !sprite.Visible)
                continue;

            // Check the predicate
            var valid = Predicate?.Invoke(entity) ?? true;

            // check the entity whitelist
            if (valid && Whitelist != null)
                valid = _whitelistSystem.IsWhitelistPass(Whitelist, entity);

            // and check the cancellable event
            if (valid && ValidationEvent != null)
            {
                ValidationEvent.Uncancel();
                RaiseLocalEvent(entity, (object) ValidationEvent, broadcast: false);
                valid = !ValidationEvent.Cancelled;
            }

            if (!valid)
            {
                // was this previously valid?
                if (_highlightedSprites.Remove(sprite) && (sprite.PostShader == _shaderTargetValid || sprite.PostShader == _shaderTargetInvalid))
                {
                    sprite.PostShader = null;
                    sprite.RenderOrder = 0;
                }

                continue;
            }

            // Range check
            if (CheckObstruction)
                valid = _interactionSystem.InRangeUnobstructed(player, entity, Range);
            else if (Range >= 0)
            {
                var origin = _transformSystem.GetWorldPosition(player);
                var target = _transformSystem.GetWorldPosition(entity);
                valid = (origin - target).LengthSquared() <= Range;
            }

            if (sprite.PostShader != null &&
                sprite.PostShader != _shaderTargetValid &&
                sprite.PostShader != _shaderTargetInvalid)
                return;

            // highlight depending on whether its in or out of range
            sprite.PostShader = valid ? _shaderTargetValid : _shaderTargetInvalid;
            sprite.RenderOrder = EntityManager.CurrentTick.Value;
            _highlightedSprites.Add(sprite);
        }
    }

    private void RemoveHighlights()
    {
        foreach (var sprite in _highlightedSprites)
        {
            if (sprite.PostShader != _shaderTargetValid && sprite.PostShader != _shaderTargetInvalid)
                continue;

            sprite.PostShader = null;
            sprite.RenderOrder = 0;
        }

        _highlightedSprites.Clear();
    }
}