// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 0x6273 <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.PDA;

[Virtual]
[GenerateTypedNameReferences]
public partial class PdaWindow : BaseWindow
{

    public string? BorderColor
    {
        get => Background.ActualModulateSelf.ToHex();

        set => Background.ModulateSelfOverride = Color.FromHex(value, Color.White);
    }

    public string? AccentHColor
    {
        get => AccentH.ActualModulateSelf.ToHex();

        set
        {
            AccentH.ModulateSelfOverride = Color.FromHex(value, Color.White);
            AccentH.Visible = value != null;
        }
    }

    public string? AccentVColor
    {
        get => AccentV.ActualModulateSelf.ToHex();

        set
        {
            AccentV.ModulateSelfOverride = Color.FromHex(value, Color.White);
            AccentV.Visible = value != null;
        }
    }

    public PdaWindow()
    {
        RobustXamlLoader.Load(this);

        CloseButton.OnPressed += _ => Close();
        XamlChildren = ContentsContainer.Children;

        AccentH.Visible = false;
        AccentV.Visible = false;
    }

    protected override DragMode GetDragModeFor(Vector2 relativeMousePos)
    {
        return DragMode.Move;
    }
}