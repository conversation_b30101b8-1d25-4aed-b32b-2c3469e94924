<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 TheDarkElites <<EMAIL>>
SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2023 router <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'comp-ringer-ui-menu-title'}"
            MinSize="320 100"
            SetSize="320 100">
        <BoxContainer Orientation="Vertical"
                    VerticalExpand="True"
                    HorizontalExpand="True"
                    HorizontalAlignment="Center"
                    MinSize="50 50">
            <PanelContainer>
                <BoxContainer Orientation="Horizontal"
                        VerticalExpand="True"
                        HorizontalExpand="True"
                        HorizontalAlignment="Center"
                        MinSize="180 0">
                    <Label Name = "Indent_0Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="♪" />
                    <LineEdit Name ="RingerNoteOneInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"/>
                    <Label Name = "Indent_1Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="-" />
                    <LineEdit Name ="RingerNoteTwoInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" />
                    <Label Name = "Indent_2Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="-" />
                    <LineEdit Name ="RingerNoteThreeInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" />
                    <Label Name = "Indent_3Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="-" />
                    <LineEdit Name ="RingerNoteFourInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" />
                    <Label Name = "Indent_4Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="-" />
                    <LineEdit Name ="RingerNoteFiveInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" />
                    <Label Name = "Indent_5Label"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Text="-" />
                    <LineEdit Name ="RingerNoteSixInput"
                            Access="Public"
                            MinSize="40 0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center" />
                </BoxContainer>
            </PanelContainer>
            <PanelContainer>
                <BoxContainer Orientation="Horizontal"
                            VerticalExpand="True"
                            HorizontalExpand="True"
                            HorizontalAlignment="Center"
                            MinSize="120 50">
                    <Button Name = "TestRingerButton"
                            Access="Public"
                            Text="{Loc 'comp-ringer-ui-test-ringtone-button'}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            StyleClasses="OpenRight" />
                    <Button Name = "SetRingerButton"
                            Access="Public"
                            Text="{Loc 'comp-ringer-ui-set-ringtone-button'}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            StyleClasses="OpenLeft" />
                </BoxContainer>
            </PanelContainer>
        </BoxContainer>
</controls:FancyWindow>
