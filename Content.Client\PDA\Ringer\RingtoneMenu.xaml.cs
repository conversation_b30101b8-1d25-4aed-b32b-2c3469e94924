// SPDX-FileCopyrightText: 2022 <PERSON> <60421075+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2022 TheDarkElites <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 router <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using System.Linq;
using System.Numerics;
using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Content.Shared.PDA;
using Robust.Client.UserInterface.Controls;

namespace Content.Client.PDA.Ringer
{
    [GenerateTypedNameReferences]
    public sealed partial class RingtoneMenu : FancyWindow
    {
        public string[] PreviousNoteInputs = new[] { "A", "A", "A", "A", "A", "A" };
        public LineEdit[] RingerNoteInputs;

        public event Action? SetRingtoneButtonPressed;
        public event Action? TestRingtoneButtonPressed;

        public RingtoneMenu()
        {
            RobustXamlLoader.Load(this);

            SetRingerButton.OnPressed += _ => SetRingtoneButtonPressed?.Invoke();
            TestRingerButton.OnPressed += _ => TestRingtoneButtonPressed?.Invoke();

            RingerNoteInputs = new[] { RingerNoteOneInput, RingerNoteTwoInput, RingerNoteThreeInput, RingerNoteFourInput, RingerNoteFiveInput, RingerNoteSixInput };

            for (var i = 0; i < RingerNoteInputs.Length; ++i)
            {
                var input = RingerNoteInputs[i];
                var index = i;

                input.OnTextChanged += args =>
                {
                    if (input.Text.Length <= 0)
                        return;

                    input.Text = args.Text.ToUpper();

                    var isValid = IsNote(input.Text);

                    if (!isValid)
                    {
                        input.Text = PreviousNoteInputs[index];
                        input.AddStyleClass("Caution");
                    }
                    else
                    {
                        PreviousNoteInputs[index] = input.Text;
                        input.RemoveStyleClass("Caution");
                    }

                    input.CursorPosition = input.Text.Length;
                };

                input.OnFocusExit += _ =>
                {
                    if (!IsNote(input.Text))
                    {
                        input.Text = PreviousNoteInputs[index];
                        input.RemoveStyleClass("Caution");
                    }
                };

                input.OnTextEntered += _ =>
                {
                    if (!IsNote(input.Text))
                    {
                        input.Text = PreviousNoteInputs[index];
                        input.RemoveStyleClass("Caution");
                    }
                    input.CursorPosition = input.Text.Length;
                };
            }
        }

        protected override DragMode GetDragModeFor(Vector2 relativeMousePos)
        {
            //Prevents the ringtone window from being resized
            return DragMode.Move;
        }

        /// <summary>
        /// Determines whether or not the characters inputed are authorized
        /// </summary>
        public static bool IsNote(string input)
        {
            if (input.Any(char.IsDigit))
                return false;

            input = input.Replace("#", "sharp");

            return Enum.TryParse(input, true, out Note _);
        }
    }
}