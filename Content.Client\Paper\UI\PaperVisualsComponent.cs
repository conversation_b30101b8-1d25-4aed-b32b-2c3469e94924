// SPDX-FileCopyrightText: 2022 Fishfish458 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 fishfish458 <fishfish458>
// SPDX-FileCopyrightText: 2023 /ʊniɹɑː/ <<EMAIL>>
// SPDX-FileCopyrightText: 2023 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Robust.Shared.Utility;

namespace Content.Client.Paper.UI;

[RegisterComponent]
public sealed partial class PaperVisualsComponent : Component
{
    /// <summary>
    ///     The path to the image which will be used as a background for the paper itself
    /// </summary>
    [DataField("backgroundImagePath")]
    public string? BackgroundImagePath;

    /// <summary>
    ///     An optional patch to configure tiling stretching of the background. Used to set
    ///     the PatchMargin in a <code>StyleBoxTexture</code>
    /// </summary>
    [DataField("backgroundPatchMargin")]
    public Box2 BackgroundPatchMargin = default;

    /// <summary>
    ///     Modulate the background image by this color. Can be used to add colorful
    ///     variants of images, without having to create new textures.
    /// </summary>
    [DataField("backgroundModulate")]
    public Color BackgroundModulate = Color.White;

    /// <summary>
    ///     Should the background image tile, or be streched? Sets <code>StyleBoxTexture.StrechMode</code>
    /// </summary>
    [DataField("backgroundImageTile")]
    public bool BackgroundImageTile = false;

    /// <summary>
    ///     An additional scale to apply to the background image
    /// </summary>
    [DataField("backgroundScale")]
    public Vector2 BackgroundScale = Vector2.One;

    /// <summary>
    ///     A path to an image which will be used as a header on the paper
    /// </summary>
    [DataField("headerImagePath")]
    public string? HeaderImagePath;

    /// <summary>
    ///     Modulate the header image by this color
    /// </summary>
    [DataField("headerImageModulate")]
    public Color HeaderImageModulate = Color.White;

    /// <summary>
    ///     Any additional margin to add around the header
    /// </summary>
    [DataField("headerMargin")]
    public Box2 HeaderMargin = default;

    /// <summary>
    /// A path to an image which will be used as a footer on the paper
    /// </summary>
    [DataField]
    public ResPath? FooterImagePath;

    /// <summary>
    /// Modulate the footer image by this color
    /// </summary>
    [DataField]
    public Color FooterImageModulate = Color.White;

    /// <summary>
    /// Any additional margin to add around the footer
    /// </summary>
    [DataField]
    public Box2 FooterMargin = default;

    /// <summary>
    ///     Path to an image to use as the background to the "content" of the paper
    ///     The header and actual written text will use this as a background. The
    ///     image will be tiled vertically with the property that the bottom of the
    ///     written text will line up with the bottom of this image.
    /// </summary>
    [DataField("contentImagePath")]
    public string? ContentImagePath;

    /// <summary>
    ///     Modulate the content image by this color
    /// </summary>
    [DataField("contentImageModulate")]
    public Color ContentImageModulate = Color.White;

    /// <summary>
    ///     An additional margin around the content (including header)
    /// </summary>
    [DataField("contentMargin")]
    public Box2 ContentMargin = default;

    /// <summary>
    ///     The number of lines that the content image represents. The
    ///     content image will be vertically tiled after this many lines
    ///     of text.
    /// </summary>
    [DataField("contentImageNumLines")]
    public int ContentImageNumLines = 1;

    /// <summary>
    ///     Modulate the style's font by this color
    /// </summary>
    [DataField("fontAccentColor")]
    public Color FontAccentColor = new Color(223, 223, 213);

    /// <summary>
    ///     This can enforce that your paper has a limited area to write in.
    ///     If you wish to constrain only one direction, the other direction
    ///     can be unlimited by specifying a value of zero.
    ///     This will be scaled according to UI scale.
    /// </summary>
    [DataField("maxWritableArea")]
    public Vector2? MaxWritableArea = null;
}