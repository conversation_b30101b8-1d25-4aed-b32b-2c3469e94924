// SPDX-FileCopyrightText: 2018 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2019 Silver <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2022 20kdc <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using System.Threading.Tasks;

namespace Content.Client.Parallax.Managers;

public interface IParallaxManager
{
    /// <summary>
    /// All WorldHomePosition values are offset by this.
    /// </summary>
    Vector2 ParallaxAnchor { get; set; }

    bool IsLoaded(string name);

    /// <summary>
    /// The layers of the selected parallax.
    /// </summary>
    ParallaxLayerPrepared[] GetParallaxLayers(string name);

    /// <summary>
    /// Loads in the default parallax to use.
    /// Do not call until prototype manager is available.
    /// </summary>
    void LoadDefaultParallax();

    Task LoadParallaxByName(string name);

    void UnloadParallax(string name);
}
