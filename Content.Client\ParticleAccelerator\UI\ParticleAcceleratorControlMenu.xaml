<!--
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 c4llv07e <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
    xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:ui="clr-namespace:Content.Client.ParticleAccelerator.UI"
    Title="{Loc 'particle-accelerator-control-menu-device-version-label'}"
    MinSize="320 120">

    <!-- Main Container -->
    <BoxContainer Orientation="Vertical"
        VerticalExpand="True">

        <!-- Sub-Main container -->
        <BoxContainer Orientation="Horizontal"
            VerticalExpand="True"
            HorizontalExpand="True">

            <!-- Info part -->
            <BoxContainer Orientation="Vertical"
                HorizontalExpand="True"
                Margin="8">

                <!-- Info -->
                <BoxContainer Orientation="Vertical"
                    SeparationOverride="4">

                    <!-- Status -->
                    <BoxContainer Orientation="Horizontal">
                        <RichTextLabel Name="StatusLabel" HorizontalExpand="True"/>
                        <Control MinWidth="8"/>
                        <RichTextLabel Name="StatusStateLabel"/>
                    </BoxContainer>

                    <!-- Power -->
                    <BoxContainer Orientation="Horizontal">
                        <RichTextLabel Name="PowerLabel"
                            HorizontalExpand="True"
                            VerticalAlignment="Center"/>

                        <Control MinWidth="8"/>

                        <Button Name="OffButton"
                            ToggleMode="False"
                            Text="{Loc 'particle-accelerator-control-menu-off-button'}"
                            StyleClasses="OpenRight"/>

                        <Button Name="OnButton"
                            ToggleMode="False"
                            Text="{Loc 'particle-accelerator-control-menu-on-button'}"
                            StyleClasses="OpenLeft"/>
                    </BoxContainer>

                    <!-- Strenght -->
                    <BoxContainer Orientation="Horizontal">
                        <RichTextLabel Name="StrengthLabel"
                            HorizontalExpand="True"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"/>

                        <Control MinWidth="8"/>

                        <SpinBox Name="StateSpinBox" Value="0"/>
                    </BoxContainer>

                    <!-- Power -->
                    <BoxContainer Orientation="Horizontal">
                        <RichTextLabel Name="DrawLabel" HorizontalExpand="True"/>
                        <Control MinWidth="8"/>
                        <RichTextLabel Name="DrawValueLabel"/>
                    </BoxContainer>
                </BoxContainer>

                <Control MinHeight="8" VerticalExpand="True"/> <!-- Filler -->

                <!-- Alarm -->
                <BoxContainer Name="AlarmControl"
                    Orientation="Vertical"
                    VerticalAlignment="Center"
                    Visible="False">

                    <controls:StripeBack Margin="-8 0">
                        <BoxContainer Orientation="Vertical">
                            <RichTextLabel Name="BigAlarmLabel"
                                HorizontalAlignment="Center"/>

                            <RichTextLabel Name="BigAlarmLabelTwo"
                                HorizontalAlignment="Center"/>
                        </BoxContainer>
                    </controls:StripeBack>

                    <Label Text="{Loc 'particle-accelerator-control-menu-service-manual-reference'}"
                        HorizontalAlignment="Center"
                        StyleClasses="LabelSubText"/>
                </BoxContainer>
            </BoxContainer>

            <PanelContainer StyleClasses="LowDivider" Margin="0 -8" HorizontalAlignment="Right"/>

            <!-- PA Visual part -->
            <BoxContainer Orientation="Vertical"
                VerticalAlignment="Center"
                Margin="8">

                <PanelContainer Name="BackPanel"
                    HorizontalAlignment="Center">

                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxTexture Modulate="#202023"
                            PatchMarginBottom="8"
                            PatchMarginLeft="8"
                            PatchMarginRight="8"
                            PatchMarginTop="8"/>
                    </PanelContainer.PanelOverride>

                    <BoxContainer Orientation="Vertical"
                        SeparationOverride="6"
                        VerticalExpand="True"
                        VerticalAlignment="Stretch"
                        HorizontalExpand="True"
                        HorizontalAlignment="Center">

                        <!-- PA Visualisation -->
                        <GridContainer Columns="3"
                            VSeparationOverride="0"
                            HSeparationOverride="0"
                            HorizontalAlignment="Center">

                            <Control/>
                            <ui:PASegmentControl Name="EndCapTexture" BaseState="end_cap"/>
                            <Control/>
                            <ui:PASegmentControl Name="ControlBoxTexture" BaseState="control_box"/>
                            <ui:PASegmentControl Name="FuelChamberTexture" BaseState="fuel_chamber"/>
                            <Control/>
                            <Control/>
                            <ui:PASegmentControl Name="PowerBoxTexture" BaseState="power_box"/>
                            <Control/>
                            <ui:PASegmentControl Name="EmitterStarboardTexture" BaseState="emitter_starboard"/>
                            <ui:PASegmentControl Name="EmitterForeTexture" BaseState="emitter_fore"/>
                            <ui:PASegmentControl Name="EmitterPortTexture" BaseState="emitter_port"/>
                        </GridContainer>

                        <Button Name="ScanButton"
                            Text="{Loc 'particle-accelerator-control-menu-scan-parts-button'}"
                            HorizontalAlignment="Center"/>
                    </BoxContainer>
                </PanelContainer>
            </BoxContainer>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical"
            VerticalAlignment="Bottom">

            <controls:StripeBack>
                <Label Text="{Loc 'particle-accelerator-control-menu-check-containment-field-warning'}"
                    HorizontalAlignment="Center"
                    StyleClasses="LabelSubText"
                    Margin="0 4"/>
            </controls:StripeBack>

            <BoxContainer Orientation="Horizontal"
                Margin="12 0 6 2"
                VerticalAlignment="Bottom">

                <!-- Footer title -->
                <Label Text="{Loc 'particle-accelerator-control-menu-flavor-left'}"
                    StyleClasses="WindowFooterText" />

                <!-- Version -->
                <Label Text="{Loc 'particle-accelerator-control-menu-flavor-right'}"
                    StyleClasses="WindowFooterText"
                    HorizontalAlignment="Right"
                    HorizontalExpand="True"
                    Margin="0 0 4 0" />

                <TextureRect StyleClasses="NTLogoDark"
                    Stretch="KeepAspectCentered"
                    VerticalAlignment="Center"
                    HorizontalAlignment="Right"
                    SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
