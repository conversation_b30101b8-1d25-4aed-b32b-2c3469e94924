// SPDX-FileCopyrightText: 2023 keronshb <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Physics.Controllers;

namespace Content.Client.Physics.Controllers;

public sealed class ConveyorController : SharedConveyorController
{
    //Class is empty, needed for prediction and networking
}