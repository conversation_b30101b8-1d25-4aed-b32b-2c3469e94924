<!--
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      Title="{Loc 'nav-beacon-window-title'}"
                      MinSize="250 230"
                      SetSize="320 230">
    <BoxContainer Orientation="Vertical" Margin="5">
        <BoxContainer Orientation="Horizontal">
            <Label Text="{Loc 'nav-beacon-text-label'}" Margin="0 0 5 0"/>
            <LineEdit Name="LabelLineEdit" HorizontalExpand="True"/>
            <Button Name="VisibleButton" ToggleMode="True" Text="{Loc 'nav-beacon-toggle-visible'}" Margin="10 0 0 0" MinWidth="100"/>
        </BoxContainer>
        <ColorSelectorSliders Name="ColorSelector" Margin="0 5 0 10"/>
        <BoxContainer HorizontalAlignment="Right" VerticalAlignment="Bottom" VerticalExpand="True">
        <Button Name="ApplyButton" Text="{Loc 'nav-beacon-button-apply'}" HorizontalAlignment="Right"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
