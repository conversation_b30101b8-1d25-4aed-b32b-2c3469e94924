// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Pinpointer;
using Content.Shared.Preferences;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Pinpointer.UI;

[GenerateTypedNameReferences]
public sealed partial class NavMapBeaconWindow : FancyWindow
{
    private string? _defaultLabel;
    private bool _defaultEnabled;
    private Color _defaultColor;

    public event Action<string?, bool, Color>? OnApplyButtonPressed;

    public NavMapBeaconWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);


        VisibleButton.OnPressed += args => UpdateVisibleButton(args.Button.Pressed);
        LabelLineEdit.OnTextChanged += OnTextChanged;
        ColorSelector.OnColorChanged += _ => TryEnableApplyButton();

        TryEnableApplyButton();
        ApplyButton.OnPressed += OnApplyPressed;
    }

    public void SetEntity(EntityUid uid, NavMapBeaconComponent navMap)
    {
        _defaultLabel = navMap.Text;
        _defaultEnabled = navMap.Enabled;
        _defaultColor = navMap.Color;

        UpdateVisibleButton(navMap.Enabled);
        LabelLineEdit.Text = navMap.Text ?? string.Empty;
        ColorSelector.Color = navMap.Color;
    }

    private void UpdateVisibleButton(bool value)
    {
        VisibleButton.Pressed = value;
        VisibleButton.Text = Loc.GetString(value
            ? "nav-beacon-toggle-visible"
            : "nav-beacon-toggle-invisible");

        TryEnableApplyButton();
    }

    private void OnTextChanged(LineEdit.LineEditEventArgs obj)
    {
        if (obj.Text.Length > HumanoidCharacterProfile.MaxNameLength)
            obj.Control.Text = obj.Text.Substring(0, HumanoidCharacterProfile.MaxNameLength);

        TryEnableApplyButton();
    }

    private void TryEnableApplyButton()
    {
        ApplyButton.Disabled = LabelLineEdit.Text == (_defaultLabel ?? string.Empty) &&
                               VisibleButton.Pressed == _defaultEnabled &&
                               ColorSelector.Color == _defaultColor;
    }

    private void OnApplyPressed(BaseButton.ButtonEventArgs obj)
    {
        _defaultLabel = LabelLineEdit.Text == string.Empty ? null : LabelLineEdit.Text;
        _defaultEnabled = VisibleButton.Pressed;
        _defaultColor = ColorSelector.Color;
        OnApplyButtonPressed?.Invoke(_defaultLabel, _defaultEnabled, _defaultColor);
        TryEnableApplyButton();
    }
}