// SPDX-FileCopyrightText: 2024 TGRCDev <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Pinpointer;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Map;

namespace Content.Client.Pinpointer.UI;

[GenerateTypedNameReferences]
public sealed partial class StationMapBeaconControl : Control, IComparable<StationMapBeaconControl>
{
    public readonly EntityCoordinates BeaconPosition;
    public Action<EntityCoordinates>? OnPressed;
    public string? Label => BeaconNameLabel.Text;
    private StyleBoxFlat _styleBox;
    public Color Color => _styleBox.BackgroundColor;

    public StationMapBeaconControl(EntityUid mapUid, SharedNavMapSystem.NavMapBeacon beacon)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        BeaconPosition = new EntityCoordinates(mapUid, beacon.Position);

        _styleBox = new StyleBoxFlat { BackgroundColor = beacon.Color };
        ColorPanel.PanelOverride = _styleBox;
        BeaconNameLabel.Text = beacon.Text;

        MainButton.OnPressed += args => OnPressed?.Invoke(BeaconPosition);
    }

    public int CompareTo(StationMapBeaconControl? other)
    {
        if (other == null)
            return 1;

        // Group by color
        var colorCompare = Color.ToArgb().CompareTo(other.Color.ToArgb());
        if (colorCompare != 0)
        {
            return colorCompare;
        }

        // If same color, sort by text
        return string.Compare(Label, other.Label);
    }
}