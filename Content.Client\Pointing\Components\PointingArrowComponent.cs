// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2023 AJCM-git <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Krunklehorn <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Shared.Pointing.Components;
using System.Numerics;

namespace Content.Client.Pointing.Components;
[RegisterComponent]
public sealed partial class PointingArrowComponent : SharedPointingArrowComponent
{
    /// <summary>
    /// How far the arrow moves up and down during the floating phase.
    /// </summary>
    [ViewVariables(VVAccess.ReadWrite)]
    [DataField("offset")]
    public Vector2 Offset = new(0, 0.25f);

    public readonly string AnimationKey = "pointingarrow";
}