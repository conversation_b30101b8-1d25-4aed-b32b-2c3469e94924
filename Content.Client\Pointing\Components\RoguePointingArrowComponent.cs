// SPDX-FileCopyrightText: 2021 Vera <PERSON>iler<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Pointing.Components;

namespace Content.Client.Pointing.Components
{
    [RegisterComponent]
    public sealed partial class RoguePointingArrowComponent : SharedRoguePointingArrowComponent
    {
    }
}