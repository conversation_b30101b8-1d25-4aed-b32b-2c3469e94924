<!--
SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow  xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:style="clr-namespace:Content.Client.Stylesheets"
    Name="APCMenu"
    Title="{Loc 'apc-menu-title'}"
    Resizable="False">

    <BoxContainer Orientation="Vertical">
        <BoxContainer Margin="0 5 10 10" Orientation="Vertical">
            <BoxContainer Margin="0" Orientation="Horizontal">
                <!-- Sprite View -->
                <PanelContainer Margin="0" StyleClasses="Inset" VerticalAlignment="Center">
                    <SpriteView Name="EntityView" Scale="2 2" SetSize="64 64" OverrideDirection="South" />
                </PanelContainer>
                <!-- Data -->
                <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                    <GridContainer Margin="10 0 0 0" Columns="2">
                        <!-- Power On/Off -->
                        <Label Text="{Loc 'apc-menu-breaker-label'}" HorizontalExpand="True"
                                StyleClasses="StatusFieldTitle" MinWidth="120"/>
                        <BoxContainer Orientation="Horizontal" MinWidth="90">
                            <Button Name="BreakerButton" Text="{Loc 'apc-menu-breaker-button'}" HorizontalExpand="True"/>
                        </BoxContainer>
                        <!--Charging Status-->
                        <Label Text="{Loc 'apc-menu-external-label'}" StyleClasses="StatusFieldTitle" MinWidth="120" />
                        <Label Name="ExternalPowerStateLabel" Text="{Loc 'apc-menu-power-state-good'}" />
                        <!--Battery Power-->
                        <Label Text="{Loc 'apc-menu-power-label'}" StyleClasses="StatusFieldTitle" MinWidth="120" />
                        <Label Name="PowerLabel"/>
                    </GridContainer>
                </BoxContainer>
            </BoxContainer>
            <!-- Charge Progress Bar-->
            <ProgressBar Name="ChargeBar"
                        HorizontalExpand="True"
                        MinValue="0"
                        MaxValue="1"
                        MinHeight = "25"
                        Page="0"
                        Margin="10 10 0 0"
                        Value="0.5">
                <Label Name="ChargePercentage" Margin="4 0" Text="0 %" />
            </ProgressBar>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'apc-menu-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'apc-menu-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
