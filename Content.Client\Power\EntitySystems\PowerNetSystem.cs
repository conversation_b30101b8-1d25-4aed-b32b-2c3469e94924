// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 chromiumboy <<EMAIL>>
// SPDX-FileCopyrightText: 2025 chromiumboy <<EMAIL>>
// SPDX-FileCopyrightText: 2025 metalgearsloth <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Power.EntitySystems;

namespace Content.Client.Power.EntitySystems;

public sealed class PowerNetSystem : SharedPowerNetSystem
{

}