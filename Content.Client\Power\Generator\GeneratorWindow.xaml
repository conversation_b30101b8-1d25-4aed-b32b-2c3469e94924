<!--
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                MinSize="450 250"
                SetSize="450 250"
                Resizable="False"
                Title="{Loc 'portable-generator-ui-title'}">
    <BoxContainer Margin="4 0" Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" HorizontalExpand="True" SizeFlagsStretchRatio="2" VerticalAlignment="Top" Margin="5">
            <GridContainer Margin="2 0 0 0" Columns="2" HorizontalExpand="True">
                <Label Name="StatusLabel" Text="{Loc 'portable-generator-ui-power-switch'}" HorizontalExpand="True" />
                <Control MinWidth="120">
                    <Button Name="StartButton" Text="{Loc 'portable-generator-ui-start'}" />
                    <Button Name="StopButton" Text="{Loc 'portable-generator-ui-stop'}" />
                    <ProgressBar Name="StartProgress" MaxValue="1" />
                    <Label Name="LabelUnanchored" Text="{Loc 'portable-generator-ui-unanchored'}" />
                </Control>
                <!-- Power -->
                <Label Text="{Loc 'portable-generator-ui-target-power-label'}"/>
                <SpinBox Name="TargetPower" HorizontalExpand="True"/>
                <Label Text="{Loc 'portable-generator-ui-efficiency-label'}"/>
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <Label Name="Efficiency" Text="???%" />
                    <Label Name="Eta" HorizontalExpand="True" Margin="4 0 0 0" />
                </BoxContainer>
                <Label Text="{Loc 'portable-generator-ui-fuel-use-label'}"/>
                <ProgressBar Name="FuelFraction" MinValue="0" MaxValue="1" HorizontalExpand="True"/>
                <Label Text="{Loc 'portable-generator-ui-fuel-left-label'}"/>
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <Label Name="FuelLeft" Text="0" HorizontalExpand="True"/>
                    <Button Name="FuelEject" Text="{Loc 'portable-generator-ui-eject'}" />
                </BoxContainer>
                <Label Name="OutputSwitchLabel" Text="{Loc 'portable-generator-ui-switch'}" Visible="False" />
                <Button Name="OutputSwitchButton" Visible="False" />
                <!-- Network stats menu -->
                <Label Text="{Loc 'portable-generator-ui-network-stats'}"/>
                <Control>
                    <Label Name="NetworkStats" />
                </Control>
            </GridContainer>
            <Label Margin="2 0 0 0" Name="CloggedLabel" FontColorOverride="Red" Text="{Loc 'portable-generator-ui-clogged'}" />
        </BoxContainer>
        <cc:VSeparator StyleClasses="LowDivider"/>
        <PanelContainer Margin="12 0 0 0" StyleClasses="Inset" VerticalAlignment="Center">
            <SpriteView Name="EntityView" SetSize="64 64" Scale="2 2" OverrideDirection="South" Margin="15"/>
        </PanelContainer>
    </BoxContainer>
</controls:FancyWindow>
