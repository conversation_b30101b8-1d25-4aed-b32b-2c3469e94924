// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Power;

namespace Content.Client.Power.PowerCharge;

/// <inheritdoc cref="Content.Shared.Power.SharedPowerChargeComponent" />
[RegisterComponent]
public sealed partial class PowerChargeComponent : SharedPowerChargeComponent
{

}