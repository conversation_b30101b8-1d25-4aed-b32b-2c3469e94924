<!--
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Julian <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    MinSize="320 180"
    Resizable="False">
    <BoxContainer
        Margin="12 8"
        Orientation="Horizontal"
        SeparationOverride="12">
        <BoxContainer
            Orientation="Vertical"
            HorizontalExpand="True">
            <GridContainer
                Columns="2"
                VerticalAlignment="Center">

                <!-- Power -->
                <Label
                    Text="{Loc 'power-charge-window-power'}"
                    HorizontalExpand="True"
                    StyleClasses="StatusFieldTitle" />

                <BoxContainer
                    Orientation="Horizontal"
                    MinWidth="120"
                    SetWidth="160">

                    <Button Name="OnButton"
                        Text="{Loc 'power-charge-window-power-on'}"
                        StyleClasses="OpenRight"
                        MinWidth="60"
                        SetWidth="80"
                        HorizontalAlignment="Center"/>

                    <Button
                        Name="OffButton"
                        Text="{Loc 'power-charge-window-power-off'}"
                        StyleClasses="OpenLeft"
                        MinWidth="60"
                        SetWidth="80"
                        HorizontalAlignment="Center"/>
                </BoxContainer>

                <Control /> <!-- Empty control to act as a spacer in the grid. -->

                <Label Name="PowerLabel"
                    Text="0 / 0 W" />

                <!-- Status -->
                <Label
                    Text="{Loc 'power-charge-window-status'}"
                    StyleClasses="StatusFieldTitle" />
                <Label Name="StatusLabel"
                    Text="{Loc 'power-charge-window-status-fully-charged'}" />

                <!-- ETA -->
                <Label
                    Text="{Loc 'power-charge-window-eta'}"
                    StyleClasses="StatusFieldTitle" />
                <Label Name="EtaLabel"
                    Text="N/A" />

                <!-- Charge -->
                <Label
                    Text="{Loc 'power-charge-window-charge'}"
                    StyleClasses="StatusFieldTitle" />

                <ProgressBar Name="ChargeBar"
                    MaxValue="255">

                    <Label Name="ChargeText"
                        HorizontalAlignment="Center"
                        Margin="4 0"
                        Text="0 %" />
                </ProgressBar>
            </GridContainer>
        </BoxContainer>
        <PanelContainer
            StyleClasses="Inset"
            VerticalAlignment="Top">

            <SpriteView Name="EntityView"
                SetSize="96 96"
                Margin="0 0 0 26"
                OverrideDirection="South" />
        </PanelContainer>
    </BoxContainer>
</controls:FancyWindow>
