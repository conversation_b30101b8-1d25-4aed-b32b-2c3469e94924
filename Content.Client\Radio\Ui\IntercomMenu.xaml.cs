// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Radio.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Robust.Shared.Utility;

namespace Content.Client.Radio.Ui;

[GenerateTypedNameReferences]
public sealed partial class IntercomMenu : FancyWindow
{
    [Dependency] private readonly IPrototypeManager _prototype = default!;

    public event Action<bool>? OnMicPressed;
    public event Action<bool>? OnSpeakerPressed;
    public event Action<string>? OnChannelSelected;

    private readonly List<string> _channels = new();

    public IntercomMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        MicButton.OnPressed += args => OnMicPressed?.Invoke(args.Button.Pressed);
        SpeakerButton.OnPressed += args => OnSpeakerPressed?.Invoke(args.Button.Pressed);
    }

    public void Update(Entity<IntercomComponent> entity)
    {
        MicButton.Pressed = entity.Comp.MicrophoneEnabled;
        SpeakerButton.Pressed = entity.Comp.SpeakerEnabled;

        MicButton.Disabled = entity.Comp.SupportedChannels.Count == 0;
        SpeakerButton.Disabled = entity.Comp.SupportedChannels.Count == 0;
        ChannelOptions.Disabled = entity.Comp.SupportedChannels.Count == 0;

        ChannelOptions.Clear();
        _channels.Clear();
        for (var i = 0; i < entity.Comp.SupportedChannels.Count; i++)
        {
            var channel = entity.Comp.SupportedChannels[i];
            if (!_prototype.TryIndex(channel, out var prototype))
                continue;

            _channels.Add(channel);
            ChannelOptions.AddItem(Loc.GetString(prototype.Name), i);

            if (channel == entity.Comp.CurrentChannel)
                ChannelOptions.Select(i);
        }

        if (entity.Comp.SupportedChannels.Count == 0)
        {
            ChannelOptions.AddItem(Loc.GetString("intercom-options-none"), 0);
            ChannelOptions.Select(0);
        }

        ChannelOptions.OnItemSelected += args =>
        {
            if (!_channels.TryGetValue(args.Id, out var proto))
                return;

            ChannelOptions.SelectId(args.Id);
            OnChannelSelected?.Invoke(proto);
        };
    }
}
