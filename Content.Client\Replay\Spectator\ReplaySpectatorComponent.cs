// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

namespace Content.Client.Replay.Spectator;

/// <summary>
/// This component indicates that this entity currently has a replay spectator/observer attached to it.
/// </summary>
[RegisterComponent]
public sealed partial class ReplaySpectatorComponent : Component
{
}