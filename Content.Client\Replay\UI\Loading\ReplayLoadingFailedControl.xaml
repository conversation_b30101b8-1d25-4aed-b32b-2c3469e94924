<!--
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io"
         xmlns:pllax="clr-namespace:Content.Client.Parallax">
    <pllax:ParallaxControl SpeedX="20"/>
    <Control HorizontalAlignment="Center" VerticalAlignment="Center">
        <PanelContainer StyleClasses="AngleRect" />
        <BoxContainer Orientation="Vertical" SetSize="800 600" Margin="2">
            <ScrollContainer Name="what" VerticalExpand="True" HScrollEnabled="False" Margin="0 0 0 2" ReturnMeasure="True">
                <RichTextLabel Name="ReasonLabel" VerticalAlignment="Top" />
            </ScrollContainer>
            <Button Name="RetryButton" StyleClasses="Caution" Text="{Loc 'replay-loading-retry'}" Visible="False" />
            <Button Name="CancelButton" Text="{Loc 'replay-loading-cancel'}" Visible="False" />
        </BoxContainer>
    </Control>
</Control>
