// SPDX-FileCopyrightText: 2019 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2019 ZelteHonor <<EMAIL>>
// SPDX-FileCopyrightText: 2020 V<PERSON><PERSON> <6766154+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Galactic Chimp <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Chris <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Pancake <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Research.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class ResearchClientServerSelectionMenu : DefaultWindow
    {
        private int _serverCount;
        private string[] _serverNames = Array.Empty<string>();
        private int[] _serverIds = Array.Empty<int>();
        private int _selectedServerId = -1;

        public event Action<int>? OnServerSelected;
        public event Action? OnServerDeselected;

        public ResearchClientServerSelectionMenu()
        {
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            Servers.OnItemSelected += OnItemSelected;
            Servers.OnItemDeselected += OnItemDeselected;
        }

        public void OnItemSelected(ItemList.ItemListSelectedEventArgs itemListSelectedEventArgs)
        {
            OnServerSelected?.Invoke(_serverIds[itemListSelectedEventArgs.ItemIndex]);
        }

        public void OnItemDeselected(ItemList.ItemListDeselectedEventArgs itemListDeselectedEventArgs)
        {
            OnServerDeselected?.Invoke();
        }

        public void Populate(int serverCount, string[] serverNames, int[] serverIds, int selectedServerId)
        {
            _serverCount = serverCount;
            _serverNames = serverNames;
            _serverIds = serverIds;
            _selectedServerId = selectedServerId;

            // Disable so we can select the new selected server without triggering a new sync request.
            Servers.OnItemSelected -= OnItemSelected;
            Servers.OnItemDeselected -= OnItemDeselected;

            Servers.Clear();
            for (var i = 0; i < _serverCount; i++)
            {
                var id = _serverIds[i];
                Servers.AddItem(Loc.GetString("research-client-server-selection-menu-server-entry-text", ("id", id), ("serverName", _serverNames[i])));
                if (id == _selectedServerId)
                {
                    Servers[i].Selected = true;
                }
            }

            Servers.OnItemSelected += OnItemSelected;
            Servers.OnItemDeselected += OnItemDeselected;
        }
    }
}