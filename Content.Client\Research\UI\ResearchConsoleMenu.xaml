<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
                      Title="{Loc 'research-console-menu-title'}"
                      MinSize="625 400"
                      SetSize="700 550">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True"
                  VerticalExpand="True">
        <BoxContainer Orientation="Horizontal"
                      HorizontalExpand="True"
                      VerticalExpand="False"
                      MinHeight="85"
                      Margin="10">
            <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalExpand="True">
                <RichTextLabel Name="ResearchAmountLabel"/>
                <RichTextLabel Name="MainDisciplineLabel"/>
                <Control VerticalExpand="True"/>
                <BoxContainer Name="TierDisplayContainer" Orientation="Horizontal" HorizontalExpand="True" VerticalAlignment="Bottom"/>
                <!-- This is where we put all of the little graphics that display discipline tiers!-->
            </BoxContainer>
            <BoxContainer Orientation="Vertical" VerticalExpand="True" HorizontalAlignment="Right">
                <Button Name="ServerButton" Text="{Loc 'research-console-menu-server-selection-button'}" MinHeight="40"/>
            </BoxContainer>
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
                      HorizontalExpand="True"
                      VerticalExpand="True">
            <BoxContainer Orientation="Vertical"
                          VerticalExpand="True"
                          HorizontalExpand="True"
                          SizeFlagsStretchRatio="2"
                          Margin="10 0 10 10"
                          MinWidth="175">
                <Label Text="{Loc 'research-console-available-text'}" HorizontalAlignment="Center"/>
                <customControls:HSeparator StyleClasses="LowDivider" Margin="0 0 0 10"/>
                <PanelContainer VerticalExpand="True">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>
                    <ScrollContainer
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="AvailableCardsContainer"
                            Orientation="Vertical"
                            VerticalExpand="True">
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
                <Control MinHeight="10"/>
                <Label Text="{Loc 'research-console-unlocked-text'}" HorizontalAlignment="Center"/>
                <customControls:HSeparator StyleClasses="LowDivider" Margin="0 0 0 10"/>
                <PanelContainer VerticalExpand="True">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>
                    <ScrollContainer
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="UnlockedCardsContainer"
                            Orientation="Vertical"
                            VerticalExpand="True">
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>
            <BoxContainer Orientation="Vertical"
                          VerticalExpand="True"
                          HorizontalExpand="True"
                          SizeFlagsStretchRatio="3"
                          Margin="0 0 10 10">
                <PanelContainer VerticalExpand="True" MinSize="0 200">
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#1B1B1E" />
                    </PanelContainer.PanelOverride>
                    <ScrollContainer
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        SizeFlagsStretchRatio="2"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="TechnologyCardsContainer"
                            MinSize="100 256"
                            Orientation="Vertical"
                            SizeFlagsStretchRatio="2"
                            VerticalExpand="True">
                        </BoxContainer>
                    </ScrollContainer>
                </PanelContainer>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
