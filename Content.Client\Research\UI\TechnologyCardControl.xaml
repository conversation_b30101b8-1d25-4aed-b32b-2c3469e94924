<!--
SPDX-FileCopyrightText: 2023 0x6273 <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>

SPDX-License-Identifier: MIT
-->

<Control xmlns="https://spacestation14.io"
         xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls">
    <BoxContainer Name="MainContainer" Orientation="Vertical" Margin="10 0 10 10">
        <PanelContainer Name="Background"
                        Access="Public"
                        StyleClasses="PdaBackground"
                        MinHeight="15"
                        VerticalExpand="False"
                        HorizontalExpand="True"
                        Margin="0 0 0 -5"/>
        <Button Name="MainButton"
                Disabled="True"
                HorizontalExpand="True"
                VerticalExpand="True"
                StyleClasses="ButtonSquare"
                Margin="0">
            <BoxContainer Orientation="Vertical"
                          VerticalExpand="True"
                          Margin="5">
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <TextureRect Name="TechnologyTexture"
                                 TextureScale="2 2"
                                 VerticalAlignment="Center"
                                 HorizontalAlignment="Center"/>
                    <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalAlignment="Center" Margin="10 0 0 0">
                        <BoxContainer Orientation="Horizontal">
                            <Label Name="TechnologyNameLabel" StyleClasses="LabelKeyText" HorizontalExpand="True"/>
                            <TextureRect Name="DisciplineTexture" TextureScale="2 2" VerticalAlignment="Top" HorizontalAlignment="Right"/>
                        </BoxContainer>
                        <customControls:HSeparator StyleClasses="LowDivider" Margin="0 5 0 5"/>
                        <BoxContainer Orientation="Horizontal">
                            <RichTextLabel Name="TierLabel" HorizontalAlignment="Left" StyleClasses="LabelSubText" HorizontalExpand="True"/>
                            <Button Name="ResearchButton" Text="{Loc 'research-console-menu-server-research-button'}"/>
                        </BoxContainer>
                    </BoxContainer>
                </BoxContainer>
                <Control MinHeight="5"></Control>
                <RichTextLabel Name="UnlocksLabel" HorizontalExpand="True" StyleClasses="LabelSubText"></RichTextLabel>
            </BoxContainer>
        </Button>
    </BoxContainer>
</Control>
