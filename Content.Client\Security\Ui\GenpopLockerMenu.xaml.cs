﻿using Content.Client.Message;
using Content.Client.UserInterface.Controls;
using Content.Shared.Access.Components;
using Content.Shared.Security.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Security.Ui;

[GenerateTypedNameReferences]
public sealed partial class GenpopLockerMenu : FancyWindow
{
    public event Action<string, float, string>? OnConfigurationComplete;

    public GenpopLockerMenu(EntityUid owner, IEntityManager entMan)
    {
        RobustXamlLoader.Load(this);

        Title = entMan.GetComponent<MetaDataComponent>(owner).EntityName;

        NameLabel.SetMarkup(Loc.GetString("genpop-locker-ui-label-name"));
        SentenceLabel.SetMarkup(Loc.GetString("genpop-locker-ui-label-sentence"));
        CrimeLabel.SetMarkup(Loc.GetString("genpop-locker-ui-label-crime"));

        SentenceEdit.Text = "5";
        CrimeEdit.Text = Loc.GetString("genpop-prisoner-id-crime-default");

        NameEdit.IsValid = val => !string.IsNullOrWhiteSpace(val) && val.Length <= IdCardConsoleComponent.MaxFullNameLength;
        SentenceEdit.IsValid = val => float.TryParse(val, out var f) && f >= 0;
        CrimeEdit.IsValid = val => !string.IsNullOrWhiteSpace(val) && val.Length <= GenpopLockerComponent.MaxCrimeLength;

        NameEdit.OnTextChanged += _ => OnTextEdit();
        SentenceEdit.OnTextChanged += _ => OnTextEdit();
        CrimeEdit.OnTextChanged += _ => OnTextEdit();

        DoneButton.OnPressed += _ =>
        {
            OnConfigurationComplete?.Invoke(NameEdit.Text, float.Parse(SentenceEdit.Text), CrimeEdit.Text);
        };
    }

    private void OnTextEdit()
    {
        DoneButton.Disabled = string.IsNullOrWhiteSpace(NameEdit.Text) ||
                              !float.TryParse(SentenceEdit.Text, out var sentence) ||
                              sentence < 0 ||
                              string.IsNullOrWhiteSpace(CrimeEdit.Text);
    }
}
