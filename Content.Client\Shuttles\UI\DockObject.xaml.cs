// SPDX-FileCopyrightText: 2024 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Shuttles.BUIStates;
using JetBrains.Annotations;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Shuttles.UI;

[GenerateTypedNameReferences]
public sealed partial class DockObject : PanelContainer
{
    public BoxContainer ContentsContainer => Contents;

    public DockObject()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        PanelOverride = new StyleBoxFlat(new Color(30, 30, 34));
    }

    public void AddDock(DockingPortState state, ShuttleDockControl dockControl)
    {
        var viewButton = new Button()
        {
            Text = Loc.GetString("shuttle-console-view"),
        };

        viewButton.OnPressed += args =>
        {
            dockControl.SetViewedDock(state);
        };

        var container = new BoxContainer()
        {
            Orientation = BoxContainer.LayoutOrientation.Vertical,
            Children =
            {
                new Label()
                {
                    Text = state.Name,
                    HorizontalAlignment = HAlignment.Center,
                },
                viewButton
            }
        };

        DockContainer.AddChild(container);
    }

    public void SetName(string value)
    {
        DockedLabel.Text = value;
    }
}