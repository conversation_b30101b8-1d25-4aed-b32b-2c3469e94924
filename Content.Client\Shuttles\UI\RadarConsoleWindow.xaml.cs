// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Computer;
using Content.Client.UserInterface.Controls;
using Content.Shared.Shuttles.BUIStates;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.Shuttles.UI;

[GenerateTypedNameReferences]
public sealed partial class RadarConsoleWindow : FancyWindow,
    IComputerWindow<NavInterfaceState>
{
    public RadarConsoleWindow()
    {
        RobustXamlLoader.Load(this);
    }

    public void UpdateState(NavInterfaceState scc)
    {
        RadarScreen.UpdateState(scc);
    }
}