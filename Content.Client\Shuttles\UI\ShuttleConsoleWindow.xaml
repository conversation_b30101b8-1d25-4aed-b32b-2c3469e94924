<!--
SPDX-FileCopyrightText: 2022 deathride58 <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      xmlns:ui="clr-namespace:Content.Client.Shuttles.UI"
                      Title="{Loc 'shuttle-console-window-title'}"
                      SetSize="960 762"
                      MinSize="960 762">
    <BoxContainer Orientation="Vertical">
        <!-- Top row mode buttons -->
        <BoxContainer Name="ModeButtons"
                       Orientation="Horizontal"
                       HorizontalAlignment="Stretch"
                       MinSize="52 52"
                       Margin="5">
            <Button Name="NavModeButton"
                    ToggleMode="True"
                    HorizontalExpand="True"
                    VerticalExpand="True"
                    Text="NAV"
                    Margin="5"/>
            <Button Name="MapModeButton"
                    ToggleMode="True"
                    HorizontalExpand="True"
                    VerticalExpand="True"
                    Text="MAP"
                    Margin="5"/>
            <Button Name="DockModeButton"
                    ToggleMode="True"
                    HorizontalExpand="True"
                    VerticalExpand="True"
                    Text="DOCK"
                    Margin="5"/>
        </BoxContainer>
        <!-- Contents box -->
        <BoxContainer Name="Contents"
                      HorizontalAlignment="Stretch"
                      VerticalExpand="True"
                      Margin="5">
            <ui:NavScreen Name="NavContainer" Visible="False"/>
            <ui:MapScreen Name="MapContainer" Visible="False"/>
            <ui:DockingScreen Name="DockContainer" Visible="False"/>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
