// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

// Shitmed Change Start

using Content.Shared.Smoking.Systems;

namespace Content.Client.Smoking;

public sealed class MatchstickSystem : SharedMatchstickSystem;

// Shitmed Change End