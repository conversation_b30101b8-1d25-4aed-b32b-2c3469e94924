<!--
SPDX-FileCopyrightText: 2024 Fildrance <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 username <<EMAIL>>
SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io">
    <BoxContainer Margin="8,8,8,8" Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <Label Name="StoreItemName" HorizontalExpand="True" />
            <!-- goob edit - fuck newstore -->
            <Button
                Name="StoreItemBuyButton"
                MinWidth="64"
                HorizontalAlignment="Right"
                Access="Public" />
        </BoxContainer>
        <PanelContainer StyleClasses="HighDivider" />
        <BoxContainer HorizontalExpand="True" Orientation="Horizontal">
            <TextureRect
                Name="StoreItemTexture"
                Margin="0,0,4,0"
                MinSize="48 48"
                Stretch="KeepAspectCentered" />
            <Control MinWidth="5"/>
            <RichTextLabel Name="StoreItemDescription" />
        </BoxContainer>
    </BoxContainer>
</Control>
