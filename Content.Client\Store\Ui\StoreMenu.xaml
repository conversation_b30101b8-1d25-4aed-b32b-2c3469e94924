<!--
SPDX-FileCopyrightText: 2023 Repo <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2024 <PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 keronshb <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow
    xmlns="https://spacestation14.io"
    xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    Title="{Loc 'store-ui-default-title'}"
    MinSize="512 512"
    SetSize="512 512">
    <BoxContainer Orientation="Vertical">
        <BoxContainer Orientation="Vertical" VerticalExpand="True">
            <BoxContainer Margin="4,4,4,4" Orientation="Horizontal">
                <RichTextLabel
                    Name="BalanceInfo"
                    HorizontalAlignment="Left"
                    Access="Public"
                    HorizontalExpand="True" />
                <Button
                    Name="WithdrawButton"
                    MinWidth="64"
                    HorizontalAlignment="Right"
                    Text="{Loc 'store-ui-default-withdraw-text'}" />
                <Button
                    Name="RefundButton"
                    MinWidth="64"
                    HorizontalAlignment="Right"
                    Text="Refund" />
            </BoxContainer>
            <LineEdit Name="SearchBar" Margin="4" PlaceHolder="Search" HorizontalExpand="True"/>
                <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#000000FF" />
                </PanelContainer.PanelOverride>
                <BoxContainer Orientation="Horizontal" VerticalExpand="True">
                    <PanelContainer VerticalExpand="True">
                        <PanelContainer.PanelOverride>
                            <gfx:StyleBoxFlat BackgroundColor="#80808005" />
                        </PanelContainer.PanelOverride>
                        <BoxContainer Name="CategoryListContainer" Orientation="Vertical">
                            <!--  Category buttons are added here by code  -->
                        </BoxContainer>
                    </PanelContainer>
                    <ScrollContainer
                        Name="StoreListingsScroll"
                        HScrollEnabled="False"
                        HorizontalExpand="True"
                        MinSize="100 256"
                        SizeFlagsStretchRatio="2"
                        VerticalExpand="True">
                        <BoxContainer
                            Name="StoreListingsContainer"
                            MinSize="100 256"
                            Orientation="Vertical"
                            SizeFlagsStretchRatio="2"
                            VerticalExpand="True">
                            <!--  Listings are added here by code  -->
                        </BoxContainer>
                    </ScrollContainer>
                </BoxContainer>
            </PanelContainer>
        </BoxContainer>

        <!-- Footer -->
        <BoxContainer Orientation="Vertical" Name="TraitorFooter">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'store-ui-traitor-flavor'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'store-ui-traitor-warning'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
