// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

namespace Content.Client.SubFloor;

/// <summary>
/// Added clientside if an entity is revealed for TRay.
/// </summary>
[RegisterComponent]
public sealed partial class TrayRevealedComponent : Component
{

}