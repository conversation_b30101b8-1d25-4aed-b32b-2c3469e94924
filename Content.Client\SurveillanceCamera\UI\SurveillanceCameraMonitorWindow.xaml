<!--
SPDX-FileCopyrightText: 2022 Flipp <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
               xmlns:viewport="clr-namespace:Content.Client.Viewport"
               Title="{Loc 'surveillance-camera-monitor-ui-window'}">
    <BoxContainer Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" MinWidth="350" VerticalExpand="True">
            <!-- lazy -->
            <OptionButton Name="SubnetSelector" />
            <Button Name="SubnetRefreshButton" Text="{Loc 'surveillance-camera-monitor-ui-refresh-subnets'}" />
            <ScrollContainer VerticalExpand="True">
                <ItemList Name="SubnetList" />
            </ScrollContainer>
            <Button Name="CameraRefreshButton" Text="{Loc 'surveillance-camera-monitor-ui-refresh-cameras'}" />
            <Button Name="CameraDisconnectButton" Text="{Loc 'surveillance-camera-monitor-ui-disconnect'}" />
            <Label Name="CameraStatus" />
        </BoxContainer>
        <Control VerticalExpand="True" HorizontalExpand="True" Margin="5 5 5 5" Name="CameraViewBox">
            <viewport:ScalingViewport Name="CameraView"
                                      VerticalExpand="True"
                                      HorizontalExpand="True"
                                      MinSize="500 500"
                                      MouseFilter="Ignore" />
            <TextureRect VerticalExpand="True" HorizontalExpand="True" MinSize="500 500" Name="CameraViewBackground" />
        </Control>
    </BoxContainer>
</DefaultWindow>
