<!--
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+Aiden<PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:viewport="clr-namespace:Content.Client.Viewport">
    <viewport:ScalingViewport Name="ScalingVp" MouseFilter="Stop" RenderScaleMode="CeilInt">
        <Button Name="FlipButton"
                Text="{ Loc 'tabletop-chess-flip' }"
                MinSize="60 30"
                MaxSize="60 30"
                HorizontalAlignment="Right"
                VerticalAlignment="Top" />
    </viewport:ScalingViewport>
</DefaultWindow>
