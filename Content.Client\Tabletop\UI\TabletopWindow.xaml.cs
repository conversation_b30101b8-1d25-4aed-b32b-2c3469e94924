// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Graphics;

namespace Content.Client.Tabletop.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class TabletopWindow : DefaultWindow
    {
        public TabletopWindow(IEye? eye, Vector2i size)
        {
            RobustXamlLoader.Load(this);

            ScalingVp.Eye = eye;
            ScalingVp.ViewportSize = size;

            FlipButton.OnButtonUp += Flip;
            OpenCentered();
        }

        private void Flip(BaseButton.ButtonEventArgs args)
        {
            // Flip the view 180 degrees
            if (ScalingVp.Eye is { } eye)
            {
                eye.Rotation = eye.Rotation.Opposite();

                // Flip alignmento of the button
                FlipButton.HorizontalAlignment = FlipButton.HorizontalAlignment == HAlignment.Right
                    ? HAlignment.Left
                    : HAlignment.Right;
            }
        }
    }
}