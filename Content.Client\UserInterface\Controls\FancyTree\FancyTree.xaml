<!--
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:FancyTree xmlns="https://spacestation14.io"
                    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls.FancyTree">
    <ScrollContainer ReturnMeasure="True">
        <BoxContainer Orientation="Vertical" Name="Body" Access="Public" Margin="2"/>
    </ScrollContainer>
</controls:FancyTree>
