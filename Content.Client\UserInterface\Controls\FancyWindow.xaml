<!--
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->


<controls:FancyWindow xmlns="https://spacestation14.io"
                      xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                      MouseFilter="Stop"
                      MinWidth="200" MinHeight="150">
    <PanelContainer StyleClasses="AngleRect" />

    <BoxContainer Orientation="Vertical">
        <Control>
            <PanelContainer StyleClasses="WindowHeadingBackground" />
            <BoxContainer Margin="4 2 8 0" Orientation="Horizontal">
                <Label Name="WindowTitle"
                       HorizontalExpand="True" VAlign="Center" StyleClasses="FancyWindowTitle" ClipText="True" />
                <TextureButton Name="HelpButton" StyleClasses="windowHelpButton" VerticalAlignment="Center" Disabled="True" Visible="False" Access="Public" />
                <TextureButton Name="CloseButton" StyleClasses="windowCloseButton"
                               VerticalAlignment="Center" />
            </BoxContainer>
        </Control>
        <PanelContainer StyleClasses="LowDivider" />
        <Control Access="Public" Name="ContentsContainer" Margin="0 2" RectClipContent="True" VerticalExpand="true" />
    </BoxContainer>
</controls:FancyWindow>
