// SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.Guidebook;
using Content.Client.Guidebook.Components;
using Content.Shared.Guidebook;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client.UserInterface.Controls
{
    [GenerateTypedNameReferences]
    [Virtual]
    public partial class FancyWindow : BaseWindow
    {
        [Dependency] private readonly IEntitySystemManager _sysMan = default!;
        private GuidebookSystem? _guidebookSystem;
        private const int DRAG_MARGIN_SIZE = 7;
        public const string StyleClassWindowHelpButton = "windowHelpButton";

        public FancyWindow()
        {
            RobustXamlLoader.Load(this);

            CloseButton.OnPressed += _ => Close();
            HelpButton.OnPressed += _ => Help();
            XamlChildren = ContentsContainer.Children;
        }

        public string? Title
        {
            get => WindowTitle.Text;
            set => WindowTitle.Text = value;
        }

        private List<ProtoId<GuideEntryPrototype>>? _helpGuidebookIds;
        public List<ProtoId<GuideEntryPrototype>>? HelpGuidebookIds
        {
            get => _helpGuidebookIds;
            set
            {
                _helpGuidebookIds = value;
                HelpButton.Disabled = _helpGuidebookIds == null;
                HelpButton.Visible = !HelpButton.Disabled;
            }
        }

        public void Help()
        {
            if (HelpGuidebookIds is null)
                return;
            _guidebookSystem ??= _sysMan.GetEntitySystem<GuidebookSystem>();
            _guidebookSystem.OpenHelp(HelpGuidebookIds);
        }

        protected override DragMode GetDragModeFor(Vector2 relativeMousePos)
        {
            var mode = DragMode.Move;

            if (Resizable)
            {
                if (relativeMousePos.Y < DRAG_MARGIN_SIZE)
                {
                    mode = DragMode.Top;
                }
                else if (relativeMousePos.Y > Size.Y - DRAG_MARGIN_SIZE)
                {
                    mode = DragMode.Bottom;
                }

                if (relativeMousePos.X < DRAG_MARGIN_SIZE)
                {
                    mode |= DragMode.Left;
                }
                else if (relativeMousePos.X > Size.X - DRAG_MARGIN_SIZE)
                {
                    mode |= DragMode.Right;
                }
            }

            return mode;
        }
    }

    /// <summary>
    /// Helper functions for working with <see cref="FancyWindow"/>.
    /// </summary>
    public static class FancyWindowExt
    {
        /// <summary>
        /// Sets information for a window (title and guidebooks) based on an entity.
        /// </summary>
        /// <param name="window">The window to modify.</param>
        /// <param name="entityManager">Entity manager used to retrieve the information.</param>
        /// <param name="entity">The entity that this window represents.</param>
        /// <seealso cref="SetTitleFromEntity"/>
        /// <seealso cref="SetGuidebookFromEntity"/>
        public static void SetInfoFromEntity(this FancyWindow window, IEntityManager entityManager, EntityUid entity)
        {
            window.SetTitleFromEntity(entityManager, entity);
            window.SetGuidebookFromEntity(entityManager, entity);
        }

        /// <summary>
        /// Set a window's title to the name of an entity.
        /// </summary>
        /// <param name="window">The window to modify.</param>
        /// <param name="entityManager">Entity manager used to retrieve the information.</param>
        /// <param name="entity">The entity that this window represents.</param>
        /// <seealso cref="SetInfoFromEntity"/>
        public static void SetTitleFromEntity(
            this FancyWindow window,
            IEntityManager entityManager,
            EntityUid entity)
        {
            window.Title = entityManager.GetComponent<MetaDataComponent>(entity).EntityName;
        }

        /// <summary>
        /// Set a window's guidebook IDs to those of an entity.
        /// </summary>
        /// <param name="window">The window to modify.</param>
        /// <param name="entityManager">Entity manager used to retrieve the information.</param>
        /// <param name="entity">The entity that this window represents.</param>
        /// <seealso cref="SetInfoFromEntity"/>
        public static void SetGuidebookFromEntity(
            this FancyWindow window,
            IEntityManager entityManager,
            EntityUid entity)
        {
            window.HelpGuidebookIds = entityManager.GetComponentOrNull<GuideHelpComponent>(entity)?.Guides;
        }
    }
}
