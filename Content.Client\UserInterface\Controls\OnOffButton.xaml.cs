﻿using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.UserInterface.Controls;

/// <summary>
/// A simple control that displays a toggleable on/off button.
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class OnOffButton : Control
{
    /// <summary>
    /// Whether the control is currently in the "on" state.
    /// </summary>
    public bool IsOn
    {
        get => OnButton.Pressed;
        set
        {
            if (value)
                OnButton.Pressed = true;
            else
                OffButton.Pressed = true;
        }
    }

    /// <summary>
    /// Raised when the user changes the state of the control.
    /// </summary>
    /// <remarks>
    /// This does not get raised if state is changed with <see cref="set_IsOn"/>.
    /// </remarks>
    public event Action<bool>? StateChanged;

    public OnOffButton()
    {
        RobustXamlLoader.Load(this);

        var group = new ButtonGroup(isNoneSetAllowed: false);
        OffButton.Group = group;
        OnButton.Group = group;

        OffButton.OnPressed += _ => StateChanged?.Invoke(false);
        OnButton.OnPressed += _ => StateChanged?.Invoke(true);
    }
}
