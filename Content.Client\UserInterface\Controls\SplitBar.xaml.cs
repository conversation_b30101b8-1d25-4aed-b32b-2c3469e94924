// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 qwerltaz <<EMAIL>>
// SPDX-FileCopyrightText: 2024 0x6273 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using System.Numerics;
using Robust.Client.AutoGenerated;
using Robust.Client.Graphics;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.UserInterface.Controls
{
    [GenerateTypedNameReferences]
    [Virtual]
    public partial class SplitBar : BoxContainer
    {
        public Vector2 MinBarSize = new(24, 0);

        public SplitBar()
        {
            RobustXamlLoader.Load(this);
        }

        public void Clear()
        {
            DisposeAllChildren();
        }

        public void AddEntry(float amount, Color color, string? tooltip = null)
        {
            AddChild(new PanelContainer
            {
                ToolTip = tooltip,
                HorizontalExpand = true,
                SizeFlagsStretchRatio = amount,
                MouseFilter = MouseFilterMode.Stop,
                PanelOverride = new StyleBoxFlat
                {
                    BackgroundColor = color,
                    PaddingLeft = 2f,
                    PaddingRight = 2f,
                },
                MinSize = MinBarSize
            });
        }
    }
}