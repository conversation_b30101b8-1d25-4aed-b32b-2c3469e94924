<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON>lip<PERSON> Syder <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aviu00 <<EMAIL>>
SPDX-FileCopyrightText: 2024 JoeHammad1844 <<EMAIL>>
SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
SPDX-FileCopyrightText: 2024 yglop <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<screens:DefaultGameScreen
    xmlns="https://spacestation14.io"
    xmlns:screens="clr-namespace:Content.Client.UserInterface.Screens"
    xmlns:menuBar="clr-namespace:Content.Client.UserInterface.Systems.MenuBar.Widgets"
    xmlns:actions="clr-namespace:Content.Client.UserInterface.Systems.Actions.Widgets"
    xmlns:chat="clr-namespace:Content.Client.UserInterface.Systems.Chat.Widgets"
    xmlns:alerts="clr-namespace:Content.Client.UserInterface.Systems.Alerts.Widgets"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Ghost.Widgets"
    xmlns:hotbar="clr-namespace:Content.Client.UserInterface.Systems.Hotbar.Widgets"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:inventory="clr-namespace:Content.Client.UserInterface.Systems.Inventory.Widgets"
    xmlns:targeting="clr-namespace:Content.Client._Shitmed.UserInterface.Systems.Targeting.Widgets"
    Name="DefaultHud"
    VerticalExpand="False"
    VerticalAlignment="Bottom"
    HorizontalAlignment="Center">
    <LayoutContainer Name="ViewportContainer" HorizontalExpand="True" VerticalExpand="True">
        <controls:MainViewport Name="MainViewport"/>
    </LayoutContainer>
    <BoxContainer Name="TopLeft" Access="Protected" Orientation="Vertical">
        <BoxContainer Orientation="Horizontal">
            <menuBar:GameTopMenuBar Name="TopBar" Access="Protected" />
            <!-- Buffer so big votes don't skew it -->
            <Control/>
        </BoxContainer>
        <BoxContainer Name="VoteMenu" Access="Public" Margin="0 10 0 10" Orientation="Vertical"/>
        <actions:ActionsBar Name="Actions" Access="Protected" />
    </BoxContainer>
    <widgets:GhostGui Name="Ghost" Access="Protected" />
    <inventory:InventoryGui Name="Inventory" Access="Protected" />
    <hotbar:HotbarGui Name="Hotbar" Access="Protected" />
    <targeting:TargetingControl Name="Targeting" Access="Protected"/> <!-- Shitmed Change -->
    <chat:ResizableChatBox Name="Chat" Access="Protected" />
    <alerts:AlertsUI Name="Alerts" Access="Protected" />
</screens:DefaultGameScreen>
