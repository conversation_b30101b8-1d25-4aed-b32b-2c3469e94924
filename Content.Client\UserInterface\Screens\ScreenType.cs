// SPDX-FileCopyrightText: 2022 Flipp <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

namespace Content.Client.UserInterface.Screens;

public enum ScreenType
{
    /// <summary>
    ///     The modern SS14 user interface.
    /// </summary>
    Default,
    /// <summary>
    ///     The classic SS13 user interface.
    /// </summary>
    Separated
}