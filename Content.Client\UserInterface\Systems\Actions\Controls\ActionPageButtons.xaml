<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2023 Skye <22365940+<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<controls:ActionPageButtons
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Systems.Actions.Controls">
    <BoxContainer Orientation="Horizontal">
        <Control HorizontalExpand="True" SizeFlagsStretchRatio="1"/>
        <TextureButton TexturePath="left_arrow.svg.192dpi"
                       SizeFlagsStretchRatio="1"
                       Scale="0.5 0.5"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Name="LeftArrow"
                       Access="Public"/>
        <Control HorizontalExpand="True" SizeFlagsStretchRatio="2"/>
        <Label Text="1" SizeFlagsStretchRatio="1" Name="Label" Access="Public" />
        <Control HorizontalExpand="True" SizeFlagsStretchRatio="2"/>
        <TextureButton TexturePath="right_arrow.svg.192dpi"
                       SizeFlagsStretchRatio="1"
                       Scale="0.5 0.5"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Name="RightArrow"
                       Access="Public"/>
        <Control HorizontalExpand="True" SizeFlagsStretchRatio="1"/>
    </BoxContainer>
</controls:ActionPageButtons>
