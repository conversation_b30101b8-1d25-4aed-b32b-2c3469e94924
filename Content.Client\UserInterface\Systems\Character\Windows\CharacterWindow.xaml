<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 Errant <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<windows:CharacterWindow
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:windows="clr-namespace:Content.Client.UserInterface.Systems.Character.Windows"
    Title="{Loc 'character-info-title'}"
    MinWidth="400"
    MinHeight="545">
    <ScrollContainer>
        <BoxContainer Orientation="Vertical">
            <Label Name="RoleType" VerticalAlignment="Top" Margin="0 6 0 10" HorizontalAlignment="Center" StyleClasses="LabelHeading" Access="Public"/>
            <BoxContainer Orientation="Horizontal">
                <SpriteView OverrideDirection="South" Scale="2 2" Name="SpriteView" Access="Public" SetSize="64 64"/>
                <BoxContainer Orientation="Vertical" VerticalAlignment="Top">
                    <Label Name="NameLabel" Access="Public"/>
                    <Label Name="SubText" VerticalAlignment="Top" StyleClasses="LabelSubText" Access="Public"/>
                </BoxContainer>
            </BoxContainer>
            <Label Name="ObjectivesLabel" Access="Public" Text="{Loc 'character-info-objectives-label'}" HorizontalAlignment="Center"/>
            <BoxContainer Orientation="Vertical" Name="Objectives" Access="Public"/>
            <cc:Placeholder Name="RolePlaceholder" Access="Public" PlaceholderText="{Loc 'character-info-roles-antagonist-text'}"/>
        </BoxContainer>
    </ScrollContainer>
</windows:CharacterWindow>
