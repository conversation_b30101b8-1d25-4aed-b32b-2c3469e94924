using Content.Client.UserInterface.Controls;
using Content.Shared.Chat;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client.UserInterface.Systems.Chat;

/// <summary>
/// Window which only holds a single chatbox, useful for monitoring multiple chats simultaneously.
/// </summary>
[GenerateTypedNameReferences]
public sealed partial class ChatWindow : FancyWindow
{
    public ChatWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        // These are necessary for the controls inside the chatbox to initialize correctly:
        Chatbox.Repopulate();
        var controller = UserInterfaceManager.GetUIController<ChatUIController>();
        controller.UpdateSelectedChannel(Chatbox);
    }

    /// <summary>
    /// Helper method to configure this window to be useful for admins.
    /// Sets incoming filters to only admin chats and output to admin channel
    /// </summary>
    public void ConfigureForAdminChat()
    {
        Chatbox.ChatInput.ChannelSelector.Select(ChatSelectChannel.Admin);

        var filter = Chatbox.ChatInput.FilterButton.Popup;
        foreach (var c in Enum.GetValues(typeof(ChatChannel)))
        {
            var channel = (ChatChannel)c;
            var isAdminInterest = channel == ChatChannel.Admin
                                  || channel == ChatChannel.AdminChat
                                  || channel == ChatChannel.AdminAlert
                                  || channel == ChatChannel.AdminRelated;
            filter.SetActive(channel, isAdminInterest);
        }
    }
}
