<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 <PERSON> <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:ChannelFilterPopup
    xmlns="https://spacestation14.io"
    xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Systems.Chat.Controls">
    <PanelContainer Name="FilterPopupPanel" StyleClasses="BorderedWindowPanel">
<!--        <BoxContainer Orientation="Horizontal">
            <Control MinSize="4 0"/>
            <BoxContainer Name="FilterVBox" MinWidth="110" Margin="0 10" Orientation="Vertical" SeparationOverride="4"/> -->
     <!-- Goobstation - start -->
        <BoxContainer Orientation="Horizontal" SeparationOverride="8" Margin="10 0">
            <BoxContainer Name="FilterVBox" MinWidth="105" Margin="0 10" Orientation="Vertical" SeparationOverride="4"/>
            <BoxContainer Name="HighlightsVBox" MinWidth="120" Margin="0 10" Orientation="Vertical" SeparationOverride="4">
                <Label Text="{Loc 'hud-chatbox-highlights'}"/>
                <PanelContainer>
                    <!-- Begin custom background for TextEdit -->
                    <PanelContainer.PanelOverride>
                        <gfx:StyleBoxFlat BackgroundColor="#323446"/>
                    </PanelContainer.PanelOverride>
                    <!-- End custom background -->
                    <TextEdit Name="HighlightEdit" MinHeight="150" Margin="5 5"/>
                </PanelContainer>
                <Button Name="HighlightButton" Text="{Loc 'hud-chatbox-highlights-button'}" ToolTip="{Loc 'hud-chatbox-highlights-tooltip'}"/>
            </BoxContainer>
    <!-- Goobstation - end -->
        </BoxContainer>
    </PanelContainer>
</controls:ChannelFilterPopup>
