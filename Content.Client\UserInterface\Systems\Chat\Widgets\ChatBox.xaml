<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Flip<PERSON> Syder <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vasilis <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 Sk1tch <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<widgets:ChatBox
    xmlns="https://spacestation14.io"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Chat.Widgets"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Systems.Chat.Controls"
    xmlns:goobcontrols="clr-namespace:Content.Goobstation.UIKit.UserInterface.Controls;assembly=Content.Goobstation.UIKit"
    MouseFilter="Stop"
    HorizontalExpand="True"
    VerticalExpand="True"
    MinSize="465 225">
    <PanelContainer Name="ChatWindowPanel" Access="Public" HorizontalExpand="True" VerticalExpand="True"
                    StyleClasses="StyleNano.StyleClassChatPanel">
        <BoxContainer Orientation="Vertical" SeparationOverride="4" HorizontalExpand="True" VerticalExpand="True">
            <!-- Goobstation Change - Output Panel -->
            <goobcontrols:CustomOutputPanel Name="Contents" HorizontalExpand="True" VerticalExpand="True" Margin="8 8 8 4" />
            <controls:ChatInputBox HorizontalExpand="True" Name="ChatInput" Access="Public" Margin="2"/>
        </BoxContainer>
    </PanelContainer>
</widgets:ChatBox>
