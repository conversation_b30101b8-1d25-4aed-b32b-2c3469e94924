// SPDX-FileCopyrightText: 2022 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Flipp Syder <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 jicksaw <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 0x6273 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nikolai Korolev <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RedFoxIV <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Ted Lukin <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Systems.Chat.Controls;
using Content.Goobstation.Common.CCVar; // Goobstation Change
using Content.Shared.Chat;
using Content.Shared.Input;
using Robust.Client.Audio;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Audio;
using Robust.Shared.Configuration;
using Robust.Shared.Input;
using Robust.Shared.Player;
using Robust.Shared.Utility;
using static Robust.Client.UserInterface.Controls.LineEdit;
using System.Text.RegularExpressions; // Goob

namespace Content.Client.UserInterface.Systems.Chat.Widgets;

[GenerateTypedNameReferences]
[Virtual]
public partial class ChatBox : UIWidget
{
    private readonly ChatUIController _controller;
    private readonly IEntityManager _entManager;
    private readonly IConfigurationManager _cfg; // WD EDIT
    private readonly ILocalizationManager _loc; // WD EDIT

    public bool Main { get; set; }

    public ChatSelectChannel SelectedChannel => ChatInput.ChannelSelector.SelectedChannel;
    // WD EDIT START
    private bool _coalescence = false; // op ult btw
    private (string, Color)? _lastLine;
    private int _lastLineRepeatCount = 0;
    // WD EDIT END

    public ChatBox()
    {
        RobustXamlLoader.Load(this);
        _loc = IoCManager.Resolve<ILocalizationManager>();
        _entManager = IoCManager.Resolve<IEntityManager>();

        ChatInput.Input.OnTextEntered += OnTextEntered;
        ChatInput.Input.OnKeyBindDown += OnInputKeyBindDown;
        ChatInput.Input.OnTextChanged += OnTextChanged;
        ChatInput.Input.OnFocusEnter += OnFocusEnter;
        ChatInput.Input.OnFocusExit += OnFocusExit;
        ChatInput.ChannelSelector.OnChannelSelect += OnChannelSelect;
        ChatInput.FilterButton.Popup.OnChannelFilter += OnChannelFilter;
        ChatInput.FilterButton.Popup.OnNewHighlights += OnNewHighlights; // Goob

        _controller = UserInterfaceManager.GetUIController<ChatUIController>();
        _controller.MessageAdded += OnMessageAdded;
        _controller.HighlightsUpdated += OnHighlightsUpdated; // Goob
        _controller.RegisterChat(this);

        // WD EDIT START
        _cfg = IoCManager.Resolve<IConfigurationManager>();
        _coalescence = _cfg.GetCVar(GoobCVars.CoalesceIdenticalMessages); // i am uncomfortable calling repopulate on chatbox in its ctor, even though it worked in testing i'll still err on the side of caution
        _cfg.OnValueChanged(GoobCVars.CoalesceIdenticalMessages, UpdateCoalescence, false); // eplicitly false to underline the above comment
        // WD EDIT END
    }

    private void UpdateCoalescence(bool value) { _coalescence = value; Repopulate(); } // WD EDIT

    private void OnTextEntered(LineEditEventArgs args)
    {
        _controller.SendMessage(this, SelectedChannel);
    }

    private void OnMessageAdded(ChatMessage msg)
    {
        Logger.DebugS("chat", $"{msg.Channel}: {msg.Message}");
        if (!ChatInput.FilterButton.Popup.IsActive(msg.Channel))
        {
            return;
        }

        if (msg is { Read: false, AudioPath: { } })
            _entManager.System<AudioSystem>().PlayGlobal(msg.AudioPath, Filter.Local(), false, AudioParams.Default.WithVolume(msg.AudioVolume));

        msg.Read = true;

        var color = msg.MessageColorOverride ?? msg.Channel.TextColor();

        // WD EDIT START
        (string, Color) tup = (msg.WrappedMessage, color);

        // Removing and then adding insantly nudges the chat window up before slowly dragging it back down, which makes the whole chat log shake
        // and make it borderline unreadable with frequent enough spam.
        // Adding first and then removing does not produce any visual effects.
        // The other option is to copypaste into Content all of OutputPanel and everything it uses but is intertanl to Robust namespace.
        // Thanks robustengine, very cool.
        if (_coalescence && _lastLine == tup)
        {
            if (!msg.CanCoalesce) // Goobstation Edit - Coalescing Chat
                return;

            _lastLineRepeatCount++;
            AddLine(msg.WrappedMessage, color, _lastLineRepeatCount);
            Contents.RemoveEntry(^2);
        }
        else
        {
            _lastLineRepeatCount = 0;
            _lastLine = (msg.WrappedMessage, color);
            AddLine(msg.WrappedMessage, color, _lastLineRepeatCount);
        } // WD EDIT END
    }

    private void OnChannelSelect(ChatSelectChannel channel)
    {
        _controller.UpdateSelectedChannel(this);
    }

    public void Repopulate()
    {
        Contents.Clear();

        foreach (var message in _controller.History)
        {
            OnMessageAdded(message.Item2);
        }
    }

    private void OnChannelFilter(ChatChannel channel, bool active)
    {
        Contents.Clear();

        foreach (var message in _controller.History)
        {
            OnMessageAdded(message.Item2);
        }

        if (active)
        {
            _controller.ClearUnfilteredUnreads(channel);
        }
    }

    public void AddLine(string message, Color color, int repeat = 0) // WD EDIT
    {
        var formatted = new FormattedMessage(4); // WD EDIT // specifying size beforehand smells like a useless microoptimisation, but i'll give them the benefit of doubt
        formatted.PushColor(color);
        formatted.AddMarkupOrThrow(message);
        formatted.Pop();
        if(repeat != 0) // WD EDIT START
        {
            int displayRepeat = repeat + 1;
            int sizeIncrease = Math.Min(displayRepeat / 6, 5);
            formatted.AddMarkup(_loc.GetString("chat-system-repeated-message-counter",
                                ("count", displayRepeat),
                                ("size", 8+sizeIncrease)
                                ));
        } // WD EDIT END
        Contents.AddMessage(formatted);
    }

    public void Focus(ChatSelectChannel? channel = null)
    {
        var input = ChatInput.Input;
        var selectStart = Index.End;

        if (channel != null)
            ChatInput.ChannelSelector.Select(channel.Value);

        input.IgnoreNext = true;
        input.GrabKeyboardFocus();

        input.CursorPosition = input.Text.Length;
        input.SelectionStart = selectStart.GetOffset(input.Text.Length);
    }

    public void CycleChatChannel(bool forward)
    {
        var idx = Array.IndexOf(ChannelSelectorPopup.ChannelSelectorOrder, SelectedChannel);
        do
        {
            // go over every channel until we find one we can actually select.
            idx += forward ? 1 : -1;
            idx = MathHelper.Mod(idx, ChannelSelectorPopup.ChannelSelectorOrder.Length);
        } while ((_controller.SelectableChannels & ChannelSelectorPopup.ChannelSelectorOrder[idx]) == 0);

        SafelySelectChannel(ChannelSelectorPopup.ChannelSelectorOrder[idx]);
    }

    public void SafelySelectChannel(ChatSelectChannel toSelect)
    {
        toSelect = _controller.MapLocalIfGhost(toSelect);
        if ((_controller.SelectableChannels & toSelect) == 0)
            return;

        ChatInput.ChannelSelector.Select(toSelect);
    }

    private void OnInputKeyBindDown(GUIBoundKeyEventArgs args)
    {
        if (args.Function == EngineKeyFunctions.TextReleaseFocus)
        {
            ChatInput.Input.ReleaseKeyboardFocus();
            ChatInput.Input.Clear();
            args.Handle();
            return;
        }

        if (args.Function == ContentKeyFunctions.CycleChatChannelForward)
        {
            CycleChatChannel(true);
            args.Handle();
            return;
        }

        if (args.Function == ContentKeyFunctions.CycleChatChannelBackward)
        {
            CycleChatChannel(false);
            args.Handle();
        }
    }

    private void OnTextChanged(LineEditEventArgs args)
    {
        // Update channel select button to correct channel if we have a prefix.
        _controller.UpdateSelectedChannel(this);

        // Warn typing indicator about change
        _controller.NotifyChatTextChange();
    }

    private void OnFocusEnter(LineEditEventArgs args)
    {
        // Warn typing indicator about focus
        _controller.NotifyChatFocus(true);
    }

    private void OnFocusExit(LineEditEventArgs args)
    {
        // Warn typing indicator about focus
        _controller.NotifyChatFocus(false);
    }

    protected override void Dispose(bool disposing)
    {
        base.Dispose(disposing);

        if (!disposing) return;
        _controller.UnregisterChat(this);
        ChatInput.Input.OnTextEntered -= OnTextEntered;
        ChatInput.Input.OnKeyBindDown -= OnInputKeyBindDown;
        ChatInput.Input.OnTextChanged -= OnTextChanged;
        ChatInput.ChannelSelector.OnChannelSelect -= OnChannelSelect;
        _cfg.UnsubValueChanged(GoobCVars.CoalesceIdenticalMessages, UpdateCoalescence); // WD EDIT
    }
    // Goobstation - start
    private void OnHighlightsUpdated(string highlights)
    {
        ChatInput.FilterButton.Popup.UpdateHighlights(highlights);
    }
        private void OnNewHighlights(string highlighs)
    {
        _controller.UpdateHighlights(highlighs);
    }
    // Goobstation - end
}