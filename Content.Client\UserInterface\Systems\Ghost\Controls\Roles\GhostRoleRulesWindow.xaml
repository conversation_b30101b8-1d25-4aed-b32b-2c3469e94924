<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Ray <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: MIT
-->

<DefaultWindow xmlns="https://spacestation14.io"
            Title="{Loc 'ghost-roles-window-title'}"
            MinSize="500 300"
            SetSize="500 300">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True">
        <RichTextLabel Name="TopBanner" VerticalExpand="True"/>
        <Button Name="RequestButton"
                Text="{Loc 'ghost-roles-window-request-role-button'}"
                Disabled="True"
                TextAlign="Center"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" />
    </BoxContainer>
</DefaultWindow>
