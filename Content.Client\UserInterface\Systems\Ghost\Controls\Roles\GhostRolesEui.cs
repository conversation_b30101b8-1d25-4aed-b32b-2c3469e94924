// SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <6766154+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2022 Alex <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon <PERSON> <103440971+<PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MilenVolf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 no <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Singul0 <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Client.Eui;
using Content.Client.Players.PlayTimeTracking;
using Content.Shared.Eui;
using Content.Shared.Ghost.Roles;
using JetBrains.Annotations;
using Robust.Client.GameObjects;

namespace Content.Client.UserInterface.Systems.Ghost.Controls.Roles
{
    [UsedImplicitly]
    public sealed class GhostRolesEui : BaseEui
    {
        private readonly GhostRolesWindow _window;
        private GhostRoleRulesWindow? _windowRules = null;
        private uint _windowRulesId = 0;

        public GhostRolesEui()
        {
            _window = new GhostRolesWindow();

            _window.OnRoleRequestButtonClicked += info =>
            {
                _windowRules?.Close();

                if (info.Kind == GhostRoleKind.RaffleJoined)
                {
                    SendMessage(new LeaveGhostRoleRaffleMessage(info.Identifier));
                    return;
                }

                _windowRules = new GhostRoleRulesWindow(info.Rules, _ =>
                {
                    SendMessage(new RequestGhostRoleMessage(info.Identifier));

                    // if raffle role, close rules window on request, otherwise do
                    // old behavior of waiting for the server to close it
                    if (info.Kind != GhostRoleKind.FirstComeFirstServe)
                        _windowRules?.Close();
                });
                _windowRulesId = info.Identifier;
                _windowRules.OnClose += () =>
                {
                    _windowRules = null;
                };
                _windowRules.OpenCentered();
            };

            _window.OnRoleFollow += info =>
            {
                SendMessage(new FollowGhostRoleMessage(info.Identifier));
            };

            _window.OnClose += () =>
            {
                SendMessage(new CloseEuiMessage());
            };
        }

        public override void Opened()
        {
            base.Opened();
            _window.OpenCentered();
        }

        public override void Closed()
        {
            base.Closed();
            _window.Close();
            _windowRules?.Close();
        }

        public override void HandleState(EuiStateBase state)
        {
            base.HandleState(state);

            if (state is not GhostRolesEuiState ghostState)
                return;

            // We must save BodyVisible state, so all Collapsible boxes will not close
            // on adding new ghost role.
            // Save the current state of each Collapsible box being visible or not
            _window.SaveCollapsibleBoxesStates();

            // Clearing the container before adding new roles
            _window.ClearEntries();

            var entityManager = IoCManager.Resolve<IEntityManager>();
            var sysManager = entityManager.EntitySysManager;
            var spriteSystem = sysManager.GetEntitySystem<SpriteSystem>();
            var requirementsManager = IoCManager.Resolve<JobRequirementsManager>();

            // We only use the name and description for grouping,
            // and take the requirements from the first role in each group.
            // TLDR: DON'T USE THE SAME NAME AND DESC FOR DIFFERENT ROLE REQUIREMENTS OR I WILL BEAT YOU WITH HAMMERS (PLURAL)
            var groupedRoles = ghostState.GhostRoles.GroupBy(
                role => (role.Name, role.Description)); //goobstation edit, less polluted ghost spawners menu

            // Add a new entry for each role group
            foreach (var group in groupedRoles)
            {
                var name = group.Key.Name;
                var description = group.Key.Description;
                var groupReq = group.First(); //goobstation edit - since reqs can't be grouped fuckery
                var hasAccess = requirementsManager.CheckRoleRequirements(
                    groupReq.Requirements, //goobstation edit, less polluted ghost spawners menu
                    null,
                    out var reason);

                // Adding a new role
                _window.AddEntry(name, description, hasAccess, reason, group, spriteSystem);
            }

            // Restore the Collapsible box state if it is saved
            _window.RestoreCollapsibleBoxesStates();

            // Close the rules window if it is no longer needed
            var closeRulesWindow = ghostState.GhostRoles.All(role => role.Identifier != _windowRulesId);
            if (closeRulesWindow)
                _windowRules?.Close();
        }
    }
}
