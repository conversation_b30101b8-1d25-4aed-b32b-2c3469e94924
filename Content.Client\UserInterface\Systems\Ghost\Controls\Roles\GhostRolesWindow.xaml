<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 Alex <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Ray <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2024 no <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
            Title="{Loc 'ghost-roles-window-title'}"
            MinSize="490 400"
            SetSize="490 500">
    <Label Name="NoRolesMessage"
           Text="{Loc 'ghost-roles-window-no-roles-available-label'}"
           VerticalAlignment="Top" />
    <ScrollContainer VerticalExpand="True"
                     HScrollEnabled="False">
        <BoxContainer Orientation="Vertical"
                      Name="EntryContainer"
                      VerticalExpand="True">
            <!-- Ghost role entries are added here by code -->
        </BoxContainer>
    </ScrollContainer>
</DefaultWindow>
