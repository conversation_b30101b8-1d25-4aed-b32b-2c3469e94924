<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<widgets:GhostGui xmlns="https://spacestation14.io"
                  xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Ghost.Widgets"
                  HorizontalAlignment="Center">
    <BoxContainer Orientation="Horizontal">
        <Button Name="ReturnToBodyButton" Text="{Loc ghost-gui-return-to-body-button}" />
        <Button Name="GhostWarpButton" Text="{Loc ghost-gui-ghost-warp-button}" />
        <Button Name="GhostRolesButton" />
        <Button Name="GhostBarButton" Text="{Loc 'ghost-target-window-ghostbar'}" /> <!-- Goobstation - Ghost Bar -->
    </BoxContainer>
</widgets:GhostGui>
