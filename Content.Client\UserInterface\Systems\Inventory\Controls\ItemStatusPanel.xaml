<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:ItemStatusPanel
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Systems.Inventory.Controls"
    xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
    VerticalAlignment="Bottom"
    HorizontalAlignment="Center">
    <PanelContainer Name="Panel">
        <PanelContainer.PanelOverride>
            <graphics:StyleBoxTexture
                PatchMarginBottom="4"
                PatchMarginTop="6"
                TextureScale="2 2"
                Mode="Tile"/>
        </PanelContainer.PanelOverride>
    </PanelContainer>
    <PanelContainer Name="HighlightPanel">
        <PanelContainer.PanelOverride>
            <graphics:StyleBoxTexture PatchMarginBottom="4" PatchMarginTop="6" TextureScale="2 2">
            </graphics:StyleBoxTexture>
        </PanelContainer.PanelOverride>
    </PanelContainer>
    <BoxContainer Name="Contents" Orientation="Vertical" Margin="0 6 0 4" RectClipContent="True">
        <BoxContainer Name="StatusContents" Orientation="Vertical" VerticalExpand="True" VerticalAlignment="Bottom" />
        <Control>
            <Label Name="NoItemLabel" ClipText="True" StyleClasses="ItemStatusNotHeld" Align="Left" Text="{Loc 'item-status-not-held'}" />
            <Label Name="ItemNameLabel" ClipText="True" StyleClasses="ItemStatus" Align="Left" Visible="False" />
        </Control>
    </BoxContainer>
</controls:ItemStatusPanel>
