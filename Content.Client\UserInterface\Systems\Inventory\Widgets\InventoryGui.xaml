<!--
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<widgets:InventoryGui
    xmlns="https://spacestation14.io"
    xmlns:inventory="clr-namespace:Content.Client.UserInterface.Systems.Inventory.Controls"
    xmlns:widgets="clr-namespace:Content.Client.UserInterface.Systems.Inventory.Widgets"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    Name="InventoryInterface"
    VerticalExpand="True"
    VerticalAlignment="Bottom"
    Orientation="Horizontal"
    HorizontalAlignment="Center">
    <Control HorizontalAlignment="Center">
        <!-- Needs to default to invisible because if we attach to a non-slots entity this will never get unset -->
        <controls:SlotButton
            Name="InventoryButton"
            Access="Public"
            Visible="False"
            VerticalAlignment="Bottom"
            HorizontalExpand="False"
            VerticalExpand="False"
            ButtonTexturePath="Slots/toggle"/>
        <inventory:ItemSlotButtonContainer
            Name="InventoryHotbar"
            Access="Public"
            Visible="False"
            MaxColumns="3"
            SlotGroup="Default"
            ExpandBackwards="True"
            VerticalExpand="True"
            HorizontalAlignment="Center"
        />
    </Control>
</widgets:InventoryGui>
