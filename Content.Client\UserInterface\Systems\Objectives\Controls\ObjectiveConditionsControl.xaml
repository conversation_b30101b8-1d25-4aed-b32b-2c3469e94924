<!--
SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: MIT
-->

<controls:ObjectiveConditionsControl
    xmlns="https://spacestation14.io"
    xmlns:cc="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Systems.Objectives.Controls"
    Orientation="Horizontal">
    <cc:ProgressTextureRect Name="ProgressTexture" VerticalAlignment="Top" Access="Public" Margin="0 8 0 0"/>
    <Control MinSize="10 0"/>
    <BoxContainer Orientation="Vertical">
        <RichTextLabel Name="Title" Access="Public" SetWidth="325" HorizontalAlignment="Left"/>
        <RichTextLabel Name="Description" Access="Public" SetWidth="325" HorizontalAlignment="Left"/>
    </BoxContainer>
</controls:ObjectiveConditionsControl>
