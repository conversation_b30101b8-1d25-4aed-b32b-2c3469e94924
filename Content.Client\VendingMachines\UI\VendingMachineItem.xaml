<!--
SPDX-FileCopyrightText: 2024 Plykiya <58439124+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<BoxContainer xmlns="https://spacestation14.io"
            Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="4">
    <EntityPrototypeView
            Name="ItemPrototype"
            Margin="4 0 0 0"
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            MinSize="32 32"
            />
    <Label Name="NameLabel"
            SizeFlagsStretchRatio="3"
            HorizontalExpand="True"
            ClipText="True"/>
</BoxContainer>
