<!--
SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Nemanja <<EMAIL>>
SPDX-FileCopyrightText: 2023 ike709 <<EMAIL>>
SPDX-FileCopyrightText: 2023 themias <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
SPDX-FileCopyrightText: 2024 scrivoy <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns:co="clr-namespace:Content.Client.UserInterface.Controls"
    MinHeight="210">
    <BoxContainer Name="MainContainer" Orientation="Vertical">
        <LineEdit Name="SearchBar" PlaceHolder="{Loc 'vending-machine-component-search-filter'}" HorizontalExpand="True"  Margin ="4 4"/>
        <co:SearchListContainer Name="VendingContents" VerticalExpand="True" Margin="4 4"/>
         <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'vending-machine-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'vending-machine-flavor-right'}" StyleClasses="WindowFooterText"
                        HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                        VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
