// SPDX-FileCopyrightText: 2019 DamianX <<EMAIL>>
// SPDX-FileCopyrightText: 2019 ZelteHonor <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
// SPDX-FileCopyrightText: 2021 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Fishfish458 <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Flipp Syder <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 fishfish458 <fishfish458>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2023 ike709 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 12rabbits <<EMAIL>>
// SPDX-FileCopyrightText: 2024 AJCM <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alex Evgrashin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alex Pavlenko <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alice "Arimah" Heurlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArkiveDev <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ArtisticRoomba <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Boaz1111 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Dimastra <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Doomsdrayk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrEnzyme <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Elysium206 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Floofi <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ghagliiarghii <<EMAIL>>
// SPDX-FileCopyrightText: 2024 HS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JIPDawg <<EMAIL>>
// SPDX-FileCopyrightText: 2024 James Simonson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 K-Dynamic <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kevin Zheng <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MetalSage <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MilenVolf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Moomoobeef <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Partmedia <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PopGamer46 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PursuitInAshes <<EMAIL>>
// SPDX-FileCopyrightText: 2024 QueerNB <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Redfire1331 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rouge2t7 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Saphire Lattice <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Spessmann <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Tayrtahn <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Truoizys <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 WarMechanic <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Winkarst <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 marbow <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 mhamster <<EMAIL>>
// SPDX-FileCopyrightText: 2024 neutrino <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 osjarw <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 redfire1331 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stellar-novas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Арт <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Shared.VendingMachines;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using FancyWindow = Content.Client.UserInterface.Controls.FancyWindow;
using Robust.Client.UserInterface;
using Content.Client.UserInterface.Controls;
using Content.Shared.IdentityManagement;
using Robust.Client.Graphics;
using Robust.Shared.Utility;

namespace Content.Client.VendingMachines.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class VendingMachineMenu : FancyWindow
    {
        [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
        [Dependency] private readonly IEntityManager _entityManager = default!;

        private readonly Dictionary<EntProtoId, EntityUid> _dummies = [];
        private readonly Dictionary<EntProtoId, (ListContainerButton Button, VendingMachineItem Item)> _listItems = new();
        private readonly Dictionary<EntProtoId, uint> _amounts = new();

        /// <summary>
        /// Whether the vending machine is able to be interacted with or not.
        /// </summary>
        private bool _enabled;

        public event Action<GUIBoundKeyEventArgs, ListData>? OnItemSelected;

        public VendingMachineMenu()
        {
            MinSize = SetSize = new Vector2(250, 150);
            RobustXamlLoader.Load(this);
            IoCManager.InjectDependencies(this);

            VendingContents.SearchBar = SearchBar;
            VendingContents.DataFilterCondition += DataFilterCondition;
            VendingContents.GenerateItem += GenerateButton;
            VendingContents.ItemKeyBindDown += (args, data) => OnItemSelected?.Invoke(args, data);
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);

            // Don't clean up dummies during disposal or we'll just have to spawn them again
            if (!disposing)
                return;

            // Delete any dummy items we spawned
            foreach (var entity in _dummies.Values)
            {
                _entityManager.QueueDeleteEntity(entity);
            }
            _dummies.Clear();
        }

        private bool DataFilterCondition(string filter, ListData data)
        {
            if (data is not VendorItemsListData { ItemText: var text })
                return false;

            if (string.IsNullOrEmpty(filter))
                return true;

            return text.Contains(filter, StringComparison.CurrentCultureIgnoreCase);
        }

        private void GenerateButton(ListData data, ListContainerButton button)
        {
            if (data is not VendorItemsListData { ItemProtoID: var protoID, ItemText: var text })
                return;

            var item = new VendingMachineItem(protoID, text);
            _listItems[protoID] = (button, item);
            button.AddChild(item);
            button.AddStyleClass("ButtonSquare");
            button.Disabled = !_enabled || _amounts[protoID] == 0;
        }

        /// <summary>
        /// Populates the list of available items on the vending machine interface
        /// and sets icons based on their prototypes
        /// </summary>
        public void Populate(List<VendingMachineInventoryEntry> inventory, bool enabled)
        {
            _enabled = enabled;
            _listItems.Clear();
            _amounts.Clear();

            if (inventory.Count == 0 && VendingContents.Visible)
            {
                SearchBar.Visible = false;
                VendingContents.Visible = false;

                var outOfStockLabel = new Label()
                {
                    Text = Loc.GetString("vending-machine-component-try-eject-out-of-stock"),
                    Margin = new Thickness(4, 4),
                    HorizontalExpand = true,
                    VerticalAlignment = VAlignment.Stretch,
                    HorizontalAlignment = HAlignment.Center
                };

                MainContainer.AddChild(outOfStockLabel);

                SetSizeAfterUpdate(outOfStockLabel.Text.Length, 0);

                return;
            }

            var longestEntry = string.Empty;
            var listData = new List<VendorItemsListData>();

            for (var i = 0; i < inventory.Count; i++)
            {
                var entry = inventory[i];

                if (!_prototypeManager.TryIndex(entry.ID, out var prototype))
                {
                    _amounts[entry.ID] = 0;
                    continue;
                }

                if (!_dummies.TryGetValue(entry.ID, out var dummy))
                {
                    dummy = _entityManager.Spawn(entry.ID);
                    _dummies.Add(entry.ID, dummy);
                }

                var itemName = Identity.Name(dummy, _entityManager);
                var itemText = $"{itemName} [{entry.Amount}]";
                _amounts[entry.ID] = entry.Amount;

                if (itemText.Length > longestEntry.Length)
                    longestEntry = itemText;

                listData.Add(new VendorItemsListData(prototype.ID, i)
                {
                    ItemText = itemText,
                });
            }

            VendingContents.PopulateList(listData);

            SetSizeAfterUpdate(longestEntry.Length, inventory.Count);
        }

        /// <summary>
        /// Updates text entries for vending data in place without modifying the list controls.
        /// </summary>
        public void UpdateAmounts(List<VendingMachineInventoryEntry> cachedInventory, bool enabled)
        {
            _enabled = enabled;

            foreach (var proto in _dummies.Keys)
            {
                if (!_listItems.TryGetValue(proto, out var button))
                    continue;

                var dummy = _dummies[proto];
                if (!cachedInventory.TryFirstOrDefault(o => o.ID == proto, out var entry))
                    continue;
                var amount = entry.Amount;
                // Could be better? Problem is all inventory entries get squashed.
                var text = GetItemText(dummy, amount);

                button.Item.SetText(text);
                button.Button.Disabled = !enabled || amount == 0;
            }
        }

        private string GetItemText(EntityUid dummy, uint amount)
        {
            var itemName = Identity.Name(dummy, _entityManager);
            return $"{itemName} [{amount}]";
        }

        private void SetSizeAfterUpdate(int longestEntryLength, int contentCount)
        {
            SetSize = new Vector2(Math.Clamp((longestEntryLength + 2) * 12, 250, 400),
                Math.Clamp(contentCount * 50, 150, 350));
        }
    }

    public record VendorItemsListData(EntProtoId ItemProtoID, int ItemIndex) : ListData
    {
        public string ItemText = string.Empty;
    }
}
