// SPDX-FileCopyrightText: 2022 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.ContextMenu.UI;
using Content.Shared.Verbs;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.Utility;
using Robust.Shared.GameObjects;
using Robust.Shared.IoC;
using Robust.Shared.Maths;
using Robust.Shared.Utility;

namespace Content.Client.Verbs.UI
{
    /// <summary>
    ///     Slight extension of <see cref="ContextMenuElement"/> that uses a SpriteSpecifier for it's icon and provides
    ///     constructors that take verbs or verb categories.
    /// </summary>
    public sealed partial class VerbMenuElement : ContextMenuElement
    {
        public const string StyleClassVerbMenuConfirmationTexture = "verbMenuConfirmationTexture";

        // Setters to provide access to children generated by XAML.
        public bool IconVisible { set => Icon.Visible = value; }
        public bool TextVisible { set => Label.Visible = value; }

        // Top quality variable naming
        public readonly Verb? Verb;

        public VerbMenuElement(Verb verb) : base(verb.Text)
        {
            TooltipSupplier = sender =>
            {
                var label = new RichTextLabel();
                label.SetMessage(FormattedMessage.FromMarkupOrThrow(verb.Message ?? verb.Text));

                var tooltip = new Tooltip();
                tooltip.GetChild(0).Children.Clear();
                tooltip.GetChild(0).Children.Add(label);

                return tooltip;
            };
            Disabled = verb.Disabled;
            Verb = verb;

            Label.SetOnlyStyleClass(verb.TextStyleClass);

            // There are no confirmations in debug fam.
#if !DEBUG
            if (verb.ConfirmationPopup)
            {
                ExpansionIndicator.SetOnlyStyleClass(StyleClassVerbMenuConfirmationTexture);
                ExpansionIndicator.Visible = true;
            }
#endif

            var entManager = IoCManager.Resolve<IEntityManager>();

            if (verb.Icon == null && verb.IconEntity != null)
            {
                var spriteView = new SpriteView()
                {
                    OverrideDirection = Direction.South,
                    SetSize = new Vector2(ElementHeight, ElementHeight),
                };
                spriteView.SetEntity(entManager.GetEntity(verb.IconEntity.Value));

                Icon.AddChild(spriteView);
                return;
            }

            Icon.AddChild(new TextureRect()
            {
                Texture = verb.Icon != null ? entManager.System<SpriteSystem>().Frame0(verb.Icon) : null,
                Stretch = TextureRect.StretchMode.KeepAspectCentered
            });
        }

        public VerbMenuElement(VerbCategory category, string styleClass) : base(category.Text)
        {
            Label.SetOnlyStyleClass(styleClass);

            Icon.AddChild(new TextureRect()
            {
                Texture = category.Icon != null ? IoCManager.Resolve<IEntitySystemManager>().GetEntitySystem<SpriteSystem>().Frame0(category.Icon) : null,
                Stretch = TextureRect.StretchMode.KeepAspectCentered
            });
        }
    }
}