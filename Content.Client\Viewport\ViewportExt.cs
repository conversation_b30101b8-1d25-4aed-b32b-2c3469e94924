// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Robust.Client.UserInterface.CustomControls;

namespace Content.Client.Viewport
{
    public static class ViewportExt
    {
        public static int GetRenderScale(this IViewportControl viewport)
        {
            if (viewport is ScalingViewport svp)
                return svp.CurrentRenderScale;

            return 1;
        }
    }
}