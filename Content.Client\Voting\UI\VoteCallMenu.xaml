<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 <PERSON>-<PERSON>s <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<ui:VoteCallMenu xmlns="https://spacestation14.io"
                 xmlns:ui="clr-namespace:Content.Client.Voting.UI"
                 xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                 MouseFilter="Stop" MinSize="350 200">
    <PanelContainer StyleClasses="AngleRect" />
    <BoxContainer Orientation="Vertical">
        <BoxContainer Margin="8 0" Orientation="Horizontal">
            <Label Text="{Loc 'ui-vote-create-title'}"
                   HorizontalExpand="True" VAlign="Center" StyleClasses="LabelHeading" />
            <TextureButton Name="CloseButton" StyleClasses="windowCloseButton"
                           VerticalAlignment="Center" />
        </BoxContainer>
        <controls:HighDivider />

        <BoxContainer Orientation="Vertical" Margin="8 2 8 0" VerticalExpand="True" VerticalAlignment="Top">
            <BoxContainer Orientation="Vertical">
                <OptionButton Margin="2 1" Name="VoteTypeButton" HorizontalExpand="False" />
                <BoxContainer Name="VoteOptionsButtonContainer" HorizontalExpand="False" Orientation="Vertical"> 
                </BoxContainer>
                <Button Margin="64 4" Name="FollowButton" Text="{Loc 'ui-vote-follow-button'}" Visible="False" />
                <Label Margin="2 2" Name="VoteNotTrustedLabel" Text="{Loc 'ui-vote-trusted-users-notice'}" Visible="False" />
                <Label Margin="2 2" Name="VoteWarningLabel" Text="{Loc 'ui-vote-abuse-warning'}" Visible="False" HorizontalAlignment="Center"/>
            </BoxContainer>
            <Label Margin="8 2" Name="VoteTypeTimeoutLabel" Visible="False" />
        </BoxContainer>
        
        <Button Margin="8 32 8 2" Name="CreateButton" Text="{Loc 'ui-vote-create-button'}" />

        <PanelContainer StyleClasses="LowDivider" />
        <Label Margin="12 0 0 0" StyleClasses="LabelSubText" Text="{Loc 'ui-vote-fluff'}" />
    </BoxContainer>
</ui:VoteCallMenu>
