<!--
SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2021 Leo <l<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Control xmlns="https://spacestation14.io" MinWidth="300" MaxWidth="500">
    <PanelContainer StyleClasses="AngleRect" />
    <BoxContainer Margin="4" Orientation="Vertical">
        <Label Name="VoteCaller" />
        <RichTextLabel Name="VoteTitle" />
        <Button Margin="4 4" Name="FollowVoteTarget" Text="{Loc 'ui-vote-follow-button-popup'}" Visible="False"></Button>
        
        <GridContainer Columns="3" Name="VoteOptionsContainer"/>
        <BoxContainer Orientation="Horizontal">
            <ProgressBar Margin="4" HorizontalExpand="True" Name="TimeLeftBar" MinValue="0" MaxValue="1" />
            <Label Name="TimeLeftText" />
        </BoxContainer>
    </BoxContainer>
</Control>
