// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.Stylesheets;
using Content.Shared.Ghost;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;
using Robust.Shared.Utility;

namespace Content.Client.Voting.UI
{
    [GenerateTypedNameReferences]
    public sealed partial class VotePopup : Control
    {
        [Dependency] private readonly IGameTiming _gameTiming = default!;
        [Dependency] private readonly IVoteManager _voteManager = default!;
        [Dependency] private readonly IEntityNetworkManager _net = default!;

        private readonly VoteManager.ActiveVote _vote;
        private readonly Button[] _voteButtons;
        private readonly NetEntity? _targetEntity;

        public VotePopup(VoteManager.ActiveVote vote)
        {
            _vote = vote;
            IoCManager.InjectDependencies(this);
            RobustXamlLoader.Load(this);

            Stylesheet = IoCManager.Resolve<IStylesheetManager>().SheetSpace;

            if (_vote.TargetEntity != null && _vote.TargetEntity != 0)
            {
                _targetEntity = new NetEntity(_vote.TargetEntity.Value);
                FollowVoteTarget.Visible = true;
                FollowVoteTarget.OnPressed += _ => AttemptFollowVoteEntity();
            }

            Modulate = Color.White.WithAlpha(0.75f);
            _voteButtons = new Button[vote.Entries.Length];
            var group = new ButtonGroup();

            for (var i = 0; i < _voteButtons.Length; i++)
            {
                var button = new Button
                {
                    ToggleMode = true,
                    Group = group
                };
                _voteButtons[i] = button;
                VoteOptionsContainer.AddChild(button);
                var i1 = i;
                button.OnPressed += _ => _voteManager.SendCastVote(vote.Id, i1);
            }
        }

        public void UpdateData()
        {
            VoteTitle.SetMessage(FormattedMessage.FromUnformatted(_vote.Title));
            VoteCaller.Text = Loc.GetString("ui-vote-created", ("initiator", _vote.Initiator));

            for (var i = 0; i < _voteButtons.Length; i++)
            {
                var entry = _vote.Entries[i];
                if (_vote.DisplayVotes)
                {
                    _voteButtons[i].Text = Loc.GetString("ui-vote-button", ("text", entry.Text), ("votes", entry.Votes));
                }
                else
                {
                    _voteButtons[i].Text = Loc.GetString("ui-vote-button-no-votes", ("text", entry.Text));
                }

                if (_vote.OurVote == i)
                    _voteButtons[i].Pressed = true;
            }
        }

        private void AttemptFollowVoteEntity()
        {
            if (_targetEntity != null)
            {
                var msg = new GhostWarpToTargetRequestEvent(_targetEntity.Value);
                _net.SendSystemNetworkMessage(msg);
            }
        }

        protected override void FrameUpdate(FrameEventArgs args)
        {
            // Logger.Debug($"{_gameTiming.ServerTime}, {_vote.StartTime}, {_vote.EndTime}");

            var curTime = _gameTiming.RealTime;
            var timeLeft = _vote.EndTime - curTime;
            if (timeLeft < TimeSpan.Zero)
                timeLeft = TimeSpan.Zero;

            // Round up a second.
            timeLeft = TimeSpan.FromSeconds(Math.Ceiling(timeLeft.TotalSeconds));

            TimeLeftBar.Value = Math.Min(1, (float) ((curTime.TotalSeconds - _vote.StartTime.TotalSeconds) /
                                                     (_vote.EndTime.TotalSeconds - _vote.StartTime.TotalSeconds)));

            TimeLeftText.Text = $"{timeLeft:m\\:ss}";
        }
    }
}