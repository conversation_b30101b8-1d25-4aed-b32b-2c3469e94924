// SPDX-FileCopyrightText: 2022 ElectroJr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Kara <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2022 T-Stalker <<EMAIL>>
// SPDX-FileCopyrightText: 2022 T-Stalker <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PoorMansDreams <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: MIT

using Content.Client.Weapons.Ranged.Systems;

namespace Content.Client.Weapons.Ranged.Components;

[RegisterComponent, Access(typeof(GunSystem))]
public sealed partial class SpentAmmoVisualsComponent : Component
{
    /// <summary>
    /// Should we do "{_state}-spent" or just "spent"
    /// </summary>
    [DataField("suffix")] public bool Suffix = true;

    [DataField("state")]
    public string State = "base";
}

public enum AmmoVisualLayers : byte
{
    Base,
    Tip,
}