// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 ImHoks <<EMAIL>>
// SPDX-FileCopyrightText: 2025 KillanGenifer <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.UserInterface;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;
using Content.Shared._CorvaxNext.Silicons.Borgs.Components;

namespace Content.Client._CorvaxNext.Silicons.Laws.Ui;

[GenerateTypedNameReferences]
public sealed partial class RemoteDeviceDisplay : Control
{
    public event Action<RemoteDeviceActionEvent>? OnRemoteDeviceAction;

    public RemoteDeviceDisplay(NetEntity netEntityUid, string displayName)
    {
        RobustXamlLoader.Load(this);

        DeviceName.SetMessage(displayName);

        MoveButton.OnPressed += _ =>
        {
            OnRemoteDeviceAction?.Invoke(new RemoteDeviceActionEvent(
                RemoteDeviceActionEvent.RemoteDeviceActionType.MoveToDevice,
                netEntityUid));
        };

        TakeControlButton.OnPressed += _ =>
        {
            OnRemoteDeviceAction?.Invoke(new RemoteDeviceActionEvent(
                RemoteDeviceActionEvent.RemoteDeviceActionType.TakeControl,
                netEntityUid));
        };
    }
}
