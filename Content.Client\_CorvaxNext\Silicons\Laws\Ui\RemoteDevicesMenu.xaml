<controls:FancyWindow xmlns="https://spacestation14.io"
                     xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
                     xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                     Title="{Loc ai-remote-ui-menu-title}"
                     MinSize="355 100"
                     SetSize="355 415">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True"
                  VerticalExpand="True">
        <PanelContainer VerticalExpand="True" Margin="10 10 10 10">
            <PanelContainer.PanelOverride>
                <gfx:StyleBoxFlat BackgroundColor="#1B1B1E"/>
            </PanelContainer.PanelOverride>
            <ScrollContainer
                HScrollEnabled="False"
                HorizontalExpand="True"
                VerticalExpand="True">
                <BoxContainer
                    Name="RemoteDevicesDisplayContainer"
                    Orientation="Vertical"
                    VerticalExpand="True"
                    Margin="10 10 10 0">
                </BoxContainer>
            </ScrollContainer>
        </PanelContainer>
    </BoxContainer>
</controls:FancyWindow>
