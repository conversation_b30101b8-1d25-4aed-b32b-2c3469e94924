<!--
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<cartridges:MailMetricUiFragment
    xmlns:cartridges="clr-namespace:Content.Client._DV.CartridgeLoader.Cartridges"
    xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls"
    xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
    xmlns="https://spacestation14.io"
    Margin="5"
    VerticalExpand="True"
    Orientation="Vertical">
    <PanelContainer StyleClasses="BackgroundDark"></PanelContainer>
    <controls:StripeBack Name="MailMetricHeaderContainer">
        <PanelContainer>
            <Label Name="MailMetricHeader"
                Align="Center"
                Text="{Loc 'mail-metrics-header'}" />
        </PanelContainer>
    </controls:StripeBack>
    <BoxContainer
        Orientation="Vertical"
        HorizontalExpand="True"
        Margin="20 0">
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="TitleEmpty1"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True" />
            <Label Name="MailCountLabel"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                ClipText="True"
                StyleClasses="monospace"
                Align="Center"
                Text="{Loc 'mail-metrics-count-header'}" />
            <Label Name="SpesosLabel"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                ClipText="True"
                Align="Center"
                StyleClasses="monospace"
                Text="{Loc 'mail-metrics-money-header'}" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="OpenedLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-opened'}" />
            <Label Name="OpenedMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
            <Label Name="OpenedMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="ExpiredLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-expired'}" />
            <Label Name="ExpiredMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
            <Label Name="ExpiredMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="TamperedLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-tampered'}" />
            <Label Name="TamperedMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
            <Label Name="TamperedMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="DamagedLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-damaged'}" />
            <Label Name="DamagedMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
            <Label Name="DamagedMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                StyleClasses="monospace"
                Align="Right"
                ClipText="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="UnopenedLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-unopened'}" />
            <Label Name="UnopenedMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                StyleClasses="monospace"
                Align="Right"
                ClipText="True" />
            <Label Name="UnopenedMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="2">
            <Label Name="TotalMailLabel"
                SizeFlagsStretchRatio="2"
                HorizontalExpand="True"
                ClipText="True"
                Text="{Loc 'mail-metrics-total'}" />
            <Label Name="TotalMailCount"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
            <Label Name="TotalMailSpesos"
                SizeFlagsStretchRatio="1"
                HorizontalExpand="True"
                Align="Right"
                StyleClasses="monospace"
                ClipText="True" />
        </BoxContainer>
    </BoxContainer>
    <BoxContainer
        Orientation="Vertical"
        HorizontalExpand="True"
        Margin="10">
        <Label
            Name="SuccessRateCounts"
            Align="Center"
            StyleClasses="LabelBig" />
        <Label
            Name="SuccessRatePercent"
            Align="Center"
            StyleClasses="LabelBig" />
    </BoxContainer>
</cartridges:MailMetricUiFragment>
