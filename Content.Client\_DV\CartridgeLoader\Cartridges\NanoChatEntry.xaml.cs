// SPDX-FileCopyrightText: 2024 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared._DV.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._DV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NanoChatEntry : BoxContainer
{
    public event Action<uint>? OnPressed;
    private uint _number;
    private Action<EventArgs>? _pressHandler;

    public NanoChatEntry()
    {
        RobustXamlLoader.Load(this);
    }

    public void SetRecipient(NanoChatRecipient recipient, uint number, bool isSelected)
    {
        // Remove old handler if it exists
        if (_pressHandler != null)
            ChatButton.OnPressed -= _pressHandler;

        _number = number;

        // Create and store new handler
        _pressHandler = _ => OnPressed?.Invoke(_number);
        ChatButton.OnPressed += _pressHandler;

        NameLabel.Text = recipient.Name;
        JobLabel.Text = recipient.JobTitle ?? "";
        JobLabel.Visible = !string.IsNullOrEmpty(recipient.JobTitle);
        UnreadIndicator.Visible = recipient.HasUnread;

        ChatButton.ModulateSelfOverride = isSelected ? NanoChatMessageBubble.OwnMessageColor : null;
    }
}