// SPDX-FileCopyrightText: 2024 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._DV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NanoChatLogEntry : BoxContainer
{
    public NanoChatLogEntry(int number, string time, string message)
    {
        RobustXamlLoader.Load(this);
        NumberLabel.Text = number.ToString();
        TimeLabel.Text = time;
        MessageLabel.Text = message;
    }
}