// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Shared._DV.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._DV.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class NanoChatLookupView : PanelContainer
{
    public NanoChatLookupView()
    {
        RobustXamlLoader.Load(this);
    }

    public event Action<NanoChatRecipient>? OnStartChat;

    public void UpdateContactList(NanoChatUiState state)
    {
        ContactsList.RemoveAllChildren();
        if (state.Contacts is not { } contacts)
        {
            ContactsList.AddChild(new Label() { Text = Loc.GetString("nano-chat-look-up-no-server") });
            return;
        }

        for (var idx = 0; idx < contacts.Count; idx++)
        {
            var contact = contacts[idx];
            var nameLabel = new Label()
            {
                Text = contact.Name,
                HorizontalAlignment = HAlignment.Left,
                HorizontalExpand = true
            };
            var numberLabel = new Label()
            {
                Text = $"#{contacts[idx].Number:D4}",
                HorizontalAlignment = HAlignment.Right,
                Margin = new Thickness(0, 0, 36, 0),
            };
            var startChatButton = new Button()
            {
                Text = "+",
                HorizontalAlignment = HAlignment.Right,
                MinSize = new Vector2(32, 32),
                MaxSize = new Vector2(32, 32),
                ToolTip = Loc.GetString("nano-chat-new-chat"),
            };
            startChatButton.AddStyleClass("OpenBoth");
            if (contact.Number == state.OwnNumber || state.Recipients.ContainsKey(contact.Number) || state.MaxRecipients <= state.Recipients.Count)
            {
                startChatButton.Disabled = true;
            }
            startChatButton.OnPressed += _ => OnStartChat?.Invoke(contact);

            var panel = new PanelContainer()
            {
                HorizontalExpand = true,
            };

            panel.AddChild(nameLabel);
            panel.AddChild(numberLabel);
            panel.AddChild(startChatButton);

            var styleClass = idx % 2 == 0 ? "PanelBackgroundBaseDark" : "PanelBackgroundLight";
            panel.StyleClasses.Add(styleClass);

            ContactsList.AddChild(panel);
        }
    }
}