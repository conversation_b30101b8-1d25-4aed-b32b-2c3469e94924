// SPDX-FileCopyrightText: 2025 AftrLite <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._DV.CosmicCult.UI;

[GenerateTypedNameReferences]
public sealed partial class CosmicDeconvertedMenu : FancyWindow
{
    public CosmicDeconvertedMenu()
    {
        RobustXamlLoader.Load(this);

        ConfirmButton.OnPressed += _ => Close();
    }
}
