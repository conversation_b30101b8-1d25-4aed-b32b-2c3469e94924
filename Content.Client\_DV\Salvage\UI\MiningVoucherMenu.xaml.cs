// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 <PERSON>krz <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aineias1 <dmit<PERSON>.<PERSON>.<EMAIL>>
// SPDX-FileCopyrightText: 2025 FaDeOkno <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Mc<PERSON><PERSON>erson <148172569+<PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Rouden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TheBorzoiMustConsume <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Unlumination <<EMAIL>>
// SPDX-FileCopyrightText: 2025 coderabbitai[bot] <136622811+coderabbitai[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 username <<EMAIL>>
// SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared._DV.Salvage.Components;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using System.Numerics;

namespace Content.Client._DV.Salvage.UI;

[GenerateTypedNameReferences]
public sealed partial class MiningVoucherMenu : RadialMenu
{
    [Dependency] private readonly IEntityManager _entMan = default!;
    [Dependency] private readonly IPrototypeManager _proto = default!;

    private readonly SpriteSystem _sprite;

    public event Action<int>? OnSelected;

    public MiningVoucherMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _sprite = _entMan.System<SpriteSystem>();
    }

    public void SetEntity(EntityUid owner)
    {
        if (!_entMan.TryGetComponent<MiningVendorComponent>(owner, out var comp))
            return;

        for (int i = 0; i < comp.Kits.Count; i++)
        {
            var index = i; // copy so the closure doesn't borrow it
            var kit = _proto.Index(comp.Kits[i]);
            var button = new RadialMenuTextureButtonWithSector()
            {
                StyleClasses = { "RadialMenuTextureButtonWithSector" },
                SetSize = new Vector2(64f, 64f),
                ToolTip = Loc.GetString(kit.Description)
            };
            button.AddChild(new TextureRect()
            {
                VerticalAlignment = VAlignment.Center,
                HorizontalAlignment = HAlignment.Center,
                Texture = _sprite.Frame0(kit.Sprite),
                TextureScale = new Vector2(2f, 2f)
            });

            button.OnPressed += _ => OnSelected?.Invoke(index);

            Main.AddChild(button);
        }
    }
}