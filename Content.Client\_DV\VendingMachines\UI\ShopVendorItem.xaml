<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Rouden <149893554+<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<BoxContainer xmlns="https://spacestation14.io"
            Orientation="Horizontal"
            HorizontalExpand="True"
            SeparationOverride="4">
    <EntityPrototypeView
        Name="ItemPrototype"
        Margin="4 0 0 0"
        HorizontalAlignment="Center"
        VerticalAlignment="Center"
        MinSize="32 32"/>
    <Label Name="NameLabel" SizeFlagsStretchRatio="3" HorizontalExpand="True" ClipText="True"/>
    <Label Name="CostLabel" SizeFlagsStretchRatio="3" HorizontalAlignment="Right" Margin="8 0"/>
</BoxContainer>
