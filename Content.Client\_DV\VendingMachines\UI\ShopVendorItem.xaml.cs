// SPDX-FileCopyrightText: 2024 Winkarst <74284083+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON>k<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aidenkrz <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aineias1 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 FaDeOkno <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Mc<PERSON><PERSON>erson <148172569+Mc<PERSON><PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2025 Milon <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Rouden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TheBorzoiMustConsume <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Unlumination <<EMAIL>>
// SPDX-FileCopyrightText: 2025 coderabbitai[bot] <136622811+coderabbitai[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 username <<EMAIL>>
// SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client._DV.VendingMachines.UI;

[GenerateTypedNameReferences]
public sealed partial class ShopVendorItem : BoxContainer
{
    public ShopVendorItem(EntProtoId entProto, string text, uint cost)
    {
        RobustXamlLoader.Load(this);

        ItemPrototype.SetPrototype(entProto);

        NameLabel.Text = text;

        CostLabel.Text = cost.ToString();
    }
}