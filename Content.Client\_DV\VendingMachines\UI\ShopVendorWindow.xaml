<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Rouden <149893554+<PERSON><PERSON><EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        MinHeight="210">
    <BoxContainer Name="MainContainer" Orientation="Vertical">
    	<BoxContainer Orientation="Horizontal">
    	    <LineEdit Name="SearchBar" PlaceHolder="{Loc 'vending-machine-component-search-filter'}" HorizontalExpand="True" Margin="4 4"/>
	        <Label Name="BalanceLabel" Margin="4 4"/>
        </BoxContainer>
        <controls:SearchListContainer Name="VendingContents" VerticalExpand="True" Margin="4 4"/>
         <!-- Footer -->
        <BoxContainer Orientation="Vertical">
            <PanelContainer StyleClasses="LowDivider" />
            <BoxContainer Orientation="Horizontal" Margin="10 2 5 0" VerticalAlignment="Bottom">
                <Label Text="{Loc 'shop-vendor-flavor-left'}" StyleClasses="WindowFooterText" />
                <Label Text="{Loc 'shop-vendor-flavor-right'}" StyleClasses="WindowFooterText"
                     HorizontalAlignment="Right" HorizontalExpand="True"  Margin="0 0 5 0" />
                <TextureRect StyleClasses="NTLogoDark" Stretch="KeepAspectCentered"
                     VerticalAlignment="Center" HorizontalAlignment="Right" SetSize="19 19"/>
            </BoxContainer>
        </BoxContainer>
    </BoxContainer>
</controls:FancyWindow>
