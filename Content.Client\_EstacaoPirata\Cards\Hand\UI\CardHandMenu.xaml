<!--
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 RadsammyT <<EMAIL>>
SPDX-FileCopyrightText: 2024 coderabbitai[bot] <136622811+coderabbitai[bot]@users.noreply.github.com>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<ui:RadialMenu xmlns="https://spacestation14.io"
                xmlns:ui="clr-namespace:Content.Client.UserInterface.Controls"
                xmlns:cardhand="Content.Client._EstacaoPirata.Cards.Hand.UI"
                BackButtonStyleClass="RadialMenuBackButton"
                CloseButtonStyleClass="RadialMenuCloseButton"
                VerticalExpand="True"
                HorizontalExpand="True"
                MinSize="450 450">


    <ui:RadialContainer Name="Main" VerticalExpand="True" HorizontalExpand="True" InitialRadius="100" ReserveSpaceForHiddenChildren="False"
                        InnerRadiusMultiplier="0.8" OuterRadiusMultiplier="1.2" ></ui:RadialContainer>
</ui:RadialMenu>
