<!--
SPDX-FileCopyrightText: 2025 Aiden <********+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:PatronPerksWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client._RMC14.LinkAccount"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    Title="{Loc 'rmc-ui-patron-perks'}"
    MinSize="625 475">
    <TabContainer Name="Tabs" Access="Public">
        <BoxContainer Name="LobbyMessageTab" Access="Public" Orientation="Vertical" Margin="5">
            <Label Text="{Loc 'rmc-ui-lobby-message-description'}" />
            <LineEdit Name="LobbyMessage" Access="Public" Margin="0 10 0 0" />
        </BoxContainer>
        <BoxContainer Name="ShoutoutTab" Access="Public" Orientation="Vertical" Visible="False"
                      Margin="5">
            <BoxContainer Access="Public" Orientation="Vertical">
                <Label Text="{Loc 'rmc-ui-shoutout-nt-title'}" />
                <Label Text="{Loc 'rmc-ui-shoutout-nt-description'}" />
                <RichTextLabel Name="NTShoutoutExample" Access="Public" />
                <LineEdit Name="NTShoutout" Access="Public" Margin="0 10 0 0" />
            </BoxContainer>
            <cc:HSeparator Margin="0 15" />
            <Label Text="{Loc 'rmc-ui-shoutout-info'}" />
        </BoxContainer>
        <BoxContainer Name="GhostColorTab" Access="Public" Orientation="Vertical" Margin="5">
            <Label Text="{Loc 'rmc-ui-ghost-color'}" />
            <ColorSelectorSliders Name="GhostColorSliders" Access="Public" Margin="0 10 0 0" />
            <Button Name="GhostColorClearButton" Access="Public" Text="{Loc 'rmc-ui-ghost-color-clear'}"
                    StyleClasses="OpenBoth" />
            <Button Name="GhostColorSaveButton" Access="Public" Text="{Loc 'rmc-ui-ghost-color-save'}"
                    StyleClasses="OpenBoth" Disabled="True" />
        </BoxContainer>
    </TabContainer>
</controls:PatronPerksWindow>
