// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Goobstation.UIKit.UserActions.Controls;
using Content.Client.UserInterface.Systems.Admin;
using Content.Client.UserInterface.Systems.Bwoink;
using Content.Client.UserInterface.Systems.Crafting;
using Content.Client.UserInterface.Systems.EscapeMenu;
using Content.Client.UserInterface.Systems.Sandbox;
using Content.Shared.Chat;
using Content.Shared.Chat.Prototypes;
using Content.Shared.Speech;
using Content.Shared.Whitelist;
using Robust.Client.AutoGenerated;
using Robust.Client.Console;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Client.Utility;
using Robust.Shared.Player;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;

namespace Content.Client._Shitcode.UserActions.Tabs;

[GenerateTypedNameReferences]
public sealed partial class ConfigTabControl : BaseTabControl
{
    [Dependency] private readonly EntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly ISharedPlayerManager _playerManager = default!;
    [Dependency] private readonly IGameTiming _gameTiming = default!;
    [Dependency] private readonly IClientConsoleHost _console = default!;

    public ConfigTabControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }

    public override bool UpdateState()
    {
        var optionsUIController = UserInterfaceManager.GetUIController<OptionsUIController>();
        var adminUIController = UserInterfaceManager.GetUIController<AdminUIController>();
        var ahelpUIController = UserInterfaceManager.GetUIController<AHelpUIController>();
        var craftingUIController = UserInterfaceManager.GetUIController<CraftingUIController>();
        var sandboxUIController = UserInterfaceManager.GetUIController<SandboxUIController>();

        MenuList.RemoveAllChildren();

        var disconnectButton = CreateMenuButton(
                "DisconnectButton",
                Loc.GetString("ui-config-menu-disconnect"),
                "/Textures/Interface/VerbIcons/open.svg.192dpi.png");
        disconnectButton.OnPressed += (_) =>
            _console.ExecuteCommand("disconnect");
        MenuList.AddChild(disconnectButton);

        var quitButton = CreateMenuButton(
                "QuitButton",
                Loc.GetString("ui-config-menu-quit"),
                "/Textures/Interface/VerbIcons/Spare/poweronoff.svg.192dpi.png");
        quitButton.OnPressed += (_) =>
            _console.ExecuteCommand("quit");
        MenuList.AddChild(quitButton);

        var optionsButton = CreateMenuButton(
                "OptionsButton",
                Loc.GetString("ui-config-menu-options"),
                "/Textures/Interface/VerbIcons/settings.svg.192dpi.png");
        optionsButton.OnPressed += (_) =>
            optionsUIController.OpenWindow();
        MenuList.AddChild(optionsButton);

        var craftingButton = CreateMenuButton(
                "CraftingButton",
                Loc.GetString("ui-config-menu-crafting"),
                "/Textures/Interface/hammer.svg.192dpi.png");
        craftingButton.OnPressed += (_) =>
            craftingUIController.Toggle();
        MenuList.AddChild(craftingButton);

        var adminButton = CreateMenuButton(
                "AdminButton",
                Loc.GetString("ui-config-menu-admin"),
                "/Textures/Interface/gavel.svg.192dpi.png");
        adminButton.OnPressed += (_) =>
            adminUIController.Toggle();
        MenuList.AddChild(adminButton);

        var sandboxButton = CreateMenuButton(
                "SandboxButton",
                Loc.GetString("ui-config-menu-sandbox"),
                "/Textures/Interface/sandbox.svg.192dpi.png");
        sandboxButton.OnPressed += (_) =>
            sandboxUIController.ToggleWindow();
        MenuList.AddChild(sandboxButton);

        var ahelpButton = CreateMenuButton(
                "AHelpButton",
                Loc.GetString("ui-config-menu-ahelp"),
                "/Textures/Interface/info.svg.192dpi.png");
        ahelpButton.OnPressed += (_) =>
            ahelpUIController.ToggleWindow();
        MenuList.AddChild(ahelpButton);

        UpdateButtonsLayout();
        return true;
    }

    private void UpdateButtonsLayout()
    {
    }

    private IconButton CreateMenuButton(string controlName, string buttonName, string? texturePath = null)
    {
        var button = new IconButton(buttonName);
        button.Name = controlName;
        if (texturePath is not null)
            button.Icon.TexturePath = texturePath;

        return button;
    }

    protected override void Resized()
    {
        UpdateButtonsLayout();
    }
}
