// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Goobstation.UIKit.UserActions.Controls;
using Content.Shared.Chat;
using Content.Shared.Chat.Prototypes;
using Content.Shared.Speech;
using Content.Shared.Whitelist;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Client.Utility;
using Robust.Shared.Player;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;

namespace Content.Client._Shitcode.UserActions.Tabs;

[GenerateTypedNameReferences]
public sealed partial class EmotesTabControl : BaseTabControl
{
    [Dependency] private readonly EntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly ISharedPlayerManager _playerManager = default!;
    [Dependency] private readonly IGameTiming _gameTiming = default!;

    private TimeSpan _lastEmoteTime;
    private static readonly TimeSpan EmoteCooldown = TimeSpan.FromSeconds(0);

    public EmotesTabControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }

    public override bool UpdateState()
    {
        EmotesList.RemoveAllChildren();

        var player = _playerManager.LocalEntity;
        if (player is not { Valid: true })
            return false;

        var emotes = _prototypeManager.EnumeratePrototypes<EmotePrototype>()
            .Where(emote => IsEmoteAvailable(emote, player.Value))
            .OrderBy(x => x.Category)
            .ThenBy(x => x.ID)
            .ToList();

        if (emotes.Count == 0)
            return false;

        foreach (var emote in emotes)
        {
            var button = CreateEmoteButton(emote);
            EmotesList.AddChild(button);
        }

        return true;
    }

    private BoxContainer CreateNewRow()
    {
        return new BoxContainer
        {
            Orientation = BoxContainer.LayoutOrientation.Horizontal,
            HorizontalExpand = true,
        };
    }

    private IconButton CreateEmoteButton(EmotePrototype emote)
    {
        var button = new IconButton(Loc.GetString(emote.Name));
        button.Icon.Texture = emote.Icon.Frame0();
        button.OnPressed += _ => OnPlayEmote(new ProtoId<EmotePrototype>(emote.ID));

        return button;
    }

    private bool IsEmoteAvailable(EmotePrototype emote, EntityUid player)
    {
        var whitelistSystem = _entManager.System<EntityWhitelistSystem>();

        if (emote.Category == EmoteCategory.Invalid || emote.ChatTriggers.Count == 0)
            return false;

        if (!whitelistSystem.IsWhitelistPassOrNull(emote.Whitelist, player) ||
            whitelistSystem.IsBlacklistPass(emote.Blacklist, player))
            return false;

        if (!emote.Available &&
            _entManager.TryGetComponent<SpeechComponent>(player, out var speech) &&
            !speech.AllowedEmotes.Contains(emote.ID))
            return false;

        return true;
    }

    private void OnPlayEmote(ProtoId<EmotePrototype> protoId)
    {
        var currentTime = _gameTiming.CurTime;
        if (currentTime - _lastEmoteTime < EmoteCooldown)
            return;

        _lastEmoteTime = currentTime;
        _entManager.RaisePredictiveEvent(new PlayEmoteMessage(protoId));
    }

    protected override void Resized()
    {
    }
}
