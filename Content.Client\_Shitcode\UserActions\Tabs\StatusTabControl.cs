// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Client.GameTicking.Managers;
using Content.Shared.Chat;
using Content.Shared.Chat.Prototypes;
using Content.Shared.Speech;
using Content.Shared.Whitelist;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Player;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;

namespace Content.Client._Shitcode.UserActions.Tabs;

[GenerateTypedNameReferences]
public sealed partial class StatusTabControl : BaseTabControl
{
    [Dependency] private readonly IEntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly ISharedPlayerManager _playerManager = default!;
    [Dependency] private readonly IGameTiming _gameTiming = default!;

    private ClientGameTicker? _gameTicker;

    public StatusTabControl()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }

    protected override void FrameUpdate(FrameEventArgs e)
    {
        if (_gameTicker != null)
        {
            var roundTime = _gameTiming.CurTime.Subtract(_gameTicker.RoundStartTimeSpan);
            StationTime.Text = Loc.GetString("lobby-state-player-status-round-time", ("hours", roundTime.Hours), ("minutes", roundTime.Minutes));
            return;
        }

        StationTime.Text = Loc.GetString("lobby-state-player-status-round-not-started");
    }

    public override bool UpdateState()
    {
        if (_gameTicker == null)
        {
            _gameTicker = _entManager.System<ClientGameTicker>();
            _gameTicker.InGameInfoBlobUpdated += UpdateInfoBlob;
        }
        UpdateInfoBlob();
        return true;
    }

    private void UpdateInfoBlob()
    {
        if (_gameTicker != null && _gameTicker.InGameInfoBlob != null)
            ServerInfo.SetInfoBlob(_gameTicker.InGameInfoBlob);
    }

    protected override void Resized()
    {
    }
}
