<controls:StatusTabControl
    xmlns="https://spacestation14.io"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:maths="clr-namespace:Robust.Shared.Maths;assembly=Robust.Shared.Maths"
    xmlns:info="clr-namespace:Content.Client.Info"
    xmlns:controls="clr-namespace:Content.Client._Shitcode.UserActions.Tabs"
    xmlns:actions="clr-namespace:Content.Client.UserInterface.Systems.Actions.Widgets">

    <PanelContainer StyleClasses="BackgroundDark">
        <ScrollContainer VerticalExpand="True"
                         HorizontalExpand="True"
                         Margin="2"
                         MinHeight="100"
                         HScrollEnabled="False">
            <BoxContainer Name="InfoContainer"
                          Orientation="Vertical"
                          HorizontalExpand="True"
                          Margin="5">
                <info:ServerInfo Name="ServerInfo" Access="Public" MinSize="0 30" VerticalExpand="false"
                            Margin="0" MaxWidth="400" HorizontalAlignment="Left" />
                <Label Name="StationTime" Access="Public" FontColorOverride="{x:Static maths:Color.LightGray}"
                            Margin="0" HorizontalAlignment="Left" />
            </BoxContainer>
        </ScrollContainer>
    </PanelContainer>

</controls:StatusTabControl>
