// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitcode.UserActions;

[GenerateTypedNameReferences]
public sealed partial class UserActionsPanel : Control
{
    private UserActionUIController _controller;

    public UserActionsPanel()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _controller = UserInterfaceManager.GetUIController<UserActionUIController>();
        _controller.RegisterPanel(this);

        RegisterTabs();
    }

    private void RegisterTabs()
    {
        _controller.RegisterTab(StatusTabControl);
        TabContainer.SetTabTitle(StatusTabControl, Loc.GetString("user-action-control-tab-status"));

        _controller.RegisterTab(EmotesTabControl);
        TabContainer.SetTabTitle(EmotesTabControl, Loc.GetString("user-action-control-tab-emote"));

        // _controller.RegisterTab(ActionsTabControl);
        // TabContainer.SetTabTitle(ActionsTabControl, Loc.GetString("user-action-control-tab-actions"));

        _controller.RegisterTab(ConfigTabControl);
        TabContainer.SetTabTitle(ConfigTabControl, Loc.GetString("user-action-control-tab-config"));
    }

    public void UpdateTabs()
    {
        var anyVisible = false;

        foreach (var tab in _controller.GetTabs())
        {
            var visible = tab.UpdateState();
            TabContainer.SetTabVisible(tab, visible);

            if (visible)
                anyVisible = true;
        }

        MainPanel.Visible = true;
        NoContentLabel.Visible = !anyVisible;
    }
}
