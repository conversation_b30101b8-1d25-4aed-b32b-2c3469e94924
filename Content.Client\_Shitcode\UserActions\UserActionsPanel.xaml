<controls:UserActionsPanel
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client._Shitcode.UserActions"
    xmlns:tabs="clr-namespace:Content.Client._Shitcode.UserActions.Tabs">

    <PanelContainer StyleClasses="BackgroundDark" Name="MainPanel" MinWidth="300" MinHeight="190" VerticalExpand="True" HorizontalExpand="True">

        <BoxContainer Orientation="Vertical" Name="BoxContainer" Margin="4" VerticalExpand="True">

            <TabContainer Name="TabContainer" HorizontalExpand="True" VerticalExpand="True">
                <tabs:StatusTabControl Name="StatusTabControl"/>
                <tabs:EmotesTabControl Name="EmotesTabControl"/>
                <!-- <tabs:ActionsTabControl Name="ActionsTabControl"/> -->
                <tabs:ConfigTabControl Name="ConfigTabControl"/>
            </TabContainer>

            <Label Name="NoContentLabel"
                   Text="{Loc 'user-action-control-nothing'}"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   StyleClasses="LabelBig"
                   Margin="0 8"
                   Visible="False"/>

        </BoxContainer>

    </PanelContainer>

</controls:UserActionsPanel>
