<!--
SPDX-FileCopyrightText: 2021 20kdc <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Ray <<EMAIL>>
SPDX-FileCopyrightText: 2023 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
            Title="{Loc 'ghost-target-window-ghostbar'}"
            MinSize="500 300"
            SetSize="500 300">
    <BoxContainer Orientation="Vertical"
                  HorizontalExpand="True">
        <RichTextLabel Name="TopBanner" VerticalExpand="True"/>
        <Button Name="SpawnButton"
                Text="{Loc 'ghost-window-spawn-ghostbar-button'}"
                Disabled="True"
                TextAlign="Center"
                HorizontalAlignment="Center"
                VerticalAlignment="Center" />
    </BoxContainer>
</DefaultWindow>
