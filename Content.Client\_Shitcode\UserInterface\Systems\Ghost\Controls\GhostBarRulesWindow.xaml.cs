// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Rank #1 Jonestown partygoer <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.CCVar;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Configuration;
using Robust.Shared.Timing;
using Robust.Shared.Utility;

namespace Content.Client._Shitcode.UserInterface.Systems.Ghost.Controls
{
    [GenerateTypedNameReferences]
    public sealed partial class GhostBarRulesWindow : DefaultWindow
    {
        [Dependency] private readonly IConfigurationManager _cfg = IoCManager.Resolve<IConfigurationManager>();
        private float _timer;

        public event Action? SpawnButtonPressed;
        public GhostBarRulesWindow()
        {
            RobustXamlLoader.Load(this);
            var ghostBarTime = _cfg.GetCVar(CCVars.GhostRoleTime);
            _timer = ghostBarTime;

            if (ghostBarTime > 0f)
            {
                SpawnButton.Text = Loc.GetString("ghost-window-spawn-ghostbar-button-timer", ("time", $"{_timer:0.0}"));
                TopBanner.SetMessage(FormattedMessage.FromMarkupPermissive(Loc.GetString("ghost-bar-rules") + "\n" + Loc.GetString("ghost-roles-window-rules-footer", ("time", ghostBarTime))));
                SpawnButton.Disabled = true;
            }

            SpawnButton.OnPressed += _ => SpawnButtonPressed?.Invoke();
        }


        protected override void FrameUpdate(FrameEventArgs args)
        {
            base.FrameUpdate(args);
            if (!SpawnButton.Disabled) return;
            if (_timer > 0.0)
            {
                _timer -= args.DeltaSeconds;
                SpawnButton.Text = Loc.GetString("ghost-window-spawn-ghostbar-button-timer", ("time", $"{_timer:0.0}"));
            }
            else
            {
                SpawnButton.Disabled = false;
                SpawnButton.Text = Loc.GetString("ghost-window-spawn-ghostbar-button");
            }
        }
    }

}