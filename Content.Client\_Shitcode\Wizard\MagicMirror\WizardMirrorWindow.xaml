<!--
SPDX-FileCopyrightText: 2022 Flip<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2022 Rane <60792108+<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2022 Veritius <<EMAIL>>
SPDX-FileCopyrightText: 2022 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2023 Morb <<EMAIL>>
SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
SPDX-FileCopyrightText: 2024 K<PERSON><PERSON>horn <42424291+Krunk<PERSON><EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2024 PoTeletubby <<EMAIL>>
SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aviu00 <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:humanoid="clr-namespace:Content.Client.Humanoid"
         xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
         xmlns:ui="clr-namespace:Content.Client.Lobby.UI"
         HorizontalExpand="True"
         Title="{Loc 'magic-mirror-window-title'}"
         MinSize="800 800">
        <!-- Left side -->
        <BoxContainer Orientation="Vertical" Margin="10 10 10 10" HorizontalExpand="True">
            <!-- Middle container -->
            <BoxContainer Orientation="Horizontal" SeparationOverride="10" HorizontalExpand="True">
                <!-- Name box-->
                <BoxContainer Orientation="Vertical">
                    <ui:HighlightedContainer>
                        <BoxContainer Orientation="Vertical">
                            <ui:HighlightedContainer>
                                <BoxContainer Orientation="Vertical">
                                    <BoxContainer Orientation="Horizontal" VerticalExpand="True">
                                        <Label Text="{Loc 'humanoid-profile-editor-name-label'}" />
                                        <LineEdit Name="NameEdit" MinSize="270 0" VerticalAlignment="Center" Margin="5 0 0 0" />
                                        <Button Name="NameRandomize" Text="{Loc 'humanoid-profile-editor-name-random-button'}" />
                                    </BoxContainer>
                                    <Button Name="RandomizeEverythingButton" HorizontalAlignment="Center"
                                            HorizontalExpand="False" MaxWidth="256"
                                            Text="{Loc 'humanoid-profile-editor-randomize-everything-button'}" />
                                </BoxContainer>
                            </ui:HighlightedContainer>
                        </BoxContainer>
                    </ui:HighlightedContainer>
                </BoxContainer>
                <!-- Preview -->
                <BoxContainer Orientation="Vertical" VerticalExpand="True" VerticalAlignment="Center">
                    <SpriteView Name="SpriteView" Scale="8 8" Margin="4" SizeFlagsStretchRatio="1" />
                    <BoxContainer Orientation="Horizontal" HorizontalAlignment="Center" Margin="0 5">
                        <Button Name="SpriteRotateLeft" Text="◀" StyleClasses="OpenRight" />
                        <cc:VSeparator Margin="2 0 3 0" />
                        <Button Name="SpriteRotateRight" Text="▶" StyleClasses="OpenLeft" />
                    </BoxContainer>
                </BoxContainer>
                <!-- Save -->
                <BoxContainer Orientation="Vertical" MinSize="60 0" HorizontalExpand="True" HorizontalAlignment="Right">
                    <ui:HighlightedContainer Name="ProfileHighlight">
                        <BoxContainer Orientation="Vertical">
                            <Button Name="SaveButton" Text="{Loc 'humanoid-profile-editor-save-button'}"/>
                        </BoxContainer>
                    </ui:HighlightedContainer>
                </BoxContainer>
            </BoxContainer>
            <Control MinHeight="10" />
            <!-- tabContainer -->
            <TabContainer Name="TabContainer" VerticalExpand="True">
                <BoxContainer Orientation="Vertical">
                    <ScrollContainer VerticalExpand="True">
                        <!-- appearanceList -->
                        <BoxContainer Orientation="Vertical">
                            <BoxContainer Margin="10" Orientation="Vertical" HorizontalExpand="True">
                                <!-- Species -->
                                <BoxContainer HorizontalExpand="True">
                                    <Label Text="{Loc 'humanoid-profile-editor-species-label'}" />
                                    <Control HorizontalExpand="True"/>
                                    <OptionButton Name="SpeciesButton" HorizontalAlignment="Right" />
                                </BoxContainer>
                                <!-- Sex -->
                                <BoxContainer HorizontalExpand="True">
                                    <Label Text="{Loc 'humanoid-profile-editor-sex-label'}" />
                                    <Control HorizontalExpand="True"/>
                                    <OptionButton Name="SexButton" HorizontalAlignment="Right" />
                                </BoxContainer>
                                <!-- Pronouns -->
                                <BoxContainer HorizontalExpand="True">
                                    <Label Text="{Loc 'humanoid-profile-editor-pronouns-label'}" />
                                    <Control HorizontalExpand="True"/>
                                    <OptionButton Name="PronounsButton" HorizontalAlignment="Right" />
                                </BoxContainer>
                            </BoxContainer>
                            <!-- Skin -->
                            <BoxContainer Margin="10" HorizontalExpand="True" Orientation="Vertical">
                                <Label Text="{Loc 'humanoid-profile-editor-skin-color-label'}" />
                                <Slider HorizontalExpand="True" Name="Skin" MinValue="0" MaxValue="100" Value="20" />
                                <BoxContainer Name="RgbSkinColorContainer" Visible="False" Orientation="Vertical" HorizontalExpand="True"></BoxContainer>
                            </BoxContainer>
                            <!-- Hair -->
                            <BoxContainer Margin="10" Orientation="Horizontal">
                                <humanoid:SingleMarkingPicker Name="HairStylePicker" Category="Hair" />
                                <humanoid:SingleMarkingPicker Name="FacialHairPicker" Category="FacialHair" />
                            </BoxContainer>
                            <!-- Eyes -->
                            <BoxContainer Margin="10" Orientation="Vertical">
                                <Label Text="{Loc 'humanoid-profile-editor-eyes-label'}" />
                                <humanoid:EyeColorPicker Name="EyeColorPicker" />
                            </BoxContainer>
                        </BoxContainer>
                    </ScrollContainer>
                </BoxContainer>
                <BoxContainer Name="MarkingsTab" Orientation="Vertical" Margin="10">
                    <!-- Markings -->
                    <ScrollContainer VerticalExpand="True">
                        <humanoid:MarkingPicker Name="Markings" IgnoreCategories="Hair,FacialHair" />
                    </ScrollContainer>
                </BoxContainer>
            </TabContainer>
        </BoxContainer>
</DefaultWindow>
