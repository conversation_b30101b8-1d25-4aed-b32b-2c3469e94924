// SPDX-FileCopyrightText: 2022 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Illiux <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jezithyr <<EMAIL>>
// SPDX-FileCopyrightText: 2022 J<PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Crotalus <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using System.Numerics;
using Content.Shared._Goobstation.Wizard.Refund;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitcode.Wizard.Refund
{
    [GenerateTypedNameReferences]
    public sealed partial class StoreRefundWindow : DefaultWindow
    {
        private List<RefundListingData> _listings = new();
        private bool _refundDiabled;
        private string _searchText = string.Empty;

        public event Action<NetEntity>? ListingClicked;

        public event Action? RefundAllClicked;

        public StoreRefundWindow()
        {
            RobustXamlLoader.Load(this);
            SearchBar.OnTextChanged += OnSearchTextChanged;

            RefundAllButton.OnPressed += _ => RefundAllClicked?.Invoke();
        }

        public void UpdateListings(IEnumerable<RefundListingData> listings, bool refundDisabled)
        {
            _refundDiabled = refundDisabled;
            // Server COULD send these sorted but how about we just use the client to do it instead
            _listings = listings
                .OrderBy(w => w.DisplayName,
                    Comparer<string>.Create((x, y) => string.Compare(x, y, StringComparison.Ordinal)))
                .ToList();
        }

        public void Populate()
        {
            ButtonContainer.DisposeAllChildren();
            AddButtons();
        }

        private void AddButtons()
        {
            if (_refundDiabled)
            {
                RefundAllButton.Disabled = true;
                NoRefundLabel.Visible = true;
                SearchBar.Visible = false;
                NoRefundLabel.Text = Loc.GetString("store-refund-window-refund-disabled");
                return;
            }

            if (_listings.Count == 0)
            {
                RefundAllButton.Disabled = true;
                NoRefundLabel.Visible = true;
                SearchBar.Visible = false;
                NoRefundLabel.Text = Loc.GetString("store-refund-window-nothing-to-refund");
                return;
            }

            RefundAllButton.Disabled = false;
            NoRefundLabel.Visible = false;
            SearchBar.Visible = true;

            foreach (var listing in _listings)
            {
                var name = listing.DisplayName;
                var listingUid = listing.Entity;

                var currentButtonRef = new Button
                {
                    Text = name,
                    TextAlign = Label.AlignMode.Right,
                    HorizontalAlignment = HAlignment.Center,
                    VerticalAlignment = VAlignment.Center,
                    SizeFlagsStretchRatio = 1,
                    MinSize = new Vector2(340, 20),
                    ClipText = true,
                };

                currentButtonRef.OnPressed += _ => ListingClicked?.Invoke(listingUid);
                currentButtonRef.Visible = ButtonIsVisible(currentButtonRef);

                ButtonContainer.AddChild(currentButtonRef);
            }
        }

        private bool ButtonIsVisible(Button button)
        {
            return string.IsNullOrEmpty(_searchText) || button.Text == null ||
                   button.Text.Contains(_searchText, StringComparison.OrdinalIgnoreCase);
        }

        private void UpdateVisibleButtons()
        {
            foreach (var child in ButtonContainer.Children)
            {
                if (child is Button button)
                    button.Visible = ButtonIsVisible(button);
            }
        }

        private void OnSearchTextChanged(LineEdit.LineEditEventArgs args)
        {
            _searchText = args.Text;

            UpdateVisibleButtons();
            // Reset scroll bar so they can see the relevant results.
            Scroll.SetScrollValue(Vector2.Zero);
        }
    }
}