// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.UserInterface.Controls;
using Content.Shared._Goobstation.Wizard.Spellblade;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Client._Shitcode.Wizard.Spellblade;

[GenerateTypedNameReferences]
public sealed partial class SpellbladeMenu : RadialMenu
{
    [Dependency] private readonly EntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _protoManager = default!;

    private SpriteSystem _sprites;

    public event Action<ProtoId<SpellbladeEnchantmentPrototype>>? SendSpellbladeSystemMessageAction;

    private EntityUid _item;

    public SpellbladeMenu()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);
        _sprites = _entManager.System<SpriteSystem>();
    }

    public void SetEntity(EntityUid uid)
    {
        _item = uid;
        Refresh();
    }

    public void Refresh()
    {
        var main = FindControl<RadialContainer>("Main");
        main.RemoveAllChildren();

        if (!_entManager.TryGetComponent(_item, out SpellbladeComponent? spellblade))
            return;

        foreach (var enchant in spellblade.Prototypes)
        {
            if (!_protoManager.TryIndex(enchant, out var prototype))
                continue;

            var button = new SpellbladeMenuButton
            {
                SetSize = new Vector2(64, 64),
                ToolTip = Loc.GetString(prototype.Desc),
                ProtoId = prototype.ID
            };

            var texture = new TextureRect
            {
                VerticalAlignment = VAlignment.Center,
                HorizontalAlignment = HAlignment.Center,
                Texture = _sprites.Frame0(prototype.Icon),
                TextureScale = new Vector2(2f, 2f)
            };

            button.AddChild(texture);
            main.AddChild(button);
        }

        AddSpellbladeMenuButtonOnClickActions(main);
    }

    private void AddSpellbladeMenuButtonOnClickActions(RadialContainer control)
    {
        foreach (var child in control.Children)
        {
            if (child is not SpellbladeMenuButton castChild)
                continue;

            castChild.OnButtonUp += _ =>
            {
                SendSpellbladeSystemMessageAction?.Invoke(castChild.ProtoId);
                Close();
            };
        }
    }
}

public sealed class SpellbladeMenuButton : RadialMenuTextureButtonWithSector
{
    public ProtoId<SpellbladeEnchantmentPrototype> ProtoId { get; set; }
}