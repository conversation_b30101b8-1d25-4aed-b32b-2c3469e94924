<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:AbductorCameraConsoleWindow
    xmlns="https://spacestation14.io"
    xmlns:controls="clr-namespace:Content.Client._Shitmed.Antags.Abductor"
    xmlns:cc="clr-namespace:Content.Client.Administration.UI.CustomControls"
    MinSize="400 400">
    <BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
        <BoxContainer Orientation="Horizontal" HorizontalExpand="True" Margin="0 0 0 10">
            <Button Name="StationsButton" Access="Public" Text="&lt; Stations"
                    HorizontalExpand="True" StyleClasses="OpenBoth" />
            <Button Name="BeaconsButton" Access="Public" Text="{Loc 'abductors-ui-beacons'}"
                    HorizontalExpand="True" StyleClasses="OpenBoth" />
        </BoxContainer>
        <cc:HSeparator/>
        <ScrollContainer VScrollEnabled="True" HorizontalExpand="True" VerticalExpand="True">
            <BoxContainer Name="Stations" Access="Public" Orientation="Vertical" Visible="False" />
            <BoxContainer Name="Beacons" Access="Public" Orientation="Vertical" Visible="False" />
        </ScrollContainer>
    </BoxContainer>
</controls:AbductorCameraConsoleWindow>
