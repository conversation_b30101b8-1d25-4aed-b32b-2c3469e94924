// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitmed.Antags.Abductor;
[GenerateTypedNameReferences]
public sealed partial class AbductorConsoleWindow : DefaultWindow
{
    public AbductorConsoleWindow() => RobustXamlLoader.Load(this);
}