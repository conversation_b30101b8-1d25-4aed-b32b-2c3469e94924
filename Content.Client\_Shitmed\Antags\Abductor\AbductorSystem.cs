// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared._Shitmed.Antags.Abductor;

namespace Content.Client._Shitmed.Antags.Abductor;

public sealed class AbductorSystem : SharedAbductorSystem
{
    public override void Initialize()
    {
        base.Initialize();
    }
}