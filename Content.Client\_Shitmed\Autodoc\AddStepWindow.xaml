<!--
SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Title="{Loc 'autodoc-add-step'}">
    <BoxContainer Orientation="Vertical">
        <Button Name="SurgeryButton" Text="{Loc 'autodoc-add-step-surgery'}"/>
        <Button Name="GrabItemButton" Text="{Loc 'autodoc-add-step-grab-item'}"/>
        <Button Name="GrabOrganButton" Text="{Loc 'autodoc-add-step-grab-organ'}"/>
        <Button Name="GrabPartButton" Text="{Loc 'autodoc-add-step-grab-part'}"/>
        <Button Name="StoreItemButton" Text="{Loc 'autodoc-add-step-store-item'}"/>
        <Button Name="SetLabelButton" Text="{Loc 'autodoc-add-step-set-label'}"/>
        <Button Name="WaitButton" Text="{Loc 'autodoc-add-step-wait'}"/>
    </BoxContainer>
</controls:FancyWindow>
