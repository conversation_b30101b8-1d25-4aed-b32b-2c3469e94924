// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Administration;
using Content.Shared._Shitmed.Autodoc;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitmed.Autodoc;

[GenerateTypedNameReferences]
public sealed partial class AddStepWindow : FancyWindow
{
    public event Action<IAutodocStep>? OnAddStep;

    private PickSurgeryWindow? _surgery;
    private DialogWindow? _grab;
    private DialogWindow? _label;
    private DialogWindow? _wait;

    public AddStepWindow()
    {
        RobustXamlLoader.Load(this);

        // close the window once any step is added
        OnAddStep += _ => Close();

        OnClose += () =>
        {
            _surgery?.Close();
            _grab?.Close();
            _label?.Close();
            _wait?.Close();
        };

        // dedicated ui to pick the enum values and surgery type
        SurgeryButton.OnPressed += _ =>
        {
            if (_surgery is {} window)
            {
                window.MoveToFront();
                return;
            }

            _surgery = new PickSurgeryWindow();
            _surgery.OnAddStep += step => OnAddStep?.Invoke(step);
            _surgery.OnClose += () => _surgery = null;
            _surgery.OpenCentered();
        };

        // just picking a string, use a dialog
        GrabItemButton.OnPressed += _ =>
        {
            if (_grab is {} dialog)
            {
                dialog.MoveToFront();
                return;
            }

            var field = "name";
            var prompt = Loc.GetString("autodoc-add-step-grab-item-prompt");
            var placeholder = Loc.GetString("autodoc-add-step-grab-item-placeholder");
            var entry = new QuickDialogEntry(field, QuickDialogEntryType.ShortText, prompt, placeholder);
            var entries = new List<QuickDialogEntry> { entry };
            _grab = new DialogWindow(GrabItemButton.Text!, entries);
            _grab.OnConfirmed += responses =>
            {
                var name = responses[field].Trim();
                if (name.Length < 1 || name.Length > 100)
                    return;

                OnAddStep?.Invoke(new GrabItemAutodocStep()
                {
                    Name = name
                });
            };
            _grab.OnClose += () => _grab = null;
        };

        // no arguments so these are trivial
        GrabOrganButton.OnPressed += _ => OnAddStep?.Invoke(new GrabAnyOrganAutodocStep());
        GrabPartButton.OnPressed += _ => OnAddStep?.Invoke(new GrabAnyBodyPartAutodocStep());
        StoreItemButton.OnPressed += _ => OnAddStep?.Invoke(new StoreItemAutodocStep());

        SetLabelButton.OnPressed += _ =>
        {
            if (_label is {} dialog)
            {
                dialog.MoveToFront();
                return;
            }

            var field = "label";
            var prompt = Loc.GetString("autodoc-add-step-set-label-prompt");
            var entry = new QuickDialogEntry(field, QuickDialogEntryType.ShortText, prompt);
            var entries = new List<QuickDialogEntry> { entry };
            _label = new DialogWindow(SetLabelButton.Text!, entries);
            _label.OnConfirmed += responses =>
            {
                var label = responses[field].Trim();
                if (label.Length < 1 || label.Length > 20)
                    return;

                OnAddStep?.Invoke(new SetLabelAutodocStep()
                {
                    Label = label
                });
            };
            _label.OnClose += () => _label = null;
        };

        WaitButton.OnPressed += _ =>
        {
            if (_wait is {} dialog)
            {
                dialog.MoveToFront();
                return;
            }

            var field = "length";
            var prompt = Loc.GetString("autodoc-add-step-wait-prompt");
            var entry = new QuickDialogEntry(field, QuickDialogEntryType.Integer, prompt);
            var entries = new List<QuickDialogEntry> { entry };
            _wait = new DialogWindow(WaitButton.Text!, entries);
            _wait.OnConfirmed += responses =>
            {
                var length = int.Parse(responses[field].Trim());
                if (length < 1 || length > 30)
                    return;

                OnAddStep?.Invoke(new WaitAutodocStep()
                {
                    Length = length
                });
            };
            _wait.OnClose += () => _wait = null;
        };
    }
}