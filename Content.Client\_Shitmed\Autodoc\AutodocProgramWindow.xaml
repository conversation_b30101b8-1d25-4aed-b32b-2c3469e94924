<!--
SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 <PERSON><PERSON>akman <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Title="{Loc 'autodoc-view-program-title'}"
        SetSize="600 500">
    <BoxContainer Orientation="Horizontal">
        <BoxContainer Orientation="Vertical" Margin="5 5 5 5">
            <Label Name="ProgramTitle"/> <!-- Set to Program.Title -->
            <Button Name="SafetyButton" Text="{Loc 'autodoc-safety-enabled'}"/>
            <Button Name="RemoveButton" StyleClasses="Caution" Text="{Loc 'autodoc-remove-program'}"/>
            <Button Name="AddStepButton" Text="{Loc 'autodoc-add-step'}"/>
            <Button Name="RemoveStepButton" StyleClasses="Caution" Text="{Loc 'autodoc-remove-step'}" Disabled="True"/>
            <Button Name="StartButton" StyleClasses="Caution" Text="{Loc 'autodoc-start-program'}"/>
            <Button Name="ExportProgramButton" Text="{Loc 'autodoc-export-program'}"/>
        </BoxContainer>
        <ScrollContainer HorizontalExpand="True" VerticalExpand="True">
              <ItemList Name="Steps"/> <!-- Set to Program.Steps -->
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
