<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 <PERSON><PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2025 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Title="{Loc 'autodoc-title'}">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer MinWidth="600" MinHeight="400" VerticalExpand="True">
            <BoxContainer Name="Programs" Orientation="Vertical"/> <!-- Populated at runtime -->
        </ScrollContainer>
        <BoxContainer Orientation="Horizontal" Margin="5 5 5 5" Align="Center">
            <Button Name="CreateProgramButton" StyleClasses="OpenRight" Text="{Loc 'autodoc-create-program'}"/>
            <Button Name="ImportProgramButton" StyleClasses="OpenLeft" Text="{Loc 'autodoc-import-program'}"/>
        </BoxContainer>
        <Button Name="AbortButton" MaxWidth="350" Text="{Loc 'autodoc-abort-program'}" Disabled="True"/>
    </BoxContainer>
</controls:FancyWindow>
