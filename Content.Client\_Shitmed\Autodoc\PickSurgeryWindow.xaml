<!--
SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<controls:FancyWindow
        xmlns="https://spacestation14.io"
        xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls"
        Title="{Loc 'autodoc-add-step-surgery'}"
        MinSize="600 400">
    <BoxContainer Orientation="Horizontal" VerticalExpand="True" HorizontalExpand="True">
        <BoxContainer Orientation="Vertical" MinWidth="200" HorizontalAlignment="Center">
            <BoxContainer Name="Symmetry" Orientation="Horizontal" HorizontalExpand="True"/> <!-- Populated with a radio option of BodyPartSymmetry values -->
            <PanelContainer StyleClasses="LowDivider" Margin="0 5 0 5"/>
            <ScrollContainer VerticalExpand="True" HorizontalExpand="True">
                <ItemList Name="Parts"/> <!-- Populated with BodyPartType values -->
            </ScrollContainer>
            <PanelContainer StyleClasses="LowDivider" Margin="0 5 0 5"/>
            <Button Name="SubmitButton" Text="{Loc 'autodoc-submit'}" Disabled="True"/>
        </BoxContainer>
        <ScrollContainer HorizontalExpand="True">
            <ItemList Name="Surgeries"/> <!-- Populated with SharedSurgerySystem.AllSurgeries -->
        </ScrollContainer>
    </BoxContainer>
</controls:FancyWindow>
