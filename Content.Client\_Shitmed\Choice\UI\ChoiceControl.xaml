<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<ui:ChoiceControl
    xmlns="https://spacestation14.io"
    xmlns:ui="clr-namespace:Content.Client._Shitmed.Choice.UI">
    <BoxContainer Orientation="Horizontal">
        <Button Name="Button" Access="Public"
                HorizontalExpand="True" VerticalExpand="False"
                StyleClasses="ButtonSquare" Margin="0">
            <BoxContainer Orientation="Horizontal" Margin="0">
                <TextureRect Name="Texture" Access="Public"
                             HorizontalExpand="False" VerticalExpand="False"
                             Margin="1"/>
                <Control MinWidth="5"/>
                <RichTextLabel Name="NameLabel" Access="Public" VerticalAlignment="Center"/>
            </BoxContainer>
        </Button>
    </BoxContainer>
</ui:ChoiceControl>