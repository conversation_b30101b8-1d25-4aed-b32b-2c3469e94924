// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client._Shitmed.Choice.UI;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitmed.Medical.Surgery;

[GenerateTypedNameReferences]
public sealed partial class SurgeryStepButton : ChoiceControl
{
    public EntityUid Step { get; set; }

    public SurgeryStepButton()
    {
        RobustXamlLoader.Load(this);
    }
}