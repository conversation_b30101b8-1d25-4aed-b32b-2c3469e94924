// SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared._Shitmed.Medical.Surgery;

namespace Content.Client._Shitmed.Medical.Surgery;

public sealed class SurgerySystem : SharedSurgerySystem
{
    public override void Initialize()
    {
        base.Initialize();
    }
}