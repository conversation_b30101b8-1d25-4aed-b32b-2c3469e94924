// SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitmed.Medical.Surgery;

[GenerateTypedNameReferences]
public sealed partial class SurgeryWindow : DefaultWindow
{
    public SurgeryWindow()
    {
        RobustXamlLoader.Load(this);
    }
}