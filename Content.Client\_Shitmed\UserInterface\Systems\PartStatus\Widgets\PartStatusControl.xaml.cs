// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 August Eymann <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Kayzel <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Roudenn <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Spatison <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Trest <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 kurokoTurbo <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared._Shitmed.Targeting;
using Content.Shared._Shitmed.Medical.Surgery.Wounds;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;
using Robust.Client.Input;
using Robust.Client.UserInterface;
using Robust.Shared.Input;
using Robust.Shared.Timing;

namespace Content.Client._Shitmed.UserInterface.Systems.PartStatus.Widgets;

[GenerateTypedNameReferences]
public sealed partial class PartStatusControl : UIWidget
{
    [Dependency] private readonly IGameTiming _timing = default!;
    private readonly Dictionary<TargetBodyPart, TextureRect> _partStatusControls;
    private readonly PartStatusUIController _controller;
    public event Action<GUIBoundKeyEventArgs>? OnMouseDown;
    public PartStatusControl()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);

        _controller = UserInterfaceManager.GetUIController<PartStatusUIController>();
        _partStatusControls = new Dictionary<TargetBodyPart, TextureRect>
        {
            { TargetBodyPart.Head, DollHead },
            { TargetBodyPart.Chest, DollTorso },
            { TargetBodyPart.Groin, DollGroin },
            { TargetBodyPart.LeftArm, DollLeftArm },
            { TargetBodyPart.LeftHand, DollLeftHand },
            { TargetBodyPart.RightArm, DollRightArm },
            { TargetBodyPart.RightHand, DollRightHand },
            { TargetBodyPart.LeftLeg, DollLeftLeg },
            { TargetBodyPart.LeftFoot, DollLeftFoot },
            { TargetBodyPart.RightLeg, DollRightLeg },
            { TargetBodyPart.RightFoot, DollRightFoot },
        };
        MouseFilter = MouseFilterMode.Stop;
        OnKeyBindDown += OnClicked;
    }

    public void SetTextures(Dictionary<TargetBodyPart, WoundableSeverity> state)
    {
        foreach (var (bodyPart, integrity) in state)
        {
            string enumName = Enum.GetName(typeof(TargetBodyPart), bodyPart) ?? "Unknown";
            int enumValue = (int) integrity;
            var texture = new SpriteSpecifier.Rsi(new ResPath($"/Textures/_Shitmed/Interface/Targeting/Status/{enumName.ToLowerInvariant()}.rsi"), $"{enumName.ToLowerInvariant()}_{enumValue}");
            _partStatusControls[bodyPart].Texture = _controller.GetTexture(texture);
        }
    }

    private void OnClicked(GUIBoundKeyEventArgs args)
    {
        if (_timing.IsFirstTimePredicted && args.Function == EngineKeyFunctions.Use)
            _controller.GetPartStatusMessage();
    }

    public void SetVisible(bool visible) => this.Visible = visible;

}
