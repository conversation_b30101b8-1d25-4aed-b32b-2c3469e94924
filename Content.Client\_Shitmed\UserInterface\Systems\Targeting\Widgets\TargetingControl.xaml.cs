// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Kayzel <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Roudenn <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Spatison <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Trest <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>
// SPDX-FileCopyrightText: 2025 kurokoTurbo <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Shared._Shitmed.Targeting;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Client._Shitmed.UserInterface.Systems.Targeting.Widgets;

[GenerateTypedNameReferences]
public sealed partial class TargetingControl : UIWidget
{
    private readonly TargetingUIController _controller;
    private readonly Dictionary<TargetBodyPart, TextureButton> _bodyPartControls;

    public TargetingControl()
    {
        RobustXamlLoader.Load(this);
        _controller = UserInterfaceManager.GetUIController<TargetingUIController>();

        _bodyPartControls = new Dictionary<TargetBodyPart, TextureButton>
        {
            // TODO: ADD EYE AND MOUTH TARGETING
            { TargetBodyPart.Head, HeadButton },
            { TargetBodyPart.Chest, ChestButton },
            { TargetBodyPart.Groin, GroinButton },
            { TargetBodyPart.LeftArm, LeftArmButton },
            { TargetBodyPart.LeftHand, LeftHandButton },
            { TargetBodyPart.RightArm, RightArmButton },
            { TargetBodyPart.RightHand, RightHandButton },
            { TargetBodyPart.LeftLeg, LeftLegButton },
            { TargetBodyPart.LeftFoot, LeftFootButton },
            { TargetBodyPart.RightLeg, RightLegButton },
            { TargetBodyPart.RightFoot, RightFootButton },
        };

        foreach (var bodyPartButton in _bodyPartControls)
        {
            bodyPartButton.Value.MouseFilter = MouseFilterMode.Stop;
            bodyPartButton.Value.OnPressed += _ => SetActiveBodyPart(bodyPartButton.Key);

            TargetDoll.Texture = Theme.ResolveTexture("target_doll");
        }
    }

    private void SetActiveBodyPart(TargetBodyPart bodyPart) => _controller.CycleTarget(bodyPart);

    public void SetBodyPartsVisible(TargetBodyPart bodyPart)
    {
        foreach (var bodyPartButton in _bodyPartControls)
            bodyPartButton.Value.Children.First().Visible = bodyPartButton.Key == bodyPart;
    }

    protected override void OnThemeUpdated() => TargetDoll.Texture = Theme.ResolveTexture("target_doll");

    public void SetTargetDollVisible(bool visible) => Visible = visible;

}