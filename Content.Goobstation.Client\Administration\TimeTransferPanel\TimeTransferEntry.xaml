<!--
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<BoxContainer xmlns="https://spacestation14.io"
              xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
              xmlns:customControls="clr-namespace:Content.Client.Administration.UI.CustomControls;assembly=Content.Client"
              Margin="5 5"
              HorizontalExpand="True"
              Visible="True">
    <PanelContainer StyleClasses="AngleRect" HorizontalExpand="True">
        <BoxContainer Orientation="Horizontal">
            <BoxContainer Orientation="Vertical" VerticalAlignment="Center">
                <CheckBox Name="GroupCheckbox" HorizontalAlignment="Center"/>
            </BoxContainer>
            <customControls:VSeparator Margin="0 0 10 0"/>
            <BoxContainer Orientation="Vertical" HorizontalExpand="True">
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <TextureRect Name="JobIcon" VerticalAlignment="Center" TextureScale="2 2"/>
                    <Control MinWidth="4"/>
                    <Label Name="JobLabel" Text=" " HorizontalAlignment="Center"/>
                </BoxContainer>
                <Control MinHeight="4"/>
                <BoxContainer Orientation="Horizontal" HorizontalExpand="True">
                    <Label Name="TimeLabel" Text="{Loc time-transfer-panel-time}" HorizontalAlignment="Center"/>
                    <Control MinWidth="5"/>
                    <LineEdit Name="TimeEdit" PlaceHolder="0h0m" HorizontalExpand="True"/>
                </BoxContainer>
            </BoxContainer>
        </BoxContainer>
    </PanelContainer>
</BoxContainer>
