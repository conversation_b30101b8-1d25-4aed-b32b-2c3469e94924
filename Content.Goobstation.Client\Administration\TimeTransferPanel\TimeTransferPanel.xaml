<!--
SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
                xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls;assembly=Content.Client"
                Title="{Loc time-transfer-panel-title}"
                MinWidth="500">
    <BoxContainer Orientation="Vertical" VerticalExpand = "True">
        <BoxContainer Orientation="Horizontal" Margin="2">
            <Label Name="PlayerLabel" Text="{Loc time-transfer-panel-player-label}" MinWidth = "120"/>
            <Control MinWidth ="10"/>
            <LineEdit Name="PlayerLine" PlaceHolder="PlayerUid" HorizontalExpand="True" />
        </BoxContainer>
        <BoxContainer Orientation="Horizontal" Margin="2">
            <CheckBox Name="GroupCheckbox" Text="{Loc time-transfer-panel-checkbox-group}" MinWidth = "120"/>
            <Control MinWidth ="10"/>
            <LineEdit Name="GroupTimeLine" PlaceHolder="0h0m" HorizontalExpand="True" />
        </BoxContainer>
        <Control MinHeight="5"/>
        <LineEdit Name="JobSearch" Access="Public" StyleClasses="actionSearchBox" HorizontalExpand="True" PlaceHolder="{Loc time-transfer-panel-search-placeholder}"/>
        <Control MinHeight="10"/>
        <PanelContainer VerticalExpand="True">
                <PanelContainer.PanelOverride>
                    <gfx:StyleBoxFlat BackgroundColor="#1b1b1e" />
                </PanelContainer.PanelOverride>
            <ScrollContainer HScrollEnabled="False"
                     HorizontalExpand="True"
                     VerticalExpand="True"
                     MinHeight="400"
                     SizeFlagsStretchRatio="2.5">
                <BoxContainer Name ="JobContainer" Orientation="Vertical"/>
            </ScrollContainer>
        </PanelContainer>
        <Control MinHeight="10" />
        <controls:StripeBack>
            <Label Name="WarningLabel" Text=" " HorizontalAlignment="Center" Margin="4 4 0 4"/>
        </controls:StripeBack>
        <Control MinHeight="10" />
        <Button Name="AddTimeButton" Text="{Loc time-transfer-panel-add-time}"/>
        <Button Name="SetTimeButton" Text="{Loc time-transfer-panel-set-time}"/>
        <BoxContainer Orientation="Horizontal" Margin="0 4 0 0">
            <Label Text="{Loc time-transfer-panel-author}" StyleClasses="LabelSubText"/>
        </BoxContainer>
    </BoxContainer>
</DefaultWindow>
