// SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Linq;
using Content.Goobstation.Shared.Administration;
using Content.Shared.Roles;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;

namespace Content.Goobstation.Client.Administration.TimeTransferPanel;

[GenerateTypedNameReferences]
public sealed partial class TimeTransferPanel : DefaultWindow
{
    [Dependency] private readonly IGameTiming _gameTiming = default!;
    [Dependency] private readonly IEntityManager _entityManager = default!;
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;

    private readonly SpriteSystem _spriteSystem;

    public Action<(string playerId, List<TimeTransferData> transferList, bool overwrite)>? OnTransferMessageSend;
    private TimeSpan? SetButtonResetOn { get; set; }

    public TimeTransferPanel()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _spriteSystem = _entityManager.System<SpriteSystem>();

        AddTimeButton.OnButtonUp += OnAddTimeButtonPressed;
        SetTimeButton.OnButtonUp += OnSetTimeButtonPressed;
        GroupCheckbox.OnPressed += OnGroupCheckboxPressed;

        JobSearch.OnTextChanged += OnJobSearchTextChanged;

        PopulateJobs();
        UpdateGroup();
        UpdateWarning(" ", Color.LightGreen);
    }

    public void PopulateJobs()
    {
        var jobs = _prototypeManager.EnumeratePrototypes<JobPrototype>()
            .OrderBy(job => job.LocalizedName)
            .ToList();

        foreach(var job in jobs)
        {
            var jobEntry = new TimeTransferEntry(job, _spriteSystem, _prototypeManager);
            JobContainer.AddChild(jobEntry);
        }
    }

    public void UpdateFlag(bool hasFlag)
    {
        AddTimeButton.Visible = hasFlag;
        SetTimeButton.Visible = hasFlag;

        if (!hasFlag)
            UpdateWarning(Loc.GetString("time-transfer-panel-warning-no-perms"), Color.DarkRed);
        else
            UpdateWarning(" ", Color.LightGreen);
    }

    public void TimeTransfer(bool overwrite = false)
    {
        var player = PlayerLine.Text;

        if (string.IsNullOrEmpty(player))
        {
            UpdateWarning(Loc.GetString("time-transfer-panel-warning-no-player"), Color.DarkRed);
            return;
        }

        var dataList = new List<TimeTransferData>();

        if (GroupCheckbox.Pressed)
        {
            if (string.IsNullOrEmpty(GroupTimeLine.Text))
            {
                UpdateWarning(Loc.GetString("time-transfer-panel-warning-group-no-time"), Color.DarkRed);
                return;
            }

            var entryList = GetGroupEntries();
            foreach (var entry in entryList)
            {
                var data = new TimeTransferData(entry.PlaytimeTracker, GroupTimeLine.Text);
                dataList.Add(data);
            }
        }
        else
        {
            foreach (var entry in JobContainer.Children)
            {
                if (entry is not TimeTransferEntry jobEntry)
                    continue;

                var tracker = jobEntry.PlaytimeTracker;
                var timeString = jobEntry.GetJobTimeString();

                if (string.IsNullOrEmpty(timeString))
                    continue;

                var data = new TimeTransferData(tracker, timeString);
                dataList.Add(data);
            }
        }

        OnTransferMessageSend?.Invoke((player, dataList, overwrite));
        UpdateWarning(Loc.GetString("time-transfer-panel-warning-transfer-process"), Color.Gold);
    }

    public List<TimeTransferEntry> GetGroupEntries()
    {
        var list = new List<TimeTransferEntry>();

        foreach (var entry in JobContainer.Children)
        {
            if (entry is not TimeTransferEntry jobEntry)
                continue;

            if (jobEntry.InGroup())
                list.Add(jobEntry);
        }

        return list;
    }

    public void UpdateGroup()
    {
        GroupTimeLine.Visible = GroupCheckbox.Pressed;

        foreach (var entry in JobContainer.Children)
        {
            if (entry is not TimeTransferEntry jobEntry)
                continue;

            jobEntry.UpdateGroupVisibility(GroupCheckbox.Pressed);
        }
    }

    public void OnJobSearchTextChanged(LineEdit.LineEditEventArgs args)
    {
        UpdateSearch();
    }

    public void UpdateSearch()
    {
        foreach (var entry in JobContainer.Children)
        {
            if (entry is not TimeTransferEntry jobEntry)
                continue;

            jobEntry.Visible = ShouldShowJob(jobEntry);
        }
    }

    public void UpdateWarning(string text, Color color)
    {
        WarningLabel.FontColorOverride = color;
        WarningLabel.Text = text;
    }

    public bool ShouldShowJob(TimeTransferEntry jobEntry)
    {
        return jobEntry.JobName != null && JobSearch.Text != null && jobEntry.JobName.Contains(JobSearch.Text, StringComparison.OrdinalIgnoreCase);
    }

    public void OnGroupCheckboxPressed(BaseButton.ButtonEventArgs obj)
    {
        UpdateGroup();
    }

    public void OnAddTimeButtonPressed(BaseButton.ButtonEventArgs obj)
    {
        TimeTransfer(false);
    }

    public void OnSetTimeButtonPressed(BaseButton.ButtonEventArgs obj)
    {
        if (SetButtonResetOn is null)
        {
            SetButtonResetOn = _gameTiming.CurTime.Add(TimeSpan.FromSeconds(3));
            SetTimeButton.ModulateSelfOverride = Color.DarkRed;
            SetTimeButton.Text = Loc.GetString("time-transfer-panel-set-time-confirm");
            return;
        }

        TimeTransfer(true);
        ResetSetButton();
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (SetButtonResetOn != null && _gameTiming.CurTime > SetButtonResetOn)
            ResetSetButton();
    }

    private void ResetSetButton()
    {
        SetButtonResetOn = null;
        SetTimeButton.ModulateSelfOverride = null;
        SetTimeButton.Text = Loc.GetString("time-transfer-panel-set-time");
    }
}