// SPDX-FileCopyrightText: 2024 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SX_7 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using System.Numerics;
using Content.Client.UserInterface.Controls;
using Content.Shared._Goobstation.Weapons.AmmoSelector;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Player;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Prototypes;

namespace Content.Goobstation.Client.AmmoSelector;

[GenerateTypedNameReferences]
public sealed partial class AmmoSelectorMenu : RadialMenu
{
    [Dependency] private readonly EntityManager _entManager = default!;
    [Dependency] private readonly IPrototypeManager _protoManager = default!;
    [Dependency] private readonly IPlayerManager _playerManager = default!;

    private SpriteSystem _sprites;

    public event Action<ProtoId<SelectableAmmoPrototype>>? SendAmmoSelectorSystemMessageAction;

    private EntityUid _item;

    public AmmoSelectorMenu()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);
        _sprites = _entManager.System<SpriteSystem>();
    }

    public void SetEntity(EntityUid uid)
    {
        _item = uid;
        Refresh();
    }

    public void Refresh()
    {
        var main = FindControl<RadialContainer>("Main");
        main.RemoveAllChildren();

        if (!_entManager.TryGetComponent(_item, out AmmoSelectorComponent? ammoSelector))
            return;

        foreach (var ammo in ammoSelector.Prototypes)
        {
            if (!_protoManager.TryIndex(ammo, out var prototype))
                continue;

            var button = new AmmoSelectorMenuButton
            {
                SetSize = new Vector2(64, 64),
                ToolTip = Loc.GetString(prototype.Desc),
                ProtoId = prototype.ID
            };

            var texture = new TextureRect
            {
                VerticalAlignment = VAlignment.Center,
                HorizontalAlignment = HAlignment.Center,
                Texture = _sprites.Frame0(prototype.Icon),
                TextureScale = new Vector2(2f, 2f)
            };

            button.AddChild(texture);
            main.AddChild(button);
        }

        AddAmmoSelectorMenuButtonOnClickActions(main);
    }

    private void AddAmmoSelectorMenuButtonOnClickActions(RadialContainer control)
    {
        foreach (var child in control.Children)
        {
            if (child is not AmmoSelectorMenuButton castChild)
                continue;

            castChild.OnButtonUp += _ =>
            {
                SendAmmoSelectorSystemMessageAction?.Invoke(castChild.ProtoId);
                Close();
            };
        }
    }
}

public sealed class AmmoSelectorMenuButton : RadialMenuTextureButtonWithSector
{
    public ProtoId<SelectableAmmoPrototype> ProtoId { get; set; }
}