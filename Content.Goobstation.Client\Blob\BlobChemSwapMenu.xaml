<!--
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 Moony <<EMAIL>>
SPDX-FileCopyrightText: 2024 Fishbait <<EMAIL>>
SPDX-FileCopyrightText: 2024 fishbait <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+Aiden<PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
               Title="{Loc 'blob-chem-swap-ui-window-name'}"
               MinSize="250 300"
               SetSize="250 300">
    <BoxContainer Orientation="Vertical">
        <ScrollContainer VerticalExpand="True">
            <GridContainer Name="Grid" Columns="3" Margin="0 5" >
            </GridContainer>
        </ScrollContainer>
    </BoxContainer>
</DefaultWindow>
