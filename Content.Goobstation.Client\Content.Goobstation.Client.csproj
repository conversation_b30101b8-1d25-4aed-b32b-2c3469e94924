<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <TargetFramework>$(TargetFramework)</TargetFramework>
      <LangVersion>12</LangVersion>
      <IsPackable>false</IsPackable>
      <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
      <OutputPath>..\bin\Content.Client\</OutputPath>
      <WarningsAsErrors>nullable</WarningsAsErrors>
      <Nullable>enable</Nullable>
      <Configurations>Debug;Release;Tools;DebugOpt</Configurations>
      <Platforms>AnyCPU</Platforms>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Content.Client\Content.Client.csproj" />
      <ProjectReference Include="..\Content.Goobstation.Shared\Content.Goobstation.Shared.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Client\Robust.Client.csproj" Private="false" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" Private="false" />
    </ItemGroup>

    <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
    <Import Project="..\RobustToolbox\MSBuild\XamlIL.targets" />
</Project>
