// SPDX-FileCopyrightText: 2025 BeBright <<EMAIL>>
// SPDX-FileCopyrightText: 2025 BeBright <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Shared.Access.Systems;
using Content.Shared.Administration;
using Content.Shared.CriminalRecords;
using Content.Shared.Dataset;
using Content.Shared.Security;
using Content.Shared.StationRecords;
using Content.Shared.StatusIcon;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.Player;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Utility;
using Robust.Shared.Prototypes;
using Robust.Shared.Random;
using System.Numerics;

namespace Content.Goobstation.Client.CriminalRecords;

[GenerateTypedNameReferences]
public sealed partial class WantedMenu : FancyWindow
{
    [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    [Dependency] private readonly IEntitySystemManager _entitySystem = default!;
    private readonly IPlayerManager _player;
    private readonly AccessReaderSystem _accessReader;
    private readonly IRobustRandom _random;
    private readonly SpriteSystem _spriteSystem;

    [ValidatePrototypeId<LocalizedDatasetPrototype>]
    private const string ReasonPlaceholders = "CriminalRecordsWantedReasonPlaceholders";

    public Action<SecurityStatus>? OnStatusSelected;
    public Action<SecurityStatus, string>? OnDialogConfirmed;

    private CriminalRecord? _selectedRecord;
    private DialogWindow? _reasonDialog;
    private bool _access;
    private EntityUid _uid;

    public WantedMenu(EntityUid uid, IRobustRandom robustRandom, AccessReaderSystem accessReader, IPlayerManager playerManager)
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _spriteSystem = _entitySystem.GetEntitySystem<SpriteSystem>();
        _random = robustRandom;
        _accessReader = accessReader;
        _player = playerManager;
        _uid = uid;

        OnClose += () => _reasonDialog?.Close();

        foreach (var status in Enum.GetValues<SecurityStatus>())
        {
            AddStatusSelect(status);
        }

        StatusOptionButton.OnItemSelected += args =>
        {
            SetStatus((SecurityStatus)args.Id);
        };
    }

    public void UpdateState(CriminalRecordsConsoleState state)
    {
        _access = _player.LocalSession?.AttachedEntity is {} player
                  && _accessReader.IsAllowed(player, _uid);
        StatusOptionButton.Disabled = !_access;
        if (state is { CriminalRecord: not null, StationRecord: not null })
        {
            PopulateRecordContainer(state.StationRecord, state.CriminalRecord);
            _selectedRecord = state.CriminalRecord;
        }
        else
        {
            PersonName.Text = Loc.GetString("criminal-name-error");
            PersonJob.Text = Loc.GetString("criminal-job-error");
            PersonJobIcon.Texture = null;
            WantedReason.Visible = false;
            _selectedRecord = null;
            StatusOptionButton.Disabled = true;
        }
    }
    private void PopulateRecordContainer(GeneralStationRecord stationRecord, CriminalRecord criminalRecord)
    {
        var specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Misc/job_icons.rsi"), "Unknown");
        PersonName.Text = stationRecord.Name ?? "Unknown";
        PersonJob.Text = stationRecord.JobTitle ?? "Unknown";

        if (_prototypeManager.TryIndex<JobIconPrototype>(stationRecord.JobIcon, out var proto))
            PersonJobIcon.Texture = _spriteSystem.Frame0(proto.Icon);
        if (criminalRecord.Status != SecurityStatus.None)
            specifier = new SpriteSpecifier.Rsi(new ResPath("Interface/Misc/security_icons.rsi"), GetStatusIcon(criminalRecord.Status));

        PersonStatusTX.SetFromSpriteSpecifier(specifier);
        PersonStatusTX.DisplayRect.TextureScale = new Vector2(3f, 3f);

        StatusOptionButton.SelectId((int)criminalRecord.Status);
        if (criminalRecord.Reason is { } reason)
        {
            var message = FormattedMessage.FromMarkupOrThrow(Loc.GetString($"criminal-records-console-{criminalRecord.Status.ToString().ToLower()}-reason"));
            message.AddText($": {reason}");

            WantedReason.SetMessage(message);
            WantedReason.Visible = true;
        }
        else
        {
            WantedReason.Visible = false;
        }
    }
    private void AddStatusSelect(SecurityStatus status)
    {
        var name = Loc.GetString($"criminal-records-status-{status.ToString().ToLower()}");
        StatusOptionButton.AddItem(name, (int)status);
    }
    private void SetStatus(SecurityStatus status)
    {
        if (status == SecurityStatus.Wanted
            || status == SecurityStatus.Suspected
            || status == SecurityStatus.Search
            || status == SecurityStatus.Dangerous)
        {
            GetReason(status);
            return;
        }
        OnStatusSelected?.Invoke(status);
    }
    private void GetReason(SecurityStatus status)
    {
        if (_reasonDialog != null)
        {
            _reasonDialog.MoveToFront();
            return;
        }

        var field = "reason";
        var title = Loc.GetString("criminal-records-status-" + status.ToString().ToLower());
        var placeholders = _prototypeManager.Index<LocalizedDatasetPrototype>(ReasonPlaceholders);
        var placeholderKey = _random.Pick(placeholders.Values);
        var placeholderValue = Loc.GetString(placeholderKey);
        var placeholder = Loc.GetString("criminal-records-console-reason-placeholder", ("placeholder", placeholderValue)); // just funny it doesn't actually get used
        var prompt = Loc.GetString("criminal-records-console-reason");
        var entry = new QuickDialogEntry(field, QuickDialogEntryType.LongText, prompt, placeholder);
        var entries = new List<QuickDialogEntry>() { entry };
        _reasonDialog = new DialogWindow(title, entries);

        _reasonDialog.OnConfirmed += responses =>
        {
            var reason = responses[field];
            if (reason.Length < 1 || reason.Length > 256)
                return;

            OnDialogConfirmed?.Invoke(status, reason);
        };

        _reasonDialog.OnClose += () => { _reasonDialog = null; };
    }

    private string GetStatusIcon(SecurityStatus status)
    {
        return status switch
        {
            SecurityStatus.Paroled => "hud_paroled",
            SecurityStatus.Wanted => "hud_wanted",
            SecurityStatus.Detained => "hud_incarcerated",
            SecurityStatus.Discharged => "hud_discharged",
            SecurityStatus.Suspected => "hud_suspected",
            SecurityStatus.Search => "hud_search",
            SecurityStatus.Perma => "hud_perma",
            SecurityStatus.Dangerous => "hud_dangerous",
            _ => "SecurityIconNone"
        };
    }
}
