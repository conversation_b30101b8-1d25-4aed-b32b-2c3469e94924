// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Goobstation.Shared.Factory.Filters;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Goobstation.Client.Factory.UI;

[GenerateTypedNameReferences]
public sealed partial class NameFilterWindow : FancyWindow
{
    [Dependency] private readonly EntityManager _entMan = default!;

    public event Action<string>? OnSetName;
    public event Action<NameFilterMode>? OnSetMode;

    public NameFilterWindow()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);

        foreach (var mode in Enum.GetValues<NameFilterMode>())
        {
            ModeButton.AddItem(Loc.GetString($"name-filter-mode-{mode}"), (int) mode);
        }

        ModeButton.OnItemSelected += args =>
        {
            ModeButton.SelectId(args.Id);
            OnSetMode?.Invoke((NameFilterMode) args.Id);
        };

        NameEdit.OnTextChanged += _ => OnSetName?.Invoke(NameEdit.Text);
    }

    public void SetEntity(EntityUid uid)
    {
        if (!_entMan.TryGetComponent<NameFilterComponent>(uid, out var comp))
            return;

        ModeButton.SelectId((int) comp.Mode);
        var max = comp.MaxLength;
        NameEdit.IsValid = name => name.Length < max;
        NameEdit.Text = comp.Name;
    }
}
