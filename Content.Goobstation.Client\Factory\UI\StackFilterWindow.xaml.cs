// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Goobstation.Shared.Factory.Filters;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.XAML;

namespace Content.Goobstation.Client.Factory.UI;

[GenerateTypedNameReferences]
public sealed partial class StackFilterWindow : FancyWindow
{
    [Dependency] private readonly EntityManager _entMan = default!;

    public event Action<int>? OnSetMin;
    public event Action<int>? OnSetSize;

    public StackFilterWindow()
    {
        IoCManager.InjectDependencies(this);
        RobustXamlLoader.Load(this);

        MinEdit.OnTextChanged += _ =>
        {
            MinConfirmButton.Disabled = !int.TryParse(MinEdit.Text, out var min) || min < 1;
        };

        MinConfirmButton.OnPressed += _ =>
        {
            if (int.TryParse(MinEdit.Text, out var min))
                OnSetMin?.Invoke(min);
        };

        SizeEdit.OnTextChanged += _ =>
        {
            SizeConfirmButton.Disabled = !int.TryParse(SizeEdit.Text, out var size) || size < 0;
        };

        SizeConfirmButton.OnPressed += _ =>
        {
            if (int.TryParse(SizeEdit.Text, out var size))
                OnSetSize?.Invoke(size);
        };
    }

    public void SetEntity(EntityUid uid)
    {
        if (!_entMan.TryGetComponent<StackFilterComponent>(uid, out var comp))
            return;

        var min = comp.Min;
        MinEdit.Text = min.ToString();
        var size = comp.Size;
        SizeEdit.Text = size.ToString();
    }
}
