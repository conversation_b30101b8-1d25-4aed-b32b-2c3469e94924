// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Goobstation.Client.JoinQueue;

[GenerateTypedNameReferences]
public sealed partial class QueueGui : Control
{
    public event Action? QuitPressed;


    public QueueGui()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        LayoutContainer.SetAnchorPreset(this, LayoutContainer.LayoutPreset.Wide);

        QuitButton.OnPressed += (_) => QuitPressed?.Invoke();
    }


    public void UpdateInfo(int total, int position, bool isPatron)
    {
        QueueTotal.Text = total.ToString();
        QueuePosition.Text = position.ToString();
        PatronText.Visible = isPatron;
    }
}
