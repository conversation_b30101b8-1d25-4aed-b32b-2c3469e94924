<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gluesniffler <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<LayoutContainer xmlns="https://spacestation14.io"
                 xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                 xmlns:graphics="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
                 xmlns:goob="clr-namespace:Content.Goobstation.Client.Research.UI"
                 Margin="5"
                 HorizontalExpand="False">
    <BoxContainer Orientation="Vertical" SetSize="80 80">
        <PanelContainer VerticalExpand="True"
                        HorizontalExpand="True"
                        Name="Panel">
            <PanelContainer.PanelOverride>
                <graphics:StyleBoxFlat BackgroundColor="#162031"
                                       BorderColor="#4972A1"
                                       BorderThickness="2"/>
            </PanelContainer.PanelOverride>
            <goob:DrawButton Name="Button"
                             Access="Public"
                             ModulateSelfOverride="#00000000"
                             HorizontalExpand="True"/>
            <TextureRect
                Name="ResearchDisplay"
                TextureScale="2 2"
                SetSize="64 64"
                Stretch="KeepAspectCentered">
            </TextureRect>
        </PanelContainer>
    </BoxContainer>
</LayoutContainer>
