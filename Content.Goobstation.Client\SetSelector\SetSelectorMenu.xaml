<controls:FancyWindow xmlns="https://spacestation14.io"
						xmlns:controls="clr-namespace:Content.Client.UserInterface.Controls;assembly=Content.Client"
						xmlns:gfx="clr-namespace:Robust.Client.Graphics;assembly=Robust.Client"
						Title="{Loc 'set-selector-window-title'}"
						MinSize="700 700">
	<BoxContainer Orientation="Vertical" HorizontalExpand="True" VerticalExpand="True">
		<!-- First Informational panel -->
		<Label Name="Description" Margin="5 5"/>
		<controls:HLine Color="#404040" Thickness="2" Margin="0 5"/>
		<Label Name="SelectedSets" Text="{Loc 'set-selector-window-selected'}" Margin="5 5"/>

		<!-- Second sets panel -->
		<PanelContainer Margin="5 5">
			<PanelContainer.PanelOverride>
				<gfx:StyleBoxFlat BackgroundColor="#050505" />
			</PanelContainer.PanelOverride>
			<ScrollContainer
					HScrollEnabled="False"
					MinSize="0 600"
					SizeFlagsStretchRatio="2"
					VerticalExpand="True">
				<BoxContainer Name="SetsGrid" Orientation="Vertical" MinSize="460 200">
					<!-- Sets is filled by code -->

				</BoxContainer>
			</ScrollContainer>
		</PanelContainer>

		<!-- Third approve button panel -->
		<PanelContainer Margin="10">
			<Button Name="ApproveButton"
					Text="{Loc 'set-selector-window-approve-button'}"
					Margin="0 5"
					Access="Public"
					HorizontalAlignment ="Right"
					StyleClasses="OpenRight"/>
		</PanelContainer>
	</BoxContainer>
</controls:FancyWindow>
