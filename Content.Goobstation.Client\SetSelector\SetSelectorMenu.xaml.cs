// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 pheenty <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Goobstation.Shared.SetSelector;
using Robust.Client.AutoGenerated;
using Robust.Client.GameObjects;
using Robust.Client.UserInterface.XAML;

namespace Content.Goobstation.Client.SetSelector;

[GenerateTypedNameReferences]
public sealed partial class SetSelectorMenu : FancyWindow
{
    [Dependency] private readonly IEntitySystemManager _sysMan = default!;
    private readonly SpriteSystem _spriteSystem;

    public event Action? OnApprove;
    public event Action<int>? OnSetChange;

    public SetSelectorMenu()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
        _spriteSystem = _sysMan.GetEntitySystem<SpriteSystem>();

        ApproveButton.OnPressed += _ =>
        {
            OnApprove?.Invoke();
        };
    }

    public void UpdateState(SetSelectorBoundUserInterfaceState state)
    {
        SetsGrid.DisposeAllChildren();
        var selectedNumber = 0;
        foreach (var (set, info) in state.Sets)
        {
            var child = new SelectableSet(info, _spriteSystem);

            child.SetButton.OnButtonDown += (_) =>
            {
                OnSetChange?.Invoke(set);
            };

            SetsGrid.AddChild(child);

            if (info.Selected)
                selectedNumber++;
        }

        Description.Text = Loc.GetString("set-selector-window-description", ("maxCount", state.MaxSelectedSets));
        SelectedSets.Text = Loc.GetString("set-selector-window-selected", ("selectedCount", selectedNumber), ("maxCount", state.MaxSelectedSets));
        ApproveButton.Disabled = selectedNumber != state.MaxSelectedSets;
    }
}
