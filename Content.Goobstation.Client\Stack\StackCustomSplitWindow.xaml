<!--
SPDX-FileCopyrightText: 2021 Swept <<EMAIL>>
SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
SPDX-FileCopyrightText: 2021 mirrorcult <<EMAIL>>
SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<DefaultWindow xmlns="https://spacestation14.io"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            x:Class="Content.Goobstation.Client.Stack.StackCustomSplitWindow"
            Title="{Loc 'ui-custom-stack-split-title'}"
            Resizable="False">

    <BoxContainer Orientation="Vertical" SeparationOverride="4" MinSize="240 80">
        <BoxContainer Orientation="Horizontal">
            <LineEdit Name="AmountLineEdit" Access="Public" HorizontalExpand="True" PlaceHolder="{Loc 'ui-custom-stack-split-line-edit-placeholder'}"/>
        </BoxContainer>
        <BoxContainer Orientation="Horizontal">
          <Label Name="MaximumAmount" Access="Public" />
        </BoxContainer>
        <Button Name="ApplyButton" Access="Public" Text="{Loc 'ui-custom-stack-split-apply'}"/>
    </BoxContainer>
</DefaultWindow>
