// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;

namespace Content.Goobstation.Client.Stack
{
    [GenerateTypedNameReferences]
    public sealed partial class StackCustomSplitWindow : DefaultWindow
    {
        private int _max = Int32.MaxValue;
        private int _min = 1;

        public StackCustomSplitWindow()
        {
            RobustXamlLoader.Load(this);
            AmountLineEdit.OnTextChanged += OnValueChanged;
        }

        public void SetMax(int max)
        {
          _max = max;
          MaximumAmount.Text = Loc.GetString("comp-stack-split-size", ("size", _max));
        }

        private void OnValueChanged(LineEdit.LineEditEventArgs args)
        {
            if (!int.TryParse((string?)AmountLineEdit.Text, out var amount)  || amount > _max || amount < _min)
                ApplyButton.Disabled = true;
            else
                ApplyButton.Disabled = false;
        }
    }
}