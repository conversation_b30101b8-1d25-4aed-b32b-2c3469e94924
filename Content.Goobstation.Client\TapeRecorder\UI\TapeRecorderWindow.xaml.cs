// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2025 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Client.UserInterface.Controls;
using Content.Goobstation.Shared.TapeRecorder;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.Timing;

namespace Content.Goobstation.Client.TapeRecorder.UI;

[GenerateTypedNameReferences]
public sealed partial class TapeRecorderWindow : FancyWindow
{
    [Dependency] private readonly IEntityManager _entMan = default!;

    public EntityUid Owner;
    private bool _onCooldown;
    private bool _hasCasette;
    private TapeRecorderMode _mode = TapeRecorderMode.Stopped;

    private RadioOptions<TapeRecorderMode> _options = default!;
    private bool _updating;

    public Action<TapeRecorderMode>? OnModeChanged;
    public Action? OnPrintTranscript;

    public TapeRecorderWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        _options = new RadioOptions<TapeRecorderMode>(RadioOptionsLayout.Horizontal);
        Buttons.AddChild(_options);
        _options.FirstButtonStyle = "OpenRight";
        _options.LastButtonStyle = "OpenLeft";
        _options.ButtonStyle = "OpenBoth";
        foreach (var mode in Enum.GetValues<TapeRecorderMode>())
        {
            var name = mode.ToString().ToLower();
            _options.AddItem(Loc.GetString($"tape-recorder-menu-{name}-button"), mode);
        }

        _options.OnItemSelected += args =>
        {
            if (_updating) // don't tell server to change mode to the mode it told us
                return;

            args.Button.Select(args.Id);
            var mode = args.Button.SelectedValue;
            OnModeChanged?.Invoke(mode);
        };

        PrintButton.OnPressed += _ => OnPrintTranscript?.Invoke();

        SetEnabled(TapeRecorderMode.Recording, false);
        SetEnabled(TapeRecorderMode.Playing, false);
        SetEnabled(TapeRecorderMode.Rewinding, false);
    }

    private void SetSlider(float maxTime, float currentTime)
    {
        PlaybackSlider.Disabled = true;
        PlaybackSlider.MaxValue = maxTime;
        PlaybackSlider.Value = currentTime;
    }

    public void UpdatePrint(bool disabled)
    {
        PrintButton.Disabled = disabled;
        _onCooldown = disabled;
    }

    public void UpdateState(TapeRecorderState state)
    {
        if (!_entMan.TryGetComponent<TapeRecorderComponent>(Owner, out var comp))
            return;

        _mode = comp.Mode; // TODO: update UI on handling state instead of adding UpdateUI to everything
        _hasCasette = state.HasCasette;

        _updating = true;

        CassetteLabel.Text = _hasCasette
            ? Loc.GetString("tape-recorder-menu-cassette-label", ("cassetteName", state.CassetteName))
            : Loc.GetString("tape-recorder-menu-no-cassette-label");

        // Select the currently used mode
        _options.SelectByValue(_mode);

        // When tape is ejected or a button can't be used, disable it
        // Server will change to paused once a tape is inactive
        var tapeLeft = state.CurrentTime < state.MaxTime;
        SetEnabled(TapeRecorderMode.Recording, tapeLeft);
        SetEnabled(TapeRecorderMode.Playing, tapeLeft);
        SetEnabled(TapeRecorderMode.Rewinding, state.CurrentTime > float.Epsilon);

        if (state.HasCasette)
            SetSlider(state.MaxTime, state.CurrentTime);

        _updating = false;
    }

    private void SetEnabled(TapeRecorderMode mode, bool condition)
    {
        _options.SetItemDisabled((int) mode, !(_hasCasette && condition));
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);

        if (!_entMan.HasComponent<ActiveTapeRecorderComponent>(Owner))
            return;

        if (!_entMan.TryGetComponent<TapeRecorderComponent>(Owner, out var comp))
            return;

        if (_mode != comp.Mode)
        {
            _mode = comp.Mode;
            _options.SelectByValue(_mode);
        }

        var speed = _mode == TapeRecorderMode.Rewinding
            ? -comp.RewindSpeed
            : 1f;
        PlaybackSlider.Value += args.DeltaSeconds * speed;
    }
}
