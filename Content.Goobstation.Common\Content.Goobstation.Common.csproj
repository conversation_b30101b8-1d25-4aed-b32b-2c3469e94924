<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>$(TargetFramework)</TargetFramework>
        <LangVersion>12</LangVersion>
        <IsPackable>false</IsPackable>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <WarningsAsErrors>nullable</WarningsAsErrors>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Content.ModuleManager\Content.ModuleManager.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    </ItemGroup>

    <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
    <Import Project="..\RobustToolbox\MSBuild\Robust.CompNetworkGenerator.targets" />

</Project>
