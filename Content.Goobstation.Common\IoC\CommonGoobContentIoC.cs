// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Shared.IoC;

namespace Content.Goobstation.Common.IoC;

internal static class CommonGoobContentIoC
{
    internal static void Register()
    {
        var instance = IoCManager.Instance!;
    }
}