using Robust.Shared.GameStates;

/// <summary>
/// EVERYTHING HERE IS A MODIFIED VERSION OF CRIMINAL RECORDS
/// This component is placed in Common so that both Core and Custom modules can access it
/// </summary>

namespace Content.Shared.Overlays;

/// <summary>
///     This component allows you to see Psionics record status of mobs.
/// </summary>
[RegisterComponent, NetworkedComponent]
public sealed partial class ShowPsionicsRecordIconsComponent : Component { }
