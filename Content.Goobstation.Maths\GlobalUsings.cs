// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 August <PERSON>ymann <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

global using System;
global using System.Collections.Generic;
global using Robust.Shared.Analyzers;
global using Robust.Shared.Log;
global using Robust.Shared.Localization;
global using Robust.Shared.GameObjects;
global using Robust.Shared.IoC;
global using Robust.Shared.Maths;
global using Robust.Shared.ViewVariables;
global using Robust.Shared.Serialization.Manager.Attributes;