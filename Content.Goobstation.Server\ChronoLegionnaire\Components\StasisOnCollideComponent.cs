// SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Goobstation.Server.ChronoLegionnaire.Systems;

namespace Content.Goobstation.Server.ChronoLegionnaire.Components;

/// <summary>
/// Marks projectiles that will apply stasis on hit
/// </summary>
[RegisterComponent, Access(typeof(StasisOnCollideSystem))]
public sealed partial class StasisOnCollideComponent : Component
{
    [<PERSON><PERSON>ield("stasisTime")]
    public TimeSpan StasisTime = TimeSpan.FromSeconds(60);

    [DataField("fixture")]
    public string FixtureID = "projectile";
}