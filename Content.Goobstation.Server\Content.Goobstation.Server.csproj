<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
      <TargetFramework>$(TargetFramework)</TargetFramework>
      <LangVersion>12</LangVersion>
      <IsPackable>false</IsPackable>
      <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
      <OutputPath>..\bin\Content.Server\</OutputPath>
      <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
      <NoWarn>1998</NoWarn>
      <WarningsAsErrors>nullable</WarningsAsErrors>
      <Nullable>enable</Nullable>
      <ServerGarbageCollection>true</ServerGarbageCollection>
    </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Concentus" />
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
  </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\Content.Goobstation.Maths\Content.Goobstation.Maths.csproj" />
      <ProjectReference Include="..\Content.Goobstation.Shared\Content.Goobstation.Shared.csproj" />
      <ProjectReference Include="..\Content.Server\Content.Server.csproj" />
      <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    </ItemGroup>

  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
