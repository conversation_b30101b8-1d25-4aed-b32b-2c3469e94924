// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Solstice <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SolsticeOfTheWinter <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Goobstation.Server.Devil.Objectives.Systems;

namespace Content.Goobstation.Server.Devil.Objectives.Components;

[RegisterComponent, Access(typeof(DevilSystem), typeof(DevilObjectiveSystem))]

public sealed partial class SignContractConditionComponent : Component
{
    [DataField]
    public int ContractsSigned = 0;
}
