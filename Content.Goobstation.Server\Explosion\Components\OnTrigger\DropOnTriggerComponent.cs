// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Solstice <<EMAIL>>
// SPDX-FileCopyrightText: 2025 SolsticeOfTheWinter <<EMAIL>>
// SPDX-FileCopyrightText: 2025 pheenty <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Server.Explosion.Components.OnTrigger;

/// <summary>
/// Drops the entities held items on <see cref="TriggerEvent"/>
/// </summary>
[RegisterComponent]
public sealed partial class DropOnTriggerComponent : Component;
