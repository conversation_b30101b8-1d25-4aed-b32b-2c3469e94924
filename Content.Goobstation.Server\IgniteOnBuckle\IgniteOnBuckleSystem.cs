// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 TheBorzoiMustConsume <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Goobstation.Common.IgniteOnBuckle;
using Content.Server.Atmos.Components;
using Content.Server.Atmos.EntitySystems;
using Content.Shared.Buckle.Components;


namespace Content.Goobstation.Server.IgniteOnBuckle;

public sealed class IgniteOnBuckleSystem : EntitySystem
{

    [Dependency] private readonly FlammableSystem _flammable = default!;
    public override void Initialize()
    {
        base.Initialize();
        SubscribeLocalEvent<IgniteOnBuckleComponent, StrappedEvent>(OnBuckled);
    }

    private void OnBuckled(Entity<IgniteOnBuckleComponent> ent, ref StrappedEvent args)
    {
        if (!TryComp<FlammableComponent>(args.<PERSON>, out var flammable))
            return;

        flammable.FireStacks += ent.Comp.FireStacks;
        _flammable.Ignite(args.<PERSON>le, args.Strap, flammable);
    }

}
