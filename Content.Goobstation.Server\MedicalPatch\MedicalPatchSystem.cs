// SPDX-FileCopyrightText: 2024 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Alzore <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Brandon Hu <103440971+<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 CaasGit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Chief-Engineer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Cojoke <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ed <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Emisse <<EMAIL>>
// SPDX-FileCopyrightText: 2024 EmoGarbage404 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Eoin Mcloughlin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Errant <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Fishbait <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Flareguy <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Hrosts <<EMAIL>>
// SPDX-FileCopyrightText: 2024 IProduceWidgets <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ian <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ilya246 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Joel Zimmerman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 JustCone <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Killerqu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ko4ergaPunk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Kukutis96513 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Lye <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MerrytheManokit <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Mr. 27 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 MureixloI <<EMAIL>>
// SPDX-FileCopyrightText: 2024 NakataRin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Nemanja <<EMAIL>>
// SPDX-FileCopyrightText: 2024 OrangeMoronage9622 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 PJBot <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Preston Smith <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Psychpsyo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Repo <<EMAIL>>
// SPDX-FileCopyrightText: 2024 RiceMar1244 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 ShadowCommander <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Simon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 SlamBamActionman <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Stalen <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TakoDragon <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Thomas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 TsjipTsjip <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Ubaser <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Unkn0wn_Gh0st <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vasilis <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Vigers Ray <<EMAIL>>
// SPDX-FileCopyrightText: 2024 beck-thompson <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deathride58 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <<EMAIL>>
// SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
// SPDX-FileCopyrightText: 2024 dffdff2423 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 eoineoineoin <<EMAIL>>
// SPDX-FileCopyrightText: 2024 fishbait <<EMAIL>>
// SPDX-FileCopyrightText: 2024 foboscheshir <<EMAIL>>
// SPDX-FileCopyrightText: 2024 github-actions[bot] <41898282+github-actions[bot]@users.noreply.github.com>
// SPDX-FileCopyrightText: 2024 lzk <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 nikthechampiongr <<EMAIL>>
// SPDX-FileCopyrightText: 2024 plykiya <<EMAIL>>
// SPDX-FileCopyrightText: 2024 saintmuntzer <<EMAIL>>
// SPDX-FileCopyrightText: 2024 shamp <<EMAIL>>
// SPDX-FileCopyrightText: 2024 slarticodefast <<EMAIL>>
// SPDX-FileCopyrightText: 2024 strO0pwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 stroopwafel <<EMAIL>>
// SPDX-FileCopyrightText: 2024 themias <<EMAIL>>
// SPDX-FileCopyrightText: 2024 to4no_fix <<EMAIL>>
// SPDX-FileCopyrightText: 2024 unknown <<EMAIL>>
// SPDX-FileCopyrightText: 2024 voidnull000 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Server.Popups;
using Content.Shared.Administration.Logs;
using Content.Shared.Chemistry;
using Content.Shared.Chemistry.EntitySystems;
using Content.Shared.Database;
using Content.Goobstation.Maths.FixedPoint;
using Content.Shared.Hands.EntitySystems;
using Content.Shared.Sticky;
using Content.Shared.Sticky.Components;
using Content.Shared.Sticky.Systems;
using Robust.Shared.Timing;

namespace Content.Goobstation.Server.MedicalPatch;

public sealed class MedicalPatchSystem : EntitySystem
{
    [Dependency] private readonly PopupSystem _popupSystem = default!;
    [Dependency] private readonly StickySystem _stickySystem = default!;
    [Dependency] private readonly IGameTiming _timing = default!;
    [Dependency] private readonly SharedSolutionContainerSystem _solutionContainers = default!;
    [Dependency] private readonly ReactiveSystem _reactiveSystem = default!;
    [Dependency] protected readonly ISharedAdminLogManager _adminLogger = default!;
    [Dependency] private readonly SharedHandsSystem _hands = default!;

    public override void Initialize()
    {
        base.Initialize();
        SubscribeLocalEvent<MedicalPatchComponent, EntityUnstuckEvent>(OnUnstuck);
        SubscribeLocalEvent<MedicalPatchComponent, EntityStuckEvent>(OnStuck);
    }
    public override void Update(float frameTime)
    {
        base.Update(frameTime);

        if (!_timing.IsFirstTimePredicted)
            return;

        foreach (var comp in EntityManager.EntityQuery<MedicalPatchComponent>())
        {
            if (_timing.CurTime < comp.NextUpdate)
                continue;
            var uid = comp.Owner; // TODO update thsi to the

            if (!TryComp<StickyComponent>(uid, out var stickycomp))
                continue;
            if (stickycomp.StuckTo == null)
                continue;
            comp.NextUpdate = _timing.CurTime + TimeSpan.FromSeconds(comp.UpdateTime);

            Cycle(uid, comp);
        }
    }
    public void Cycle(EntityUid uid, MedicalPatchComponent component)
    {
        if (!TryInject(uid, component, component.TransferAmount))
        {
            if (!TryComp<StickyComponent>(uid, out var stickycomp))
                return;
            _stickySystem.UnstickFromEntity((uid, stickycomp), uid);
        }
    }
    public bool TryInject(EntityUid uid, MedicalPatchComponent component, FixedPoint2 transferAmount)
    {
        if (!TryComp<StickyComponent>(uid, out var stickycomp))
            return false;

        if (stickycomp.StuckTo == null)
            return false;
        var target = (EntityUid) stickycomp.StuckTo;

        if (!_solutionContainers.TryGetSolution(uid, component.SolutionName, out var medicalPatchSoln, out var medicalPatchSolution) || medicalPatchSolution.Volume == 0)
        {
            //Solution Empty
            return false;
        }
        if (!_solutionContainers.TryGetInjectableSolution(target, out var targetSoln, out var targetSolution))
        {
            //_popupSystem.PopupEntity(Loc.GetString("Medical Patch cant find a bloodsystem"), target);
            return false;
        }
        var realTransferAmount = FixedPoint2.Min(transferAmount, targetSolution.AvailableVolume);
        if (realTransferAmount <= 0)
        {
            _popupSystem.PopupEntity(Loc.GetString("No room to inject"), target);
            return true;
        }
        var removedSolution = _solutionContainers.SplitSolution(medicalPatchSoln.Value, realTransferAmount);
        if (!targetSolution.CanAddSolution(removedSolution))
            return true;
        _reactiveSystem.DoEntityReaction(target, removedSolution, ReactionMethod.Injection);
        _solutionContainers.TryAddSolution(targetSoln.Value, removedSolution);
        return true;
    }
    public void OnStuck(EntityUid uid, MedicalPatchComponent component, ref EntityStuckEvent args)
    {
        if (!_solutionContainers.TryGetSolution(uid, component.SolutionName, out var medicalPatchSoln, out var medicalPatchSolution))
            return;

        //Logg the Patch stick to.
        _adminLogger.Add(LogType.ForceFeed, $"{EntityManager.ToPrettyString(args.User):user} stuck a patch on  {EntityManager.ToPrettyString(args.Target):target} using {EntityManager.ToPrettyString(uid):using} containing {SharedSolutionContainerSystem.ToPrettyString(medicalPatchSolution):medicalPatchSolution}");

        if (component.InjectAmmountOnAttatch > 0)
        {
            if (!TryInject(uid, component, component.InjectAmmountOnAttatch))
                return;
        }
        if (component.InjectPercentageOnAttatch > 0)
        {
            if (medicalPatchSolution.Volume == 0)
                return;
            if (!TryInject(uid, component, medicalPatchSolution.Volume * (component.InjectPercentageOnAttatch / 100)))
                return;
        }
    }
    public void OnUnstuck(EntityUid uid, MedicalPatchComponent component, ref EntityUnstuckEvent args)
    {
        if (component.SingleUse)
        {
            if (component.TrashObject!=null)
            {
                var coordinates = Transform(uid).Coordinates;
                var finisher = Spawn(component.TrashObject, coordinates);
                // If the user is holding the item
                if (_hands.IsHolding(args.User, uid, out var hand))
                {
                    Del(uid);

                    // Put the Medicalpatch in the user's hand
                    _hands.TryPickup(args.User, finisher, hand);
                    return;
                }
            }
            QueueDel(uid);
        }
    }
}
