// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 amogus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Cargo;
using Content.Shared.Dataset;
using Robust.Shared.Prototypes;

namespace Content.Goobstation.Server.Pirates.GameTicking.Rules;

[RegisterComponent]
public sealed partial class PendingPirateRuleComponent : Component
{
    [<PERSON>Field] public float PirateSpawnTime = 300f; // 5 minutes
    public float PirateSpawnTimer = 0f;

    [DataField(required: true)] public EntProtoId RansomPrototype;

    // we need this for random announcements otherwise it'd be bland
    [DataField] public string LocAnnouncer = "irs";

    [DataField] public ProtoId<DatasetPrototype>? LocAnnouncers = null;

    [DataField] public float Ransom = 25000f;

    public CargoOrderData? Order;
}