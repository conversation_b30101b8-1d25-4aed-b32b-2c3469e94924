// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Ilya246 <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Server.Singularity.ContainmentField;

/// <summary>
/// Makes containment fields not bounce this entity, and to not vaporize it if it's trash.
/// </summary>
[RegisterComponent]
public sealed partial class ContainmentFieldIgnoreComponent : Component
{

}
