using Content.Server.Psionics.Glimmer;
using Content.Server.StationEvents.Events;
using Content.Shared.GameTicking.Components;
using Content.Shared.Psionics.Glimmer;

namespace Content.Goobstation.Server.StationEvents;

/// <summary>
/// System that integrates glimmer-based events with the Game Director
/// by checking glimmer requirements before allowing events to run
/// </summary>
public sealed class GlimmerGameDirectorSystem : EntitySystem
{
    [Dependency] private readonly GlimmerSystem _glimmerSystem = default!;

    public override void Initialize()
    {
        base.Initialize();
        
        // Subscribe to game rule started events to check glimmer requirements
        SubscribeLocalEvent<GlimmerEventComponent, GameRuleStartedEvent>(OnGlimmerEventStarted);
    }

    private void OnGlimmerEventStarted(EntityUid uid, GlimmerEventComponent component, GameRuleStartedEvent args)
    {
        // Check if current glimmer level allows this event to run
        var currentGlimmer = _glimmerSystem.GlimmerOutput;
        
        if (currentGlimmer < component.MinimumGlimmer || currentGlimmer > component.MaximumGlimmer)
        {
            // Cancel the event if glimmer requirements are not met
            // This provides a safety check for Game Director selections
            Log.Warning($"Glimmer event {ToPrettyString(uid)} cancelled due to glimmer requirements. " +
                       $"Current: {currentGlimmer}, Required: {component.MinimumGlimmer}-{component.MaximumGlimmer}");
            
            // End the game rule immediately
            if (TryComp<GameRuleComponent>(uid, out var gameRule))
            {
                gameRule.EndTime = Timing.CurTime;
            }
        }
    }
}
