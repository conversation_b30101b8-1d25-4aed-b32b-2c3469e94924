using Content.Server.Psionics.Glimmer;
using Content.Shared.GameTicking.Components;
using Content.Shared.Psionics.Glimmer;
using Content.Goobstation.Server.StationEvents.Components;
using Content.Server.GameTicking;
using Robust.Shared.Timing;

namespace Content.Goobstation.Server.StationEvents;

/// <summary>
/// System that integrates glimmer-based events with the Game Director
/// by providing glimmer-based event filtering
/// </summary>
public sealed class GlimmerGameDirectorSystem : EntitySystem
{
    [Dependency] private readonly GlimmerSystem _glimmerSystem = default!;
    [Dependency] private readonly IGameTiming _timing = default!;
    [Dependency] private readonly GameTicker _gameTicker = default!;

    public override void Initialize()
    {
        base.Initialize();

        // Subscribe to game rule started events to check glimmer requirements
        SubscribeLocalEvent<GlimmerEventComponent, GameRuleStartedEvent>(OnGlimmerEventStarted);
    }

    private void OnGlimmerEventStarted(EntityUid uid, GlimmerEventComponent component, GameRuleStartedEvent args)
    {
        // Check if current glimmer level allows this event to run
        var currentGlimmer = _glimmerSystem.GlimmerOutput;

        if (currentGlimmer < component.MinimumGlimmer || currentGlimmer > component.MaximumGlimmer)
        {
            // Cancel the event if glimmer requirements are not met
            // This provides a safety check for Game Director selections
            Log.Warning($"Glimmer event {ToPrettyString(uid)} cancelled due to glimmer requirements. " +
                       $"Current: {currentGlimmer}, Required: {component.MinimumGlimmer}-{component.MaximumGlimmer}");

            // End the game rule immediately
            _gameTicker.EndGameRule(uid);
        }
    }
}
