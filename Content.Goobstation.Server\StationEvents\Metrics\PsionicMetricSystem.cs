using Content.Goobstation.Server.StationEvents.Metric;
using Content.Shared.Psionics.Glimmer;
using Content.Server.Psionics.Glimmer;

namespace Content.Goobstation.Server.StationEvents.Metrics;

/// <summary>
/// Tracks psionic activity on the station based on glimmer levels
/// </summary>
public sealed class PsionicMetricSystem : EntitySystem
{
    [Dependency] private readonly GlimmerSystem _glimmerSystem = default!;

    public override void Initialize()
    {
        base.Initialize();
        SubscribeLocalEvent<PsionicMetricComponent, CalculateChaosEvent>(OnCalculateChaos);
    }

    private void OnCalculateChaos(EntityUid uid, PsionicMetricComponent component, ref CalculateChaosEvent args)
    {
        if (!_glimmerSystem.GetGlimmerEnabled())
            return;

        var glimmerOutput = _glimmerSystem.GlimmerOutput;

        // Convert glimmer (0-1000) to psionic chaos metric
        // Higher glimmer = more psionic chaos
        var psionicChaos = glimmerOutput / 10.0; // Scale to 0-100 range

        args.Metrics.ChaosDict[ChaosMetric.Psionic] = psionicChaos;
    }
}
