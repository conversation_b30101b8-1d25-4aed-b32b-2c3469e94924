// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Marcus F <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Marcus F <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.Body.Components;

/// <summary>
///     Used to ensure that BreathingImmunityComponent is not overriden.
/// </summary>
[RegisterComponent]
public sealed partial class SpecialBreathingImmunityComponent : Component;
