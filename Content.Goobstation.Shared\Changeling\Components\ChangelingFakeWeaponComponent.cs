// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Marcus F <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Shared.GameStates;

namespace Content.Goobstation.Shared.Changeling.Components;

/// <summary>
///     Marks an entity that currently has a fake changeling weapon.
/// </summary>
[RegisterComponent, NetworkedComponent]
public sealed partial class ChangelingFakeWeaponComponent : Component
{

}
