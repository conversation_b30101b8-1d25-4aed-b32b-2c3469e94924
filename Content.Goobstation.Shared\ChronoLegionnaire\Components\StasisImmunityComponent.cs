// SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.ChronoLegionnaire.Components
{
    /// <summary>
    /// Marks an entity that cannot be affect by stasis
    /// </summary>
    [RegisterComponent]
    public sealed partial class StasisImmunityComponent : Component
    {
        /// <summary>
        /// Will the stasis immunity go away with stasis protection?
        /// </summary>
        [DataField]
        public bool DependsOnProtection = true;
    }
}