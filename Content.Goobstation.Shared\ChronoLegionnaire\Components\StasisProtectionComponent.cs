// SPDX-FileCopyrightText: 2024 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.ChronoLegionnaire.Components
{
    /// <summary>
    /// Marks entity (clothing) that will give stasis immunity to wearer
    /// </summary>
    [RegisterComponent]
    public sealed partial class StasisProtectionComponent : Component
    {
        /// <summary>
        /// Stamina buff to entity wearer (until stun resist will be added)
        /// </summary>
        [DataField]
        public float StaminaModifier = 10f;
    }
}