// SPDX-FileCopyrightText: 2024 <PERSON><PERSON><PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 VMSolidus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON>k<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.Clothing.Components;

[RegisterComponent]
public sealed partial class ClothingGrantTagComponent : Component
{
    [DataField("tag", required: true), ViewVariables(VVAccess.ReadWrite)]
    public string Tag = "";

    [ViewVariables(VVAccess.ReadWrite)]
    public bool IsActive = false;
}