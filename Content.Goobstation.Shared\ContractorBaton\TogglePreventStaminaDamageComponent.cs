// SPDX-FileCopyrightText: 2024 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Remuchi <72476615+<PERSON><EMAIL>>
// SPDX-FileCopyrightText: 2024 VMSolidus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.ContractorBaton;

[RegisterComponent]
public sealed partial class TogglePreventStaminaDamageComponent : Component
{
}