// SPDX-FileCopyrightText: 2024 Aviu00 <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Remuchi <<EMAIL>>
// SPDX-FileCopyrightText: 2024 VMSolidus <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON>k<PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Shared.Damage.Events;
using Content.Shared.Item.ItemToggle;

namespace Content.Goobstation.Shared.ContractorBaton;

public sealed class TogglePreventStaminaDamageSystem : EntitySystem
{
    [Dependency] private readonly ItemToggleSystem _toggle = default!;

    public override void Initialize()
    {
        base.Initialize();

        SubscribeLocalEvent<TogglePreventStaminaDamageComponent, StaminaDamageOnHitAttemptEvent>(OnStaminaHitAttempt);
    }

    private void OnStaminaHitAttempt(Entity<TogglePreventStaminaDamageComponent> ent,
        ref StaminaDamageOnHitAttemptEvent args)
    {
        if (!_toggle.IsActivated(ent.Owner))
            args.Cancelled = true;
    }
}