// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Roudenn <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

namespace Content.Goobstation.Shared.Medical.Components;

/// <summary>
/// Component assigned to entities that are in an uber state. Used for visuals.
/// </summary>
[RegisterComponent]
public sealed partial class MediGunUberedComponent : Component
{

}
