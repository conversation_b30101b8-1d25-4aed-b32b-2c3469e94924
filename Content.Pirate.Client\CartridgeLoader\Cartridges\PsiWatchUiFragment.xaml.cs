using Content.Shared.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

/// <summary>
/// ADAPTED FROM SECWATCH - DELTAV
/// </summary>

namespace Content.Pirate.Client.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class PsiWatchUiFragment : BoxContainer
{
    public PsiWatchUiFragment()
    {
        RobustXamlLoader.Load(this);
    }

    public void UpdateState(PsiWatchUiState state)
    {
        NoEntries.Visible = state.Entries.Count == 0;
        Entries.RemoveAllChildren();
        foreach (var entry in state.Entries)
        {
            Entries.AddChild(new PsiWatchEntryControl(entry));
        }
    }
}
