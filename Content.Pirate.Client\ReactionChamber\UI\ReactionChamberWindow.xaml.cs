using System.Collections.Generic;
using System.Collections.Immutable;
using Content.Client.Resources;
using Content.Client.Stylesheets;
using Content.Pirate.Client.UserInterface.TransitionText;
using Content.Pirate.Shared.ReactionChamber;
using Content.Pirate.Shared.ReactionChamber.Components;
using Content.Shared.Chemistry.Reagent;
using Robust.Client.AutoGenerated;
using Robust.Client.ResourceManagement;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.CustomControls;
using Robust.Client.UserInterface.XAML;
using Robust.Shared.IoC;
using Robust.Shared.Localization;
using Robust.Shared.Prototypes;
using Robust.Shared.Timing;


namespace Content.Pirate.Client.ReactionChamber.UI;

[GenerateTypedNameReferences]
public sealed partial class ReactionChamberWindow : DefaultWindow
{
    // [Dependency] private readonly IPrototypeManager _prototypeManager = default!;
    public bool Active;
    public float Temp;
    private const float TempTransitionDuration = 1f;
    private float _lastSolnTemp = 0;
    private List<float>? _tempLabelValues = null;
    public float SolnTemp;
    public ReactionChamberWindow()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);
    }
    public void UpdateState(ReactionChamberBoundUIState state)
    {
        ContainerInfo.RemoveAllChildren();
        ReagentsContainer.RemoveAllChildren();
        Sprite.SetEntity(null);
        if (state.Beaker is not null && state.BeakerInfo is not null)
        {

            if (state.BeakerInfo.SpinBoxTemp is not null)
                TempField.Value = state.BeakerInfo.SpinBoxTemp.Value.Float();
            Sprite.SetEntity(state.Beaker.Value);

            var beakerVolLabel = new Label();
            var beakerNameLabel = new Label();
            beakerNameLabel.Text = $"{state.BeakerInfo.Name}";
            beakerNameLabel.HorizontalAlignment = HAlignment.Center;
            beakerVolLabel.Text = $"{state.BeakerInfo.Volume}/{state.BeakerInfo.MaxVolume}";
            beakerVolLabel.HorizontalAlignment = HAlignment.Center;

            ContainerInfo.AddChild(beakerNameLabel);
            ContainerInfo.AddChild(beakerVolLabel);
            if (state.BeakerInfo.Reagents is not null)
            {
                foreach (var (reagent, quantity) in state.BeakerInfo.Reagents)
                {
                    var reagentLabel = new Label();
                    var quantityLabel = new Label
                    {
                        Text = Loc.GetString("reagent-dispenser-window-quantity-label-text", ("quantity", quantity)),
                        StyleClasses = { StyleNano.StyleClassLabelSecondaryColor },
                    };
                    reagentLabel.Text = reagent.Prototype + ": ";

                    // if (_prototypeManager.TryIndex(reagetnt.Prototype, out ReagentPrototype? p))
                    //     name = p.LocalizedName;
                    ReagentsContainer.Children.Add(new BoxContainer
                    {
                        Orientation = BoxContainer.LayoutOrientation.Horizontal,
                        Children =
                    {
                        reagentLabel,
                        quantityLabel,
                    }
                    });
                }
                if (state.BeakerInfo.Temp is not null)
                    SolnTemp = (float) state.BeakerInfo.Temp.Value;
                else
                    SolnTemp = _lastSolnTemp;
            }
        }
        UpdateActiveButtonUI(state.Active);
    }

    protected override void FrameUpdate(FrameEventArgs args)
    {
        base.FrameUpdate(args);
        if (_lastSolnTemp == SolnTemp) return;

        _tempLabelValues ??= TransitionText.GetLinearFloatTransitionValuesList(_lastSolnTemp, SolnTemp, TempTransitionDuration, args.DeltaSeconds);
        if (!(_tempLabelValues.Count <= 0))
        {
            TemperatureLabel.Text = $"{_tempLabelValues[0]}";
            _tempLabelValues.RemoveAt(0);
        }
        else
        {
            _tempLabelValues = null;
            _lastSolnTemp = SolnTemp;
        }
    }

    public void SetTemp(float temp)
    {
        Temp = temp;
        TempField.Value = Temp;
    }
    /// <summary>
    /// Changes button look in UI
    /// </summary>
    /// <param name="active"></param>
    public void UpdateActiveButtonUI(bool active)
    {
        Active = active;
        if (Active)
        {
            ActiveButton.TexturePath = "/Textures/_Pirate/UserInterface/Buttons/switch.rsi/switch_on.png";
        }
        else
        {
            ActiveButton.TexturePath = "/Textures/_Pirate/UserInterface/Buttons/switch.rsi/switch_off.png";
        }
        ActiveButton.Pressed = !Active;
    }
}
