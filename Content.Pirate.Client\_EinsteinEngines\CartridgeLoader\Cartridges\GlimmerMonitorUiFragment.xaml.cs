﻿using System.Linq;
using System.Numerics;
using Content.Client.UserInterface;
using Robust.Client.AutoGenerated;
using Robust.Client.ResourceManagement;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

namespace Content.Pirate.Client._EinsteinEngines.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class GlimmerMonitorUiFragment : BoxContainer
{
    [Dependency] private readonly IResourceCache _resourceCache = default!;

    public event Action<bool>? OnSync;
    private List<int> _cachedValues = new();

    public GlimmerMonitorUiFragment()
    {
        RobustXamlLoader.Load(this);
        IoCManager.InjectDependencies(this);

        Orientation = LayoutOrientation.Vertical;
        HorizontalExpand = true;
        VerticalExpand = true;

        var intervalGroup = new ButtonGroup();
        IntervalButton6s.Group = intervalGroup;
        IntervalButton1.Group = intervalGroup;
        IntervalButton5.Group = intervalGroup;
        IntervalButton10.Group = intervalGroup;

        IntervalButton6s.Pressed = true;

        IntervalButton6s.OnPressed += _ => UpdateState(_cachedValues);
        IntervalButton1.OnPressed += _ => UpdateState(_cachedValues);
        IntervalButton5.OnPressed += _ => UpdateState(_cachedValues);
        IntervalButton10.OnPressed += _ => UpdateState(_cachedValues);

        SyncButton.OnPressed += _ => OnSync?.Invoke(true);
    }

    public void UpdateState(List<int> glimmerValues)
    {
        _cachedValues = glimmerValues;
        if (glimmerValues.Count < 1)
            return;

        MonitorBox.RemoveAllChildren();

        var glimmerLabel = new Label();
        glimmerLabel.Text = Loc.GetString("glimmer-monitor-current-glimmer", ("glimmer", glimmerValues[^1]));
        MonitorBox.AddChild(glimmerLabel);

        var formattedValues = FormatGlimmerValues(glimmerValues);
        var graph = new GlimmerGraph(_resourceCache, formattedValues);
        graph.SetSize = new Vector2(450, 250);
        MonitorBox.AddChild(graph);
    }


    private List<int> FormatGlimmerValues(List<int> glimmerValues)
    {
        var returnList = glimmerValues;

        if (IntervalButton1.Pressed)
            returnList = GetAveragedList(glimmerValues, 10);
        else if (IntervalButton5.Pressed)
            returnList = GetAveragedList(glimmerValues, 50);
        else if (IntervalButton10.Pressed)
            returnList = GetAveragedList(glimmerValues, 100);

        return ClipToFifteen(returnList);
    }

    /// <summary>
    /// Format glimmer values to get <=15 data points correctly.
    /// </summary>
    private List<int> ClipToFifteen(List<int> glimmerValues)
    {
        List<int> returnList;

        if (glimmerValues.Count <= 15)
        {
            returnList = glimmerValues;
        }
        else
        {
            returnList = glimmerValues.Skip(glimmerValues.Count - 15).ToList();
        }

        return returnList;
    }

    private List<int> GetAveragedList(IEnumerable<int> glimmerValues, int interval)
    {
        var returnList = new List<int>();
        var subtotal = 0;
        var elementsPassed = 0;
        for (int i = 0; i < glimmerValues.Count(); ++i)
        {
            subtotal += glimmerValues.ElementAt(i);
            ++elementsPassed;
            if (elementsPassed == interval)
            {
                returnList.Add(subtotal / interval);
                subtotal = 0;
                elementsPassed = 0;
            }
        }
        if (elementsPassed != 0)
            returnList.Add(subtotal / elementsPassed);
        return returnList;
    }
}
