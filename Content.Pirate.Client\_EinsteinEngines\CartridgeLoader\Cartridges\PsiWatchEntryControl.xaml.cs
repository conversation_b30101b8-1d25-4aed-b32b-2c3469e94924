using Content.Shared.CartridgeLoader.Cartridges;
using Robust.Client.AutoGenerated;
using Robust.Client.UserInterface;
using Robust.Client.UserInterface.Controls;
using Robust.Client.UserInterface.XAML;

/// <summary>
/// ADAPTED FROM SECWATCH - DELTAV
/// </summary>

namespace Content.Pirate.Client._EinsteinEngines.CartridgeLoader.Cartridges;

[GenerateTypedNameReferences]
public sealed partial class PsiWatchEntryControl : BoxContainer
{
    public PsiWatchEntryControl(PsiWatchEntry entry)
    {
        RobustXamlLoader.Load(this);

        Status.Text = Loc.GetString($"psionics-records-status-{entry.Status.ToString().ToLower()}");
        Title.Text = Loc.GetString("psi-watch-entry", ("name", entry.Name), ("job", entry.Job));

        Reason.Text = entry.Reason ?? Loc.GetString("psi-watch-no-reason");
    }
}
