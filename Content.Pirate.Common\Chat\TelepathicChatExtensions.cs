using Robust.Shared.GameStates;

namespace Content.Server.Chat;

/// <summary>
/// Stub class for TelepathicChatSystem to satisfy dependencies
/// The actual implementation is in Content.Pirate.Server.Chat.TelepathicChatSystem
/// This is placed in Common so Core modules can reference it without errors
/// </summary>
public sealed class TelepathicChatSystem : EntitySystem
{
    /// <summary>
    /// Stub method - actual implementation is in Content.Pirate.Server
    /// </summary>
    public override void Initialize()
    {
        base.Initialize();
        // This is intentionally empty - the actual TelepathicChatSystem
        // is implemented in Content.Pirate.Server.Chat.TelepathicChatSystem
    }

    /// <summary>
    /// Stub method - actual implementation is in Content.Pirate.Server
    /// </summary>
    public void SendTelepathicChat(EntityUid source, string message, bool hideChat)
    {
        // This is intentionally empty - the actual implementation
        // is in Content.Pirate.Server.Chat.TelepathicChatSystem
    }
}
