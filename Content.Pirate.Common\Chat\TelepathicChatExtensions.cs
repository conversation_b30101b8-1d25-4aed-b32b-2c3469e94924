using Robust.Shared.GameStates;

namespace Content.Server._EinsteinEngines.Chat;

/// <summary>
/// Partial class extension for TelepathicChatSystem to provide missing functionality
/// This is placed in Common so Core modules can access it
/// </summary>
public partial class TelepathicChatSystem
{
    /// <summary>
    /// Provides the missing InitializePsychognomy method
    /// This is a stub implementation - actual psychognomy logic should be handled
    /// by systems in Content.Pirate.Server through events
    /// </summary>
    private void InitializePsychognomy()
    {
        // This is intentionally empty - psychognomy initialization
        // should be handled by dedicated systems in Content.Pirate.Server
        // through events and proper module architecture

        // If needed, raise an event that Content.Pirate.Server systems can subscribe to
        // var ev = new PsychognomyInitializeEvent();
        // RaiseLocalEvent(ev);
    }

    /// <summary>
    /// Provides the missing SourceToDescriptor method
    /// This is a stub implementation
    /// </summary>
    private string SourceToDescriptor(EntityUid source)
    {
        // Simple implementation - return a default descriptor
        // The actual implementation should be in Content.Pirate.Server
        return "telepathic-source";
    }
}
