// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
// SPDX-FileCopyrightText: 2025 <PERSON>chelle <<EMAIL>>
// SPDX-FileCopyrightText: 2025 GoobBot <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Content.Pirate.Server.Pacification.Managers;
using Robust.Shared.IoC;

namespace Content.Pirate.Server.IoC;

internal static class ServerPirateContentIoC
{
    internal static void Register()
    {
        var instance = IoCManager.Instance!;

        instance.Register<PacifyManager>();
    }
}
