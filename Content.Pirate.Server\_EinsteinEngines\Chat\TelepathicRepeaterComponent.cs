namespace Content.Pirate.Server.Chat;

/// <summary>
///     Repeats whatever is happening in telepathic chat.
/// </summary>
[RegisterComponent]D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Overlays\PsionicsInventoryRelaySystem.cs(35,39): error CS1061: 'ContainerSlot' does not contain a definition for 'Value' and no accessible extension method 'Value' accepting a first argument of type 'ContainerSlot' could be found (are you missing a using directive or an assembly reference?) [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(27,9): error CS0103: The name 'RaiseLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(31,16): error CS0103: The name '_random' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(35,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(36,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(37,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(38,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(39,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(40,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(41,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(43,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(44,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(45,9): error CS0103: The name 'SubscribeLocalEvent' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(69,18): error CS0103: The name 'HasComp' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(141,14): error CS0103: The name 'HasComp' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(141,47): error CS0103: The name 'HasComp' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(144,14): error CS0103: The name 'HasComp' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\_EinsteinEngines\Chat\TelepathicChatSystem.Psychognomy.cs(144,51): error CS0103: The name 'HasComp' does not exist in the current context [D:\AAAA\clones\VOID\Goob-Station\Content.Pirate.Server\Content.Pirate.Server.csproj]
public sealed partial class TelepathicRepeaterComponent : Component { }

