using Robust.Shared.Prototypes;

namespace Content.Shared.Actions.Events;

public sealed partial class SummonPsionicFamiliarActionEvent : InstantActionEvent
{
    /// <summary>
    ///     The entity to be spawned by this power.
    /// </summary>
    [DataField]
    public EntProtoId? FamiliarProto;

    /// <summary>
    ///     The name of this power, used for logging purposes.
    /// </summary>
    [DataField]
    public string PowerName;

    /// <summary>
    ///     Whether this power checks if the wearer is psionically insulated.
    /// </summary>
    [DataField]
    public bool CheckInsulation;

    /// <summary>
    ///     Whether this power generates glimmer when used.
    /// </summary>
    [DataField]
    public bool DoGlimmerEffects;

    /// <summary>
    ///     Whether the summoned entity will follow the one who summoned it.
    /// </summary>
    [DataField]
    public bool FollowMaster;

    /// <summary>
    ///     The minimum amount of glimmer generated by this power.
    /// </summary>
    [DataField]
    public int MinGlimmer;

    /// <summary>
    ///     The maximum amount of glimmer generated by this power.
    /// </summary>
    [DataField]
    public int MaxGlimmer;
}
