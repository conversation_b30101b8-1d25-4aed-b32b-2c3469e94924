﻿// <auto-generated />
using System;
using Content.Server.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Content.Server.Database.Migrations.Sqlite
{
    [DbContext(typeof(SqliteServerDbContext))]
    [Migration("20250613195335_DropBorgNames")]
    partial class DropBorgNames
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.1");

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.Property<int?>("AdminRankId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_rank_id");

                    b.Property<bool>("Deadminned")
                        .HasColumnType("INTEGER")
                        .HasColumnName("deadminned");

                    b.Property<bool>("Suspended")
                        .HasColumnType("INTEGER")
                        .HasColumnName("suspended");

                    b.Property<string>("Title")
                        .HasColumnType("TEXT")
                        .HasColumnName("title");

                    b.HasKey("UserId")
                        .HasName("PK_admin");

                    b.HasIndex("AdminRankId")
                        .HasDatabaseName("IX_admin_admin_rank_id");

                    b.ToTable("admin", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_flag_id");

                    b.Property<Guid>("AdminId")
                        .HasColumnType("TEXT")
                        .HasColumnName("admin_id");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("flag");

                    b.Property<bool>("Negative")
                        .HasColumnType("INTEGER")
                        .HasColumnName("negative");

                    b.HasKey("Id")
                        .HasName("PK_admin_flag");

                    b.HasIndex("AdminId")
                        .HasDatabaseName("IX_admin_flag_admin_id");

                    b.HasIndex("Flag", "AdminId")
                        .IsUnique();

                    b.ToTable("admin_flag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.Property<int>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<int>("Id")
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_log_id");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT")
                        .HasColumnName("date");

                    b.Property<sbyte>("Impact")
                        .HasColumnType("INTEGER")
                        .HasColumnName("impact");

                    b.Property<string>("Json")
                        .IsRequired()
                        .HasColumnType("jsonb")
                        .HasColumnName("json");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("message");

                    b.Property<int>("Type")
                        .HasColumnType("INTEGER")
                        .HasColumnName("type");

                    b.HasKey("RoundId", "Id")
                        .HasName("PK_admin_log");

                    b.HasIndex("Date");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_admin_log_type");

                    b.ToTable("admin_log", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminLogPlayer", b =>
                {
                    b.Property<int>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<int>("LogId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("log_id");

                    b.Property<Guid>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.HasKey("RoundId", "LogId", "PlayerUserId")
                        .HasName("PK_admin_log_player");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_log_player_player_user_id");

                    b.ToTable("admin_log_player", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_messages_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_by_id");

                    b.Property<bool>("Dismissed")
                        .HasColumnType("INTEGER")
                        .HasColumnName("dismissed");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("TEXT")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("TEXT")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<bool>("Seen")
                        .HasColumnType("INTEGER")
                        .HasColumnName("seen");

                    b.HasKey("Id")
                        .HasName("PK_admin_messages");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_messages_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_messages_round_id");

                    b.ToTable("admin_messages", null, t =>
                        {
                            t.HasCheckConstraint("NotDismissedAndSeen", "NOT dismissed OR seen");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.AdminNote", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_notes_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime>("LastEditedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("TEXT")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("TEXT")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<bool>("Secret")
                        .HasColumnType("INTEGER")
                        .HasColumnName("secret");

                    b.Property<int>("Severity")
                        .HasColumnType("INTEGER")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_admin_notes");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_notes_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_notes_round_id");

                    b.ToTable("admin_notes", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminRank", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_rank_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK_admin_rank");

                    b.ToTable("admin_rank", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminRankFlag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_rank_flag_id");

                    b.Property<int>("AdminRankId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_rank_id");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("flag");

                    b.HasKey("Id")
                        .HasName("PK_admin_rank_flag");

                    b.HasIndex("AdminRankId");

                    b.HasIndex("Flag", "AdminRankId")
                        .IsUnique();

                    b.ToTable("admin_rank_flag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AdminWatchlist", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("admin_watchlists_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_at");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("created_by_id");

                    b.Property<bool>("Deleted")
                        .HasColumnType("INTEGER")
                        .HasColumnName("deleted");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_at");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("expiration_time");

                    b.Property<DateTime>("LastEditedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_by_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(4096)
                        .HasColumnType("TEXT")
                        .HasColumnName("message");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("TEXT")
                        .HasColumnName("playtime_at_note");

                    b.Property<int?>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.HasKey("Id")
                        .HasName("PK_admin_watchlists");

                    b.HasIndex("CreatedById");

                    b.HasIndex("DeletedById");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_admin_watchlists_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_admin_watchlists_round_id");

                    b.ToTable("admin_watchlists", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Antag", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("antag_id");

                    b.Property<string>("AntagName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("antag_name");

                    b.Property<int>("ProfileId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_id");

                    b.HasKey("Id")
                        .HasName("PK_antag");

                    b.HasIndex("ProfileId", "AntagName")
                        .IsUnique();

                    b.ToTable("antag", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.AssignedUserId", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("assigned_user_id_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("PK_assigned_user_id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.HasIndex("UserName")
                        .IsUnique();

                    b.ToTable("assigned_user_id", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.BanTemplate", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("ban_template_id");

                    b.Property<bool>("AutoDelete")
                        .HasColumnType("INTEGER")
                        .HasColumnName("auto_delete");

                    b.Property<int>("ExemptFlags")
                        .HasColumnType("INTEGER")
                        .HasColumnName("exempt_flags");

                    b.Property<bool>("Hidden")
                        .HasColumnType("INTEGER")
                        .HasColumnName("hidden");

                    b.Property<TimeSpan>("Length")
                        .HasColumnType("TEXT")
                        .HasColumnName("length");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("reason");

                    b.Property<int>("Severity")
                        .HasColumnType("INTEGER")
                        .HasColumnName("severity");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("PK_ban_template");

                    b.ToTable("ban_template", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Blacklist", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.HasKey("UserId")
                        .HasName("PK_blacklist");

                    b.ToTable("blacklist", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("connection_log_id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("address");

                    b.Property<byte?>("Denied")
                        .HasColumnType("INTEGER")
                        .HasColumnName("denied");

                    b.Property<int>("ServerId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasDefaultValue(0)
                        .HasColumnName("server_id");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT")
                        .HasColumnName("time");

                    b.Property<float>("Trust")
                        .HasColumnType("REAL")
                        .HasColumnName("trust");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_name");

                    b.HasKey("Id")
                        .HasName("PK_connection_log");

                    b.HasIndex("ServerId")
                        .HasDatabaseName("IX_connection_log_server_id");

                    b.HasIndex("Time");

                    b.HasIndex("UserId");

                    b.ToTable("connection_log", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.IPIntelCache", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("ipintel_cache_id");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("address");

                    b.Property<float>("Score")
                        .HasColumnType("REAL")
                        .HasColumnName("score");

                    b.Property<DateTime>("Time")
                        .HasColumnType("TEXT")
                        .HasColumnName("time");

                    b.HasKey("Id")
                        .HasName("PK_ipintel_cache");

                    b.HasIndex("Address")
                        .IsUnique();

                    b.ToTable("ipintel_cache", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Job", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("job_id");

                    b.Property<string>("JobName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("job_name");

                    b.Property<int>("Priority")
                        .HasColumnType("INTEGER")
                        .HasColumnName("priority");

                    b.Property<int>("ProfileId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_id");

                    b.HasKey("Id")
                        .HasName("PK_job");

                    b.HasIndex("ProfileId");

                    b.HasIndex("ProfileId", "JobName")
                        .IsUnique();

                    b.HasIndex(new[] { "ProfileId" }, "IX_job_one_high_priority")
                        .IsUnique()
                        .HasFilter("priority = 3");

                    b.ToTable("job", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.PlayTime", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("play_time_id");

                    b.Property<Guid>("PlayerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_id");

                    b.Property<TimeSpan>("TimeSpent")
                        .HasColumnType("TEXT")
                        .HasColumnName("time_spent");

                    b.Property<string>("Tracker")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("tracker");

                    b.HasKey("Id")
                        .HasName("PK_play_time");

                    b.HasIndex("PlayerId", "Tracker")
                        .IsUnique();

                    b.ToTable("play_time", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("player_id");

                    b.Property<DateTime>("FirstSeenTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("first_seen_time");

                    b.Property<DateTime?>("LastReadRules")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_read_rules");

                    b.Property<TimeSpan?>("LastRolledAntag")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_rolled_antag");

                    b.Property<string>("LastSeenAddress")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("last_seen_address");

                    b.Property<DateTime>("LastSeenTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_seen_time");

                    b.Property<string>("LastSeenUserName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("last_seen_user_name");

                    b.Property<int>("ServerCurrency")
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_currency");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_player");

                    b.HasAlternateKey("UserId")
                        .HasName("ak_player_user_id");

                    b.HasIndex("LastSeenUserName");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("player", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Preference", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("preference_id");

                    b.Property<string>("AdminOOCColor")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("admin_ooc_color");

                    b.Property<int>("SelectedCharacterSlot")
                        .HasColumnType("INTEGER")
                        .HasColumnName("selected_character_slot");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_preference");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("preference", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_id");

                    b.Property<int>("Age")
                        .HasColumnType("INTEGER")
                        .HasColumnName("age");

                    b.Property<string>("CharacterName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("char_name");

                    b.Property<string>("EyeColor")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("eye_color");

                    b.Property<string>("FacialHairColor")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("facial_hair_color");

                    b.Property<string>("FacialHairName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("facial_hair_name");

                    b.Property<string>("FlavorText")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("flavor_text");

                    b.Property<string>("Gender")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("gender");

                    b.Property<string>("HairColor")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("hair_color");

                    b.Property<string>("HairName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("hair_name");

                    b.Property<byte[]>("Markings")
                        .HasColumnType("jsonb")
                        .HasColumnName("markings");

                    b.Property<int>("PreferenceId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("preference_id");

                    b.Property<int>("PreferenceUnavailable")
                        .HasColumnType("INTEGER")
                        .HasColumnName("pref_unavailable");

                    b.Property<string>("Sex")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("sex");

                    b.Property<string>("SkinColor")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("skin_color");

                    b.Property<int>("Slot")
                        .HasColumnType("INTEGER")
                        .HasColumnName("slot");

                    b.Property<int>("SpawnPriority")
                        .HasColumnType("INTEGER")
                        .HasColumnName("spawn_priority");

                    b.Property<string>("Species")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("species");

                    b.HasKey("Id")
                        .HasName("PK_profile");

                    b.HasIndex("PreferenceId")
                        .HasDatabaseName("IX_profile_preference_id");

                    b.HasIndex("Slot", "PreferenceId")
                        .IsUnique();

                    b.ToTable("profile", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ProfileLoadout", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_loadout_id");

                    b.Property<string>("LoadoutName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("loadout_name");

                    b.Property<int>("ProfileLoadoutGroupId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_loadout_group_id");

                    b.HasKey("Id")
                        .HasName("PK_profile_loadout");

                    b.HasIndex("ProfileLoadoutGroupId");

                    b.ToTable("profile_loadout", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ProfileLoadoutGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_loadout_group_id");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("group_name");

                    b.Property<int>("ProfileRoleLoadoutId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_role_loadout_id");

                    b.HasKey("Id")
                        .HasName("PK_profile_loadout_group");

                    b.HasIndex("ProfileRoleLoadoutId");

                    b.ToTable("profile_loadout_group", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ProfileRoleLoadout", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_role_loadout_id");

                    b.Property<string>("EntityName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT")
                        .HasColumnName("entity_name");

                    b.Property<int>("ProfileId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_id");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("role_name");

                    b.HasKey("Id")
                        .HasName("PK_profile_role_loadout");

                    b.HasIndex("ProfileId");

                    b.ToTable("profile_role_loadout", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCDiscordAccount", b =>
                {
                    b.Property<ulong>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("rmc_discord_accounts_id");

                    b.HasKey("Id")
                        .HasName("PK_rmc_discord_accounts");

                    b.ToTable("rmc_discord_accounts", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkedAccount", b =>
                {
                    b.Property<Guid>("PlayerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_id");

                    b.Property<ulong>("DiscordId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("discord_id");

                    b.HasKey("PlayerId")
                        .HasName("PK_rmc_linked_accounts");

                    b.HasIndex("DiscordId")
                        .IsUnique();

                    b.ToTable("rmc_linked_accounts", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkedAccountLogs", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("rmc_linked_accounts_logs_id");

                    b.Property<DateTime>("At")
                        .HasColumnType("TEXT")
                        .HasColumnName("at");

                    b.Property<ulong>("DiscordId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("discord_id");

                    b.Property<Guid>("PlayerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_id");

                    b.HasKey("Id")
                        .HasName("PK_rmc_linked_accounts_logs");

                    b.HasIndex("At")
                        .HasDatabaseName("IX_rmc_linked_accounts_logs_at");

                    b.HasIndex("DiscordId")
                        .HasDatabaseName("IX_rmc_linked_accounts_logs_discord_id");

                    b.HasIndex("PlayerId")
                        .HasDatabaseName("IX_rmc_linked_accounts_logs_player_id");

                    b.ToTable("rmc_linked_accounts_logs", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkingCodes", b =>
                {
                    b.Property<Guid>("PlayerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_id");

                    b.Property<Guid>("Code")
                        .HasColumnType("TEXT")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("creation_time");

                    b.HasKey("PlayerId")
                        .HasName("PK_rmc_linking_codes");

                    b.HasIndex("Code")
                        .HasDatabaseName("IX_rmc_linking_codes_code");

                    b.ToTable("rmc_linking_codes", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatron", b =>
                {
                    b.Property<Guid>("PlayerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_id");

                    b.Property<int?>("GhostColor")
                        .HasColumnType("INTEGER")
                        .HasColumnName("ghost_color");

                    b.Property<int>("TierId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("tier_id");

                    b.HasKey("PlayerId")
                        .HasName("PK_rmc_patrons");

                    b.HasIndex("TierId")
                        .HasDatabaseName("IX_rmc_patrons_tier_id");

                    b.ToTable("rmc_patrons", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronLobbyMessage", b =>
                {
                    b.Property<Guid>("PatronId")
                        .HasColumnType("TEXT")
                        .HasColumnName("patron_id");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT")
                        .HasColumnName("message");

                    b.HasKey("PatronId")
                        .HasName("PK_rmc_patron_lobby_messages");

                    b.ToTable("rmc_patron_lobby_messages", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronRoundEndNTShoutout", b =>
                {
                    b.Property<Guid>("PatronId")
                        .HasColumnType("TEXT")
                        .HasColumnName("patron_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("name");

                    b.HasKey("PatronId")
                        .HasName("PK_rmc_patron_round_end_nt_shoutouts");

                    b.ToTable("rmc_patron_round_end_nt_shoutouts", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronTier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("rmc_patron_tiers_id");

                    b.Property<ulong>("DiscordRole")
                        .HasColumnType("INTEGER")
                        .HasColumnName("discord_role");

                    b.Property<bool>("GhostColor")
                        .HasColumnType("INTEGER")
                        .HasColumnName("ghost_color");

                    b.Property<bool>("LobbyMessage")
                        .HasColumnType("INTEGER")
                        .HasColumnName("lobby_message");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("name");

                    b.Property<int>("Priority")
                        .HasColumnType("INTEGER")
                        .HasColumnName("priority");

                    b.Property<bool>("RoundEndShoutout")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_end_shoutout");

                    b.Property<bool>("ShowOnCredits")
                        .HasColumnType("INTEGER")
                        .HasColumnName("show_on_credits");

                    b.HasKey("Id")
                        .HasName("PK_rmc_patron_tiers");

                    b.HasIndex("DiscordRole")
                        .IsUnique();

                    b.ToTable("rmc_patron_tiers", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.RoleWhitelist", b =>
                {
                    b.Property<Guid>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT")
                        .HasColumnName("role_id");

                    b.HasKey("PlayerUserId", "RoleId")
                        .HasName("PK_role_whitelists");

                    b.ToTable("role_whitelists", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<int>("ServerId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_id");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("TEXT")
                        .HasColumnName("start_date");

                    b.HasKey("Id")
                        .HasName("PK_round");

                    b.HasIndex("ServerId")
                        .HasDatabaseName("IX_round_server_id");

                    b.HasIndex("StartDate");

                    b.ToTable("round", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Server", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("name");

                    b.HasKey("Id")
                        .HasName("PK_server");

                    b.ToTable("server", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_ban_id");

                    b.Property<string>("Address")
                        .HasColumnType("TEXT")
                        .HasColumnName("address");

                    b.Property<bool>("AutoDelete")
                        .HasColumnType("INTEGER")
                        .HasColumnName("auto_delete");

                    b.Property<DateTime>("BanTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("ban_time");

                    b.Property<Guid?>("BanningAdmin")
                        .HasColumnType("TEXT")
                        .HasColumnName("banning_admin");

                    b.Property<int>("ExemptFlags")
                        .HasColumnType("INTEGER")
                        .HasColumnName("exempt_flags");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("expiration_time");

                    b.Property<bool>("Hidden")
                        .HasColumnType("INTEGER")
                        .HasColumnName("hidden");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_by_id");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("TEXT")
                        .HasColumnName("playtime_at_note");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("reason");

                    b.Property<int?>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<int>("Severity")
                        .HasColumnType("INTEGER")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_server_ban");

                    b.HasIndex("Address");

                    b.HasIndex("BanningAdmin");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_server_ban_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_server_ban_round_id");

                    b.ToTable("server_ban", null, t =>
                        {
                            t.HasCheckConstraint("HaveEitherAddressOrUserIdOrHWId", "address IS NOT NULL OR player_user_id IS NOT NULL OR hwid IS NOT NULL");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanExemption", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.Property<int>("Flags")
                        .HasColumnType("INTEGER")
                        .HasColumnName("flags");

                    b.HasKey("UserId")
                        .HasName("PK_server_ban_exemption");

                    b.ToTable("server_ban_exemption", null, t =>
                        {
                            t.HasCheckConstraint("FlagsNotZero", "flags != 0");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanHit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_ban_hit_id");

                    b.Property<int>("BanId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("ban_id");

                    b.Property<int>("ConnectionId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("connection_id");

                    b.HasKey("Id")
                        .HasName("PK_server_ban_hit");

                    b.HasIndex("BanId")
                        .HasDatabaseName("IX_server_ban_hit_ban_id");

                    b.HasIndex("ConnectionId")
                        .HasDatabaseName("IX_server_ban_hit_connection_id");

                    b.ToTable("server_ban_hit", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("server_role_ban_id");

                    b.Property<string>("Address")
                        .HasColumnType("TEXT")
                        .HasColumnName("address");

                    b.Property<DateTime>("BanTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("ban_time");

                    b.Property<Guid?>("BanningAdmin")
                        .HasColumnType("TEXT")
                        .HasColumnName("banning_admin");

                    b.Property<DateTime?>("ExpirationTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("expiration_time");

                    b.Property<bool>("Hidden")
                        .HasColumnType("INTEGER")
                        .HasColumnName("hidden");

                    b.Property<DateTime?>("LastEditedAt")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_at");

                    b.Property<Guid?>("LastEditedById")
                        .HasColumnType("TEXT")
                        .HasColumnName("last_edited_by_id");

                    b.Property<Guid?>("PlayerUserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("player_user_id");

                    b.Property<TimeSpan>("PlaytimeAtNote")
                        .HasColumnType("TEXT")
                        .HasColumnName("playtime_at_note");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("reason");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("role_id");

                    b.Property<int?>("RoundId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("round_id");

                    b.Property<int>("Severity")
                        .HasColumnType("INTEGER")
                        .HasColumnName("severity");

                    b.HasKey("Id")
                        .HasName("PK_server_role_ban");

                    b.HasIndex("Address");

                    b.HasIndex("BanningAdmin");

                    b.HasIndex("LastEditedById");

                    b.HasIndex("PlayerUserId")
                        .HasDatabaseName("IX_server_role_ban_player_user_id");

                    b.HasIndex("RoundId")
                        .HasDatabaseName("IX_server_role_ban_round_id");

                    b.ToTable("server_role_ban", null, t =>
                        {
                            t.HasCheckConstraint("HaveEitherAddressOrUserIdOrHWId", "address IS NOT NULL OR player_user_id IS NOT NULL OR hwid IS NOT NULL");
                        });
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleUnban", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("role_unban_id");

                    b.Property<int>("BanId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("ban_id");

                    b.Property<DateTime>("UnbanTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("unban_time");

                    b.Property<Guid?>("UnbanningAdmin")
                        .HasColumnType("TEXT")
                        .HasColumnName("unbanning_admin");

                    b.HasKey("Id")
                        .HasName("PK_server_role_unban");

                    b.HasIndex("BanId")
                        .IsUnique();

                    b.ToTable("server_role_unban", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.ServerUnban", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("unban_id");

                    b.Property<int>("BanId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("ban_id");

                    b.Property<DateTime>("UnbanTime")
                        .HasColumnType("TEXT")
                        .HasColumnName("unban_time");

                    b.Property<Guid?>("UnbanningAdmin")
                        .HasColumnType("TEXT")
                        .HasColumnName("unbanning_admin");

                    b.HasKey("Id")
                        .HasName("PK_server_unban");

                    b.HasIndex("BanId")
                        .IsUnique();

                    b.ToTable("server_unban", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Trait", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("trait_id");

                    b.Property<int>("ProfileId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("profile_id");

                    b.Property<string>("TraitName")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("trait_name");

                    b.HasKey("Id")
                        .HasName("PK_trait");

                    b.HasIndex("ProfileId", "TraitName")
                        .IsUnique();

                    b.ToTable("trait", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.UploadedResourceLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER")
                        .HasColumnName("uploaded_resource_log_id");

                    b.Property<byte[]>("Data")
                        .IsRequired()
                        .HasColumnType("BLOB")
                        .HasColumnName("data");

                    b.Property<DateTime>("Date")
                        .HasColumnType("TEXT")
                        .HasColumnName("date");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("path");

                    b.Property<Guid>("UserId")
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("PK_uploaded_resource_log");

                    b.ToTable("uploaded_resource_log", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Whitelist", b =>
                {
                    b.Property<Guid>("UserId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasColumnName("user_id");

                    b.HasKey("UserId")
                        .HasName("PK_whitelist");

                    b.ToTable("whitelist", (string)null);
                });

            modelBuilder.Entity("PlayerRound", b =>
                {
                    b.Property<int>("PlayersId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("players_id");

                    b.Property<int>("RoundsId")
                        .HasColumnType("INTEGER")
                        .HasColumnName("rounds_id");

                    b.HasKey("PlayersId", "RoundsId")
                        .HasName("PK_player_round");

                    b.HasIndex("RoundsId")
                        .HasDatabaseName("IX_player_round_rounds_id");

                    b.ToTable("player_round", (string)null);
                });

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.HasOne("Content.Server.Database.AdminRank", "AdminRank")
                        .WithMany("Admins")
                        .HasForeignKey("AdminRankId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_admin_rank_admin_rank_id");

                    b.Navigation("AdminRank");
                });

            modelBuilder.Entity("Content.Server.Database.AdminFlag", b =>
                {
                    b.HasOne("Content.Server.Database.Admin", "Admin")
                        .WithMany("Flags")
                        .HasForeignKey("AdminId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_flag_admin_admin_id");

                    b.Navigation("Admin");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany("AdminLogs")
                        .HasForeignKey("RoundId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_round_round_id");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLogPlayer", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminLogs")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_player_player_player_user_id");

                    b.HasOne("Content.Server.Database.AdminLog", "Log")
                        .WithMany("Players")
                        .HasForeignKey("RoundId", "LogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_log_player_admin_log_round_id_log_id");

                    b.Navigation("Log");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.AdminMessage", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminMessagesCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminMessagesDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminMessagesLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_messages_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminMessagesReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_messages_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_messages_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminNote", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminNotesCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminNotesDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminNotesLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_notes_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminNotesReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_notes_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_notes_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.AdminRankFlag", b =>
                {
                    b.HasOne("Content.Server.Database.AdminRank", "Rank")
                        .WithMany("Flags")
                        .HasForeignKey("AdminRankId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_admin_rank_flag_admin_rank_admin_rank_id");

                    b.Navigation("Rank");
                });

            modelBuilder.Entity("Content.Server.Database.AdminWatchlist", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminWatchlistsCreated")
                        .HasForeignKey("CreatedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_created_by_id");

                    b.HasOne("Content.Server.Database.Player", "DeletedBy")
                        .WithMany("AdminWatchlistsDeleted")
                        .HasForeignKey("DeletedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_deleted_by_id");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminWatchlistsLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_admin_watchlists_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("AdminWatchlistsReceived")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .HasConstraintName("FK_admin_watchlists_player_player_user_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_admin_watchlists_round_round_id");

                    b.Navigation("CreatedBy");

                    b.Navigation("DeletedBy");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Player");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.Antag", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Antags")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_antag_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.HasOne("Content.Server.Database.Server", "Server")
                        .WithMany("ConnectionLogs")
                        .HasForeignKey("ServerId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired()
                        .HasConstraintName("FK_connection_log_server_server_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ConnectionLogId")
                                .HasColumnType("INTEGER")
                                .HasColumnName("connection_log_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("BLOB")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ConnectionLogId");

                            b1.ToTable("connection_log");

                            b1.WithOwner()
                                .HasForeignKey("ConnectionLogId")
                                .HasConstraintName("FK_connection_log_connection_log_connection_log_id");
                        });

                    b.Navigation("HWId");

                    b.Navigation("Server");
                });

            modelBuilder.Entity("Content.Server.Database.Job", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Jobs")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_job_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.OwnsOne("Content.Server.Database.TypedHwid", "LastSeenHWId", b1 =>
                        {
                            b1.Property<int>("PlayerId")
                                .HasColumnType("INTEGER")
                                .HasColumnName("player_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("BLOB")
                                .HasColumnName("last_seen_hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER")
                                .HasDefaultValue(0)
                                .HasColumnName("last_seen_hwid_type");

                            b1.HasKey("PlayerId");

                            b1.ToTable("player");

                            b1.WithOwner()
                                .HasForeignKey("PlayerId")
                                .HasConstraintName("FK_player_player_player_id");
                        });

                    b.Navigation("LastSeenHWId");
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.HasOne("Content.Server.Database.Preference", "Preference")
                        .WithMany("Profiles")
                        .HasForeignKey("PreferenceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_profile_preference_preference_id");

                    b.Navigation("Preference");
                });

            modelBuilder.Entity("Content.Server.Database.ProfileLoadout", b =>
                {
                    b.HasOne("Content.Server.Database.ProfileLoadoutGroup", "ProfileLoadoutGroup")
                        .WithMany("Loadouts")
                        .HasForeignKey("ProfileLoadoutGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_profile_loadout_profile_loadout_group_profile_loadout_group_id");

                    b.Navigation("ProfileLoadoutGroup");
                });

            modelBuilder.Entity("Content.Server.Database.ProfileLoadoutGroup", b =>
                {
                    b.HasOne("Content.Server.Database.ProfileRoleLoadout", "ProfileRoleLoadout")
                        .WithMany("Groups")
                        .HasForeignKey("ProfileRoleLoadoutId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_profile_loadout_group_profile_role_loadout_profile_role_loadout_id");

                    b.Navigation("ProfileRoleLoadout");
                });

            modelBuilder.Entity("Content.Server.Database.ProfileRoleLoadout", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Loadouts")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_profile_role_loadout_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkedAccount", b =>
                {
                    b.HasOne("Content.Server.Database.RMCDiscordAccount", "Discord")
                        .WithOne("LinkedAccount")
                        .HasForeignKey("Content.Server.Database.RMCLinkedAccount", "DiscordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_linked_accounts_rmc_discord_accounts_discord_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithOne("LinkedAccount")
                        .HasForeignKey("Content.Server.Database.RMCLinkedAccount", "PlayerId")
                        .HasPrincipalKey("Content.Server.Database.Player", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_linked_accounts_player_player_id");

                    b.Navigation("Discord");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkedAccountLogs", b =>
                {
                    b.HasOne("Content.Server.Database.RMCDiscordAccount", "Discord")
                        .WithMany("LinkedAccountLogs")
                        .HasForeignKey("DiscordId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_linked_accounts_logs_rmc_discord_accounts_discord_id");

                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("LinkedAccountLogs")
                        .HasForeignKey("PlayerId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_linked_accounts_logs_player_player_id1");

                    b.Navigation("Discord");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.RMCLinkingCodes", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithOne("LinkingCodes")
                        .HasForeignKey("Content.Server.Database.RMCLinkingCodes", "PlayerId")
                        .HasPrincipalKey("Content.Server.Database.Player", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_linking_codes_player_player_id");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatron", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithOne("Patron")
                        .HasForeignKey("Content.Server.Database.RMCPatron", "PlayerId")
                        .HasPrincipalKey("Content.Server.Database.Player", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_patrons_player_player_id");

                    b.HasOne("Content.Server.Database.RMCPatronTier", "Tier")
                        .WithMany("Patrons")
                        .HasForeignKey("TierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_patrons_rmc_patron_tiers_tier_id");

                    b.Navigation("Player");

                    b.Navigation("Tier");
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronLobbyMessage", b =>
                {
                    b.HasOne("Content.Server.Database.RMCPatron", "Patron")
                        .WithOne("LobbyMessage")
                        .HasForeignKey("Content.Server.Database.RMCPatronLobbyMessage", "PatronId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_patron_lobby_messages_rmc_patrons_patron_id");

                    b.Navigation("Patron");
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronRoundEndNTShoutout", b =>
                {
                    b.HasOne("Content.Server.Database.RMCPatron", "Patron")
                        .WithOne("RoundEndNTShoutout")
                        .HasForeignKey("Content.Server.Database.RMCPatronRoundEndNTShoutout", "PatronId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_rmc_patron_round_end_nt_shoutouts_rmc_patrons_patron_id");

                    b.Navigation("Patron");
                });

            modelBuilder.Entity("Content.Server.Database.RoleWhitelist", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "Player")
                        .WithMany("JobWhitelists")
                        .HasForeignKey("PlayerUserId")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_role_whitelists_player_player_user_id");

                    b.Navigation("Player");
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.HasOne("Content.Server.Database.Server", "Server")
                        .WithMany("Rounds")
                        .HasForeignKey("ServerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_round_server_server_id");

                    b.Navigation("Server");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminServerBansCreated")
                        .HasForeignKey("BanningAdmin")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_ban_player_banning_admin");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminServerBansLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_ban_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_server_ban_round_round_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ServerBanId")
                                .HasColumnType("INTEGER")
                                .HasColumnName("server_ban_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("BLOB")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ServerBanId");

                            b1.ToTable("server_ban");

                            b1.WithOwner()
                                .HasForeignKey("ServerBanId")
                                .HasConstraintName("FK_server_ban_server_ban_server_ban_id");
                        });

                    b.Navigation("CreatedBy");

                    b.Navigation("HWId");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBanHit", b =>
                {
                    b.HasOne("Content.Server.Database.ServerBan", "Ban")
                        .WithMany("BanHits")
                        .HasForeignKey("BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_ban_hit_server_ban_ban_id");

                    b.HasOne("Content.Server.Database.ConnectionLog", "Connection")
                        .WithMany("BanHits")
                        .HasForeignKey("ConnectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_ban_hit_connection_log_connection_id");

                    b.Navigation("Ban");

                    b.Navigation("Connection");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.HasOne("Content.Server.Database.Player", "CreatedBy")
                        .WithMany("AdminServerRoleBansCreated")
                        .HasForeignKey("BanningAdmin")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_role_ban_player_banning_admin");

                    b.HasOne("Content.Server.Database.Player", "LastEditedBy")
                        .WithMany("AdminServerRoleBansLastEdited")
                        .HasForeignKey("LastEditedById")
                        .HasPrincipalKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .HasConstraintName("FK_server_role_ban_player_last_edited_by_id");

                    b.HasOne("Content.Server.Database.Round", "Round")
                        .WithMany()
                        .HasForeignKey("RoundId")
                        .HasConstraintName("FK_server_role_ban_round_round_id");

                    b.OwnsOne("Content.Server.Database.TypedHwid", "HWId", b1 =>
                        {
                            b1.Property<int>("ServerRoleBanId")
                                .HasColumnType("INTEGER")
                                .HasColumnName("server_role_ban_id");

                            b1.Property<byte[]>("Hwid")
                                .IsRequired()
                                .HasColumnType("BLOB")
                                .HasColumnName("hwid");

                            b1.Property<int>("Type")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("INTEGER")
                                .HasDefaultValue(0)
                                .HasColumnName("hwid_type");

                            b1.HasKey("ServerRoleBanId");

                            b1.ToTable("server_role_ban");

                            b1.WithOwner()
                                .HasForeignKey("ServerRoleBanId")
                                .HasConstraintName("FK_server_role_ban_server_role_ban_server_role_ban_id");
                        });

                    b.Navigation("CreatedBy");

                    b.Navigation("HWId");

                    b.Navigation("LastEditedBy");

                    b.Navigation("Round");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleUnban", b =>
                {
                    b.HasOne("Content.Server.Database.ServerRoleBan", "Ban")
                        .WithOne("Unban")
                        .HasForeignKey("Content.Server.Database.ServerRoleUnban", "BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_role_unban_server_role_ban_ban_id");

                    b.Navigation("Ban");
                });

            modelBuilder.Entity("Content.Server.Database.ServerUnban", b =>
                {
                    b.HasOne("Content.Server.Database.ServerBan", "Ban")
                        .WithOne("Unban")
                        .HasForeignKey("Content.Server.Database.ServerUnban", "BanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_server_unban_server_ban_ban_id");

                    b.Navigation("Ban");
                });

            modelBuilder.Entity("Content.Server.Database.Trait", b =>
                {
                    b.HasOne("Content.Server.Database.Profile", "Profile")
                        .WithMany("Traits")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_trait_profile_profile_id");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("PlayerRound", b =>
                {
                    b.HasOne("Content.Server.Database.Player", null)
                        .WithMany()
                        .HasForeignKey("PlayersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_player_round_player_players_id");

                    b.HasOne("Content.Server.Database.Round", null)
                        .WithMany()
                        .HasForeignKey("RoundsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_player_round_round_rounds_id");
                });

            modelBuilder.Entity("Content.Server.Database.Admin", b =>
                {
                    b.Navigation("Flags");
                });

            modelBuilder.Entity("Content.Server.Database.AdminLog", b =>
                {
                    b.Navigation("Players");
                });

            modelBuilder.Entity("Content.Server.Database.AdminRank", b =>
                {
                    b.Navigation("Admins");

                    b.Navigation("Flags");
                });

            modelBuilder.Entity("Content.Server.Database.ConnectionLog", b =>
                {
                    b.Navigation("BanHits");
                });

            modelBuilder.Entity("Content.Server.Database.Player", b =>
                {
                    b.Navigation("AdminLogs");

                    b.Navigation("AdminMessagesCreated");

                    b.Navigation("AdminMessagesDeleted");

                    b.Navigation("AdminMessagesLastEdited");

                    b.Navigation("AdminMessagesReceived");

                    b.Navigation("AdminNotesCreated");

                    b.Navigation("AdminNotesDeleted");

                    b.Navigation("AdminNotesLastEdited");

                    b.Navigation("AdminNotesReceived");

                    b.Navigation("AdminServerBansCreated");

                    b.Navigation("AdminServerBansLastEdited");

                    b.Navigation("AdminServerRoleBansCreated");

                    b.Navigation("AdminServerRoleBansLastEdited");

                    b.Navigation("AdminWatchlistsCreated");

                    b.Navigation("AdminWatchlistsDeleted");

                    b.Navigation("AdminWatchlistsLastEdited");

                    b.Navigation("AdminWatchlistsReceived");

                    b.Navigation("JobWhitelists");

                    b.Navigation("LinkedAccount");

                    b.Navigation("LinkedAccountLogs");

                    b.Navigation("LinkingCodes");

                    b.Navigation("Patron");
                });

            modelBuilder.Entity("Content.Server.Database.Preference", b =>
                {
                    b.Navigation("Profiles");
                });

            modelBuilder.Entity("Content.Server.Database.Profile", b =>
                {
                    b.Navigation("Antags");

                    b.Navigation("Jobs");

                    b.Navigation("Loadouts");

                    b.Navigation("Traits");
                });

            modelBuilder.Entity("Content.Server.Database.ProfileLoadoutGroup", b =>
                {
                    b.Navigation("Loadouts");
                });

            modelBuilder.Entity("Content.Server.Database.ProfileRoleLoadout", b =>
                {
                    b.Navigation("Groups");
                });

            modelBuilder.Entity("Content.Server.Database.RMCDiscordAccount", b =>
                {
                    b.Navigation("LinkedAccount")
                        .IsRequired();

                    b.Navigation("LinkedAccountLogs");
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatron", b =>
                {
                    b.Navigation("LobbyMessage");

                    b.Navigation("RoundEndNTShoutout");
                });

            modelBuilder.Entity("Content.Server.Database.RMCPatronTier", b =>
                {
                    b.Navigation("Patrons");
                });

            modelBuilder.Entity("Content.Server.Database.Round", b =>
                {
                    b.Navigation("AdminLogs");
                });

            modelBuilder.Entity("Content.Server.Database.Server", b =>
                {
                    b.Navigation("ConnectionLogs");

                    b.Navigation("Rounds");
                });

            modelBuilder.Entity("Content.Server.Database.ServerBan", b =>
                {
                    b.Navigation("BanHits");

                    b.Navigation("Unban");
                });

            modelBuilder.Entity("Content.Server.Database.ServerRoleBan", b =>
                {
                    b.Navigation("Unban");
                });
#pragma warning restore 612, 618
        }
    }
}
