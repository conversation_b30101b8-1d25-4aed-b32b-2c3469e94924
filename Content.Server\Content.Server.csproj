<!--
SPDX-FileCopyrightText: 2018 Acruid <<EMAIL>>
SPDX-FileCopyrightText: 2018 Centronias <<EMAIL>>
SPDX-FileCopyrightText: 2018 PJB3005 <<EMAIL>>
SPDX-FileCopyrightText: 2018 Víctor <PERSON>a Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2018 clusterfack <<EMAIL>>
SPDX-FileCopyrightText: 2018 clusterfack <<EMAIL>>
SPDX-FileCopyrightText: 2019 Injazz <<EMAIL>>
SPDX-FileCopyrightText: 2019 Pieter-Jan Briers <<EMAIL>>
SPDX-FileCopyrightText: 2019 PrPleGoo <<EMAIL>>
SPDX-FileCopyrightText: 2019 ScumbagDog <<EMAIL>>
SPDX-FileCopyrightText: 2019 Silver <<EMAIL>>
SPDX-FileCopyrightText: 2020 DamianX <<EMAIL>>
SPDX-FileCopyrightText: 2020 Paul <<EMAIL>>
SPDX-FileCopyrightText: 2020 Tyler Young <<EMAIL>>
SPDX-FileCopyrightText: 2020 Víctor Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2020 Víctor Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2021 Javier Guardia Fernández <<EMAIL>>
SPDX-FileCopyrightText: 2021 Paul Ritter <<EMAIL>>
SPDX-FileCopyrightText: 2022 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
SPDX-FileCopyrightText: 2023 Chief-Engineer <<EMAIL>>
SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
SPDX-FileCopyrightText: 2023 Riggle <<EMAIL>>
SPDX-FileCopyrightText: 2023 Vera Aguilera Puerto <<EMAIL>>
SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
SPDX-FileCopyrightText: 2024 Julian Giebel <<EMAIL>>
SPDX-FileCopyrightText: 2024 Mervill <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-Jan Briers <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
SPDX-FileCopyrightText: 2025 gus <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <!-- Work around https://github.com/dotnet/project-system/issues/4314 -->
    <TargetFramework>$(TargetFramework)</TargetFramework>
    <LangVersion>12</LangVersion>
    <IsPackable>false</IsPackable>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputPath>..\bin\Content.Server\</OutputPath>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    <OutputType Condition="'$(FullRelease)' != 'True'">Exe</OutputType>
    <NoWarn>1998</NoWarn>
    <WarningsAsErrors>nullable</WarningsAsErrors>
    <Nullable>enable</Nullable>
    <ServerGarbageCollection>true</ServerGarbageCollection>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="JetBrains.Annotations" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Content.Packaging\Content.Packaging.csproj" />
    <ProjectReference Include="..\Content.Server.Database\Content.Server.Database.csproj" />
    <ProjectReference Include="..\Content.Shared.Database\Content.Shared.Database.csproj" />
    <ProjectReference Include="..\RobustToolbox\Lidgren.Network\Lidgren.Network.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared.Maths\Robust.Shared.Maths.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Shared\Robust.Shared.csproj" />
    <ProjectReference Include="..\RobustToolbox\Robust.Server\Robust.Server.csproj" />
    <ProjectReference Include="..\Content.Shared\Content.Shared.csproj" />
    <ProjectReference Include="..\Content.Goobstation.Shared\Content.Goobstation.Shared.csproj" />
    <ProjectReference Include="..\Content.Goobstation.Server\Content.Goobstation.Server.csproj" />
    <ProjectReference Include="..\Content.Pirate.Shared\Content.Pirate.Shared.csproj" />
  </ItemGroup>
  <Import Project="..\RobustToolbox\MSBuild\Robust.Properties.targets" />
</Project>
