// SPDX-FileCopyrightText: 2020 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Acruid <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Galactic Chimp <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Galactic Chimp <<EMAIL>>
// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Metal Gear Sloth <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2021 metalgearsloth <metalge<PERSON>@gmail.com>
// SPDX-FileCopyrightText: 2022 Julian <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Leon Friedrich <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Paul Ritter <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Pieter-Jan Briers <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Rane <<EMAIL>>
// SPDX-FileCopyrightText: 2022 keronshb <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 TemporalOroboros <<EMAIL>>
// SPDX-FileCopyrightText: 2023 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2023 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Piras314 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Shared.Audio;

namespace Content.Server.Disposal.Tube
{
    [RegisterComponent]
    [Access(typeof(DisposalTubeSystem))]
    public sealed partial class DisposalRouterComponent : DisposalJunctionComponent
    {
        [DataField("tags")]
        public HashSet<string> Tags = new();

        [DataField("clickSound")]
        public SoundSpecifier ClickSound = new SoundPathSpecifier("/Audio/Machines/machine_switch.ogg");
    }
}