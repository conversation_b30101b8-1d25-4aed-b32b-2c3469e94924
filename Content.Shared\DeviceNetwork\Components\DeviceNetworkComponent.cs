// SPDX-FileCopyrightText: 2021 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2021 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Flipp <PERSON>yder <<EMAIL>>
// SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Vera Aguilera Puerto <<EMAIL>>
// SPDX-FileCopyrightText: 2022 mirrorcult <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2023 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2023 <PERSON> <juliangie<PERSON>@live.de>
// SPDX-FileCopyrightText: 2023 <PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2024 <PERSON>na <<EMAIL>>
// SPDX-FileCopyrightText: 2024 Zachary Yona <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <<EMAIL>>
// SPDX-FileCopyrightText: 2025 BombasterDS <<EMAIL>>
// SPDX-FileCopyrightText: 2025 BombasterDS2 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Misandry <<EMAIL>>
// SPDX-FileCopyrightText: 2025 PJB3005 <<EMAIL>>
// SPDX-FileCopyrightText: 2025 gus <<EMAIL>>
//
// SPDX-License-Identifier: AGPL-3.0-or-later

using Robust.Shared.Serialization.TypeSerializers.Implementations.Custom.Prototype;

namespace Content.Shared.DeviceNetwork.Components
{
    [RegisterComponent]
    //Goobstation - sync device lists / modules had to nuke explicit access
    public sealed partial class DeviceNetworkComponent : Component
    {
        public enum DeviceNetIdDefaults
        {
            Private,
            Wired,
            Wireless,
            Apc,
            AtmosDevices,
            Reserved = 100,
            // Ids outside this enum may exist
            // This exists to let yml use nice names instead of numbers
        }

        [DataField("deviceNetId")]
        public DeviceNetIdDefaults NetIdEnum { get; set; }

        public int DeviceNetId => (int) NetIdEnum;

        /// <summary>
        ///     The frequency that this device is listening on.
        /// </summary>
        [DataField("receiveFrequency")]
        public uint? ReceiveFrequency;

        /// <summary>
        ///     frequency prototype. Used to select a default frequency to listen to on. Used when the map is
        ///     initialized.
        /// </summary>
        [DataField("receiveFrequencyId", customTypeSerializer: typeof(PrototypeIdSerializer<DeviceFrequencyPrototype>))]
        public string? ReceiveFrequencyId;

        /// <summary>
        ///     The frequency that this device going to try transmit on.
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        [DataField("transmitFrequency")]
        public uint? TransmitFrequency;

        /// <summary>
        ///     frequency prototype. Used to select a default frequency to transmit on. Used when the map is
        ///     initialized.
        /// </summary>
        [DataField("transmitFrequencyId", customTypeSerializer: typeof(PrototypeIdSerializer<DeviceFrequencyPrototype>))]
        public string? TransmitFrequencyId;

        /// <summary>
        ///     The address of the device, either on the network it is currently connected to or whatever address it
        ///     most recently used.
        /// </summary>
        [DataField("address")]
        public string Address = string.Empty;

        /// <summary>
        ///     If true, the address was customized and should be preserved across networks. If false, a randomly
        ///     generated address will be created whenever this device connects to a network.
        /// </summary>
        [DataField("customAddress")]
        public bool CustomAddress = false;

        /// <summary>
        ///     Prefix to prepend to any automatically generated addresses. Helps players to identify devices. This gets
        ///     localized.
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        [DataField("prefix")]
        public string? Prefix;

        /// <summary>
        ///     Whether the device should listen for all device messages, regardless of the intended recipient.
        /// </summary>
        [DataField("receiveAll")]
        public bool ReceiveAll;

        /// <summary>
        ///     If the device should show its address upon an examine. Useful for devices
        ///     that do not have a visible UI.
        /// </summary>
        [DataField("examinableAddress")]
        public bool ExaminableAddress;

        /// <summary>
        ///     Whether the device should attempt to join the network on map init.
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        [DataField("autoConnect")]
        public bool AutoConnect = true;

        /// <summary>
        ///     Whether to send the broadcast recipients list to the sender so it can be filtered.
        /// <see cref="DeviceListSystem"/>
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        [DataField("sendBroadcastAttemptEvent")]
        public bool SendBroadcastAttemptEvent = false;

        /// <summary>
        ///     Whether this device's address can be saved to device-lists
        /// </summary>
        [ViewVariables(VVAccess.ReadWrite)]
        [DataField("savableAddress")]
        public bool SavableAddress = true;

        /// <summary>
        ///     A list of device-lists that this device is on.
        /// </summary>
        [DataField]
        // had to nuke explicit access for goob modules
        public HashSet<EntityUid> DeviceLists = new();

        /// <summary>
        ///     A list of configurators that this device is on.
        /// </summary>
        [DataField]
        public HashSet<EntityUid> Configurators = new();
    }
}
