// SPDX-FileCopyrightText: 2020 <PERSON>-<PERSON> <<EMAIL>>
// SPDX-FileCopyrightText: 2020 <PERSON>-<PERSON>s <<EMAIL>>
// SPDX-FileCopyrightText: 2021 DrSmugleaf <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Metal Gear Sloth <<EMAIL>>
// SPDX-FileCopyrightText: 2021 Visne <<EMAIL>>
// SPDX-FileCopyrightText: 2021 metalgearsloth <<EMAIL>>
// SPDX-FileCopyrightText: 2022 Jack Fox <<EMAIL>>
// SPDX-FileCopyrightText: 2022 wrexbe <<EMAIL>>
// SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
//
// SPDX-License-Identifier: MIT

using Robust.Shared.Serialization;

namespace Content.Shared.Disposal.Components
{
    [Serializable, NetSerializable]
    public enum DisposalTubeVisuals
    {
        VisualState
    }

    [Serializable, NetSerializable]
    public enum DisposalTubeVisualState
    {
        Free = 0,
        Anchored,
    }
}