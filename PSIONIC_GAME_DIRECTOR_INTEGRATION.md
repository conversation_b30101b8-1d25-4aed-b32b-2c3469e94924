# Інтеграція псіонічних івентів з Game Director

## Огляд

Цей документ описує інтеграцію системи псіонічних івентів (базованих на глімері) з новою системою Game Director в Goob Station.

## Як це працює

### 1. Система метрик

**Новий ChaosMetric: `Psionic`**
- Додано новий тип хаосу `Psionic` до enum `ChaosMetric`
- Значення базується на поточному рівні глімеру (0-1000 → 0-100)
- Вищий глімер = більший псіонічний хаос

**PsionicMetricSystem:**
- Автоматично вимірює псіонічний хаос на основі глімеру
- Інтегрується з Game Director через `CalculateChaosEvent`

### 2. Псіонічні івенти

Всі псіонічні івенти тепер мають:
- **StationEventComponent** з полем `chaos` для Game Director
- **GlimmerEventComponent** для перевірки глімеру
- Правильні значення хаосу для різних типів івентів

**Доступні івенти:**
- `NoosphericStorm` - пробуджує псіоніків, додає псіонічний хаос
- `NoosphericZap` - оглушує псіоніків, помірний хаос
- `NoosphericFry` - спалює псіонічну ізоляцію, високий хаос
- `GlimmerMobSpawn` - спавнить ворожих мобів, додає Hostile хаос
- `PsionicCatGotYourTongue` - мутить псіоніків, низький хаос
- `GlimmerRandomSentience` - дає розум об'єктам, нейтральний хаос

### 3. Псіонічні історії

**Нова історія: `PsionicAwakening`**
- Фокусується на псіонічній активності
- Включає біти: Peace → PsionicRising → PsionicChaos → PsionicCalm → RepairStation

**Нові біти:**
- `PsionicRising` - збільшує псіонічну активність (ціль: 300 Psionic)
- `PsionicChaos` - високий псіонічний хаос (ціль: 600 Psionic)
- `PsionicCalm` - зменшує псіонічну активність (ціль: 100 Psionic)

### 4. Інтеграція з глімером

**GlimmerGameDirectorSystem:**
- Перевіряє вимоги глімеру перед запуском івентів
- Скасовує івенти, якщо глімер не відповідає вимогам
- Забезпечує безпеку між старою та новою системами

## Переваги інтеграції

1. **Автоматичне керування:** Game Director автоматично вибирає псіонічні івенти на основі поточного стану станції

2. **Збалансованість:** Псіонічні івенти тепер враховують загальний хаос станції, а не тільки глімер

3. **Гнучкість:** Можна створювати історії з різним рівнем псіонічної активності

4. **Сумісність:** Стара система глімеру продовжує працювати, але тепер інтегрована з Game Director

## Налаштування

### Додавання нових псіонічних івентів

1. Створіть прототип з обома компонентами:
```yaml
- type: entity
  id: YourPsionicEvent
  parent: BaseGameRule
  components:
  - type: StationEvent
    chaos:
      Psionic: 25  # Псіонічний хаос
      Medical: 10  # Інші типи хаосу
  - type: GlimmerEvent
    minimumGlimmer: 200
    maximumGlimmer: 800
```

2. Додайте відповідну логіку в систему івентів

### Створення псіонічних історій

1. Визначте біти з псіонічними цілями:
```yaml
- type: storyBeat
  id: YourPsionicBeat
  goal:
    Psionic: 400  # Цільовий рівень псіонічного хаосу
```

2. Створіть історію з цими бітами:
```yaml
- type: story
  id: YourPsionicStory
  beats:
    - YourPsionicBeat
```

## Моніторинг

Game Director автоматично логує:
- Поточний рівень псіонічного хаосу
- Вибрані псіонічні івенти
- Зміни в псіонічних бітах

Використовуйте консольні команди Game Director для моніторингу стану системи.

## Майбутні покращення

1. **Додаткові метрики:** Можна додати більш специфічні псіонічні метрики
2. **Складніші історії:** Створення історій з взаємодією між псіонікою та іншими системами
3. **Динамічні вимоги:** Адаптація вимог глімеру на основі поточного стану станції
