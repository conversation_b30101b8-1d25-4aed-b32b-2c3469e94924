clone-projector-examined-status = { $cloneStatus ->
[true] The clone is currently active.
*[false] The clone is currently inactive.
}
clone-projector-examined-health = {$cloneHealth}% Integrity

gemini-projector-clone-created = A swarm of nanites appears from {$user}'s projector.
gemini-projector-clone-retrieved = The {$target} dematerializes.

gemini-projector-clone-flavor-text = [color=cyan]It's composed of a swarm of nanomachines.[/color]
gemini-projector-clone-name-suffix = - Gemini

gemini-projector-installed = You feel a sharp pain in your lower back.
gemini-projector-removed = You feel your vision widen, and an extremely sharp pain in your lower back.
gemini-projector-clone-destroyed = Every muscle in your body aches as the projector malfunctions.

gemini-projector-regenerate-verb = Regenerate Clone
gemini-projector-regenerate-verb-text = Regenerate the current clone, keeping its mind but setting its body back to default.

gemini-projector-reboot-verb = Recalibrate Personality
gemini-projector-reboot-verb-text = Recalibrate the personality of the current clone, giving it a brand new mind. This cannot be undone.

ghost-role-information-gemini-clone-name = Gemini Hologram
ghost-role-information-gemini-clone-description = Assist the Research Director in any and all of their tasks. Maybe burn a city or two if they want.

monozygotic-projector-clone-name-suffix = - Zygote
