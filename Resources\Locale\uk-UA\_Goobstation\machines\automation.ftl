# Robotic Arm

signal-port-name-input-machine = Предмет: Вхідний пристрій
signal-port-description-input-machine = Слот автоматизації пристрою для вилучення предметів, замість того, щоб брати їх з підлоги.

signal-port-name-output-machine = Предмет: Вихідний пристрій
signal-port-description-output-machine = Слот автоматизації пристрою для вставки предметів, замість того, щоб класти їх на підлогу.

signal-port-name-item-moved = Предмет переміщено
signal-port-description-item-moved = Сигнальний порт, який отримує імпульс після переміщення предмета цією рукою.

signal-port-name-automation-slot-filter = Предмет: Слот фільтра
signal-port-description-automation-slot-filter = Слот автоматизації для фільтра пристрою автоматизації.

# Подрібнювач реагентів

signal-port-name-automation-slot-beaker = Предмет: Слот мензурки
signal-port-description-automation-slot-beaker = Слот автоматизації для мензурки пристрою, що працює з рідинами.

signal-port-name-automation-slot-input = Предмет: Вхідні предмети
signal-port-description-automation-slot-input = Слот автоматизації для зберігання вхідних предметів пристрою.

# Пакувальник

signal-port-name-automation-slot-board = Предмет: Слот плати
signal-port-description-automation-slot-board = Слот автоматизації для мікросхеми пакувальника.

signal-port-name-automation-slot-materials = Предмет: Сховище матеріалів
signal-port-description-automation-slot-materials = Слот автоматизації для вставки матеріалів у сховище пристрою.

# Сміттєпровід

signal-port-name-flush = Змити
signal-port-description-flush = Сигнальний порт для перемикання механізму змиву сміттєпроводу.

signal-port-name-eject = Викинути
signal-port-description-eject = Сигнальний порт для викидання вмісту сміттєпроводу.

signal-port-name-ready = Готовий
signal-port-description-ready = Сигнальний порт, який отримує імпульс після повного нагнітання тиску в сміттєпроводі.

# Контейнер

signal-port-name-automation-slot-storage = Предмет: Сховище
signal-port-description-automation-slot-storage = Слот автоматизації для інвентарю контейнера.

signal-port-name-storage-inserted = Вставлено
signal-port-description-storage-inserted = Сигнальний порт, який отримує імпульс після вставки предмета в контейнер.

signal-port-name-storage-removed = Вилучено
signal-port-description-storage-removed = Сигнальний порт, який отримує імпульс після вилучення предмета з контейнера.

# Факс

signal-port-name-automation-slot-paper = Предмет: Папір
signal-port-description-automation-slot-paper = Слот автоматизації для лотка з папером факсу.

signal-port-name-fax-copy = Копіювати факс
signal-port-description-fax-copy = Сигнальний порт для копіювання паперу в факсі.

# Конструктор / Інтерактор

signal-port-name-machine-start = Старт
signal-port-description-machine-start = Сигнальний порт для одноразового запуску пристрою.

signal-port-name-machine-autostart = Автозапуск
signal-port-description-machine-autostart = Сигнальний порт для керування автоматичним запуском після завершення.

signal-port-name-machine-started = Запущено
signal-port-description-machine-started = Сигнальний порт, який отримує імпульс після запуску пристрою.

signal-port-name-machine-completed = Завершено
signal-port-description-machine-completed = Сигнальний порт, який отримує імпульс після завершення роботи пристрою.

signal-port-name-machine-failed = Невдача
signal-port-description-machine-failed = Сигнальний порт, який отримує імпульс після невдалого запуску пристрою.

# Інтерактор

signal-port-name-automation-slot-tool = Предмет: Інструмент
signal-port-description-automation-slot-tool = Слот автоматизації для інструмента, який тримає інтерактор.

# АвтоДок

signal-port-name-automation-slot-autodoc-hand = Предмет: Рука АвтоДока
signal-port-description-automation-slot-autodoc-hand = Слот автоматизації для органу/частини, що утримується АвтоДоком, для інструкцій ЗБЕРЕГТИ ПРЕДМЕТ / ВЗЯТИ ПРЕДМЕТ.

# Газовий балон

signal-port-name-automation-slot-gas-tank = Предмет: Газовий балон
signal-port-description-automation-slot-gas-tank = Слот автоматизації для газового балона.

# Радіаційний колектор

signal-port-name-rad-empty = Порожній
signal-port-description-rad-empty = Сигнальний порт встановлюється на ВИСОКИЙ, якщо бак відсутній або тиск нижче 33%, в іншому випадку - НИЗЬКИЙ.

signal-port-name-rad-low = Низький
signal-port-description-rad-low = Сигнальний порт встановлюється на ВИСОКИЙ, якщо тиск у баку нижче 66%, в іншому випадку - НИЗЬКИЙ.

signal-port-name-rad-full = Повний
signal-port-description-rad-full = Сигнальний порт встановлюється на ВИСОКИЙ, якщо тиск у баку вище 66%, в іншому випадку - НИЗЬКИЙ.
