# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

reverse-bear-trap-component-target-self = Ви починаєте надягати пастку собі на голову.
reverse-bear-trap-component-start-cuffing-observer = {$user} надягає пастку на {$target}!
reverse-bear-trap-component-start-cuffing-target = Ви починаєте надягати пастку на {$targetName}.
reverse-bear-trap-component-start-cuffing-by-other = {$otherName} силоміць надягає пастку на вашу голову!
reverse-bear-trap-component-unlocked-trap-self = Вам вдалося відкрити пастку!
reverse-bear-trap-component-unlocked-trap-observer = {$user} вдалося відкрити пастку!
reverse-bear-trap-component-failed-unlocked-trap-self = Вам не вдалося відкрити пастку!
reverse-bear-trap-component-failed-unlocked-trap-observer = {$user} не вдалося відкрити пастку!
reverse-bear-trap-component-trap-fall-observer = Пастка падає з голови {$user}.
reverse-bear-trap-component-trap-fall-self = Пастка падає з вашої голови.
reverse-bear-trap-component-trap-click-observer = Пастка закривається на голові {$user}!
reverse-bear-trap-component-trap-click-self = Пастка закривається!
reverse-bear-trap-component-trap-snap-observer = ХРЯСЬ! Пастка розриває голову {$user} на шматки!
reverse-bear-trap-component-trap-snap-self = ХРЯСЬ! Пастка розриває вашу голову на шматки!
reverse-bear-trap-component-start-welding-observer = {$user} зрізає пастку з {$target}!
reverse-bear-trap-component-start-welding-target = Ви починаєте зрізати пастку з {$targetName}.
reverse-bear-trap-component-start-welding-by-other = {$otherName} зрізає пастку з вашої голови!
reverse-bear-trap-component-start-unlocking-target-self = Ви починаєте відкривати пастку.
reverse-bear-trap-component-start-unlocking-observer = {$user} відкриває пастку на голові {$target}!
reverse-bear-trap-component-start-unlocking-target = Ви починаєте відкривати пастку на {$targetName}.
reverse-bear-trap-component-start-unlocking-by-other = {$otherName} відкриває пастку на вашій голові!
