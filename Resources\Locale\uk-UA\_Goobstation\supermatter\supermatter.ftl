# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

supermatter-announcer = Автоматичний двигун на суперматерії

supermatter-examine-integrity = Його цілісність становить [color=yellow]{$integrity}%[/color].

supermatter-warning = Увага! Цілісність гіперструктури кристала порушена! Цілісність: {$integrity}%.

supermatter-emergency = НЕБЕЗПЕКА! Цілісність гіперструктури кристала досягає критичного рівня! Цілісність: {$integrity}%.

supermatter-delam-explosion = НЕЗАБАРОМ РОЗШАРУВАННЯ КРИСТАЛА! Кристал досяг критичного руйнування цілісності! Задіяно поле екстреної дестабілізації причинності.

supermatter-delam-overmass = НЕЗАБАРОМ РОЗШАРУВАННЯ КРИСТАЛА! Цілісність гіперструктури кристала досягла критичної маси! Незабаром утвориться сингулярність!

supermatter-delam-tesla = НЕЗАБАРОМ РОЗШАРУВАННЯ КРИСТАЛА! Цілісність гіперструктури кристала досягла критичного стрибка напруги! Незабаром утвориться енергетична куля!

supermatter-delam-cascade = НЕЗАБАРОМ РОЗШАРУВАННЯ КРИСТАЛА! Перевищено межі гармонійної частоти, поле дестабілізації причинності не може бути задіяно!

supermatter-delam-cancel = Кристалічна гіперструктура повертається до безпечних робочих параметрів. Запобіжник вимкнено. Цілісність: {$integrity}%.

supermatter-seconds-before-delam = Приблизний час до розшарування: {$seconds} секунд.

supermatter-tamper-begin = Ви починаєте обережно відрізати шматок кристала суперматерії...

supermatter-tamper-end = Ви відчуваєте силу тисячі сонць у своїх долонях. Чи це все радіація?

supermatter-announcement-cc-tamper = Наша автоматична система виявлення пошкоджень зафіксувала, що структурна цілісність кристала суперматерії була порушена зовнішньою силою.
    Інженерний відділ, негайно прибудьте до двигуна на суперматерії.
