medical-item-cant-use-rebell = Ви не можете допомогти { $target }. У нього/неї занадто сильна кровотеча...
cant-heal-damage-container-rebell = Ви не можете допомогти { $target } за допомогою { $used }
rebell-medical-item-stop-bleeding-fully = Ви повністю зупинили кровотечу.
rebell-medical-item-stop-bleeding-partially = Ви частково зупинили кровотечу
does-not-exist-rebell = Частина тіла відсутня.
puts-on-a-tourniquet = { CAPITALIZE($user) } накладає джгут на { $part }.
takes-off-a-tourniquet = { CAPITALIZE($user) } знімає джгут з { $part }.
take-off-tourniquet = Зняти джгут з { $part }
already-tourniqueted = Накладання ще одного джгута буде смертельним.
cant-tourniquet = Неможливо накласти джгут тут...
cant-put-tourniquet-here = Неможливо накласти джгут на цю частину.
medical-item-requires-surgery-rebell = Ви зробили все, що могли. { $target } потребує операції.

missing-body-part = Частина тіла відсутня.
no-wounds-tourniquet = Немає ран, на які можна накласти джгут.