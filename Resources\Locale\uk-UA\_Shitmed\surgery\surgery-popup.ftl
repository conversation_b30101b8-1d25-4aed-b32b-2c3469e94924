# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

surgery-popup-step-SurgeryStepOpenIncisionScalpel = {$user} робить розріз на {$part} у {$target}.
surgery-popup-step-SurgeryStepClampBleeders = {$user} затискає кровоносні судини на {$part} у {$target}.
surgery-popup-step-SurgeryStepRetractSkin = {$user} відтягує шкіру на {$part} у {$target}.
surgery-popup-step-SurgeryStepSawBones = {$user} пиляє кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepPriseOpenBones = {$user} розтискає кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepCloseBones = {$user} зводить кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepMendRibcage = {$user} лагодить грудну клітку на {$part} у {$target}.
surgery-popup-step-SurgeryStepCloseIncision = {$user} зашиває розріз на {$part} у {$target}.

surgery-popup-step-SurgeryStepInsertFeature = {$user} вставляє щось на {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachHead-step-SurgeryStepInsertFeature = {$user} прикріплює голову до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftArm-step-SurgeryStepInsertFeature = {$user} прикріплює ліву руку до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightArm-step-SurgeryStepInsertFeature = {$user} прикріплює праву руку до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftLeg-step-SurgeryStepInsertFeature = {$user} прикріплює ліву ногу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightLeg-step-SurgeryStepInsertFeature = {$user} прикріплює праву ногу до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftHand-step-SurgeryStepInsertFeature = {$user} прикріплює ліву кисть до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightHand-step-SurgeryStepInsertFeature = {$user} прикріплює праву кисть до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLeftFoot-step-SurgeryStepInsertFeature = {$user} прикріплює ліву ступню до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachRightFoot-step-SurgeryStepInsertFeature = {$user} прикріплює праву ступню до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachLegs-step-SurgeryStepInsertFeature = {$user} прикріплює ноги до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachHands-step-SurgeryStepInsertFeature = {$user} прикріплює кисті до {$part} у {$target}!
surgery-popup-procedure-SurgeryAttachFeet-step-SurgeryStepInsertFeature = {$user} прикріплює ступні до {$part} у {$target}!

surgery-popup-step-SurgeryStepSealWounds = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepSawFeature = {$user} пиляє кістки на {$part} у {$target}.
surgery-popup-step-SurgeryStepClampInternalBleeders = {$user} затискає внутрішні кровоносні судини на {$part} у {$target}.
surgery-popup-step-SurgeryStepRemoveFeature = {$user} ампутує {$part} у {$target}!
surgery-popup-step-SurgeryStepCarefulIncisionScalpel = {$user} обережно робить розріз на {$part} у {$target}.
surgery-popup-step-SurgeryStepRepairBruteTissue = {$user} відновлює пошкоджені тканини на {$part} у {$target}!
surgery-popup-step-SurgeryStepRepairBurnTissue = {$user} відновлює обпечені тканини на {$part} у {$target}!
surgery-popup-step-SurgeryStepSealTendWound = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepInsertItem = {$user} вставляє щось у {$part} у {$target}!
surgery-popup-step-SurgeryStepRemoveItem = {$user} видаляє щось із {$part} у {$target}!
surgery-popup-step-SurgeryStepSealDismembermentWound = {$user} зашиває рану від розчленування на {$part} у {$target}.
surgery-popup-step-SurgeryStepCloseBloodOutputs = {$user} зашиває кровоносну судину на {$part} у {$target}.
surgery-popup-step-SurgeryStepRemoveSeveredSkin = {$user} видаляє відмерлу шкіру з {$part} у {$target}.
surgery-popup-step-SurgeryStepRemoveLeftoverBones = {$user} видаляє залишки кісток після розчленування на {$part} у {$target}.
surgery-popup-step-SurgeryStepHealOrgans = {$user} відновлює тканини органів всередині {$part} у {$target}.
surgery-popup-step-SurgeryStepMendBones = {$user} відновлює кісткові тканини всередині {$part} у {$target}.
surgery-popup-step-SurgeryStepRemoveOrgan = {$user} видаляє орган із {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertOrgan = {$user} вставляє орган у {$part} у {$target}!

surgery-popup-step-SurgeryStepOpenOrganSlot = {$user} відкриває порожнину в {$part} {$target}!

surgery-popup-procedure-SurgeryRemoveBrain-step-SurgeryStepRemoveOrgan = {$user} видаляє мозок з {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveHeart-step-SurgeryStepRemoveOrgan = {$user} видаляє серце з {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveLiver-step-SurgeryStepRemoveOrgan = {$user} видаляє печінку з {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveLungs-step-SurgeryStepRemoveOrgan = {$user} видаляє легені з {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveEyes-step-SurgeryStepRemoveOrgan = {$user} видаляє очі з {$part} у {$target}!
surgery-popup-procedure-SurgeryRemoveStomach-step-SurgeryStepRemoveOrgan = {$user} видаляє шлунок з {$part} у {$target}!

surgery-popup-procedure-SurgeryInsertBrain-step-SurgeryStepInsertOrgan = {$user} вставляє мозок у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertLungs = {$user} вставляє легені у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertLiver = {$user} вставляє печінку у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertEyes = {$user} вставляє очі у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertHeart = {$user} вставляє серце у {$part} у {$target}!
surgery-popup-step-SurgeryStepInsertStomach = {$user} вставляє шлунок у {$part} у {$target}!

surgery-popup-step-SurgeryStepSealOrganWound = {$user} зашиває рани на {$part} у {$target}.
surgery-popup-step-SurgeryStepLobotomize = {$user} лоботомізує {$target}!
surgery-popup-step-SurgeryStepMendBrainTissue = {$user} відновлює тканини мозку у {$target} на {$part}.

surgery-popup-step-SurgeryStepRemoveOrgan-failed = Вам чомусь не вдалося видалити орган...
