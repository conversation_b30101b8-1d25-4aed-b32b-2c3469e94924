- type: entity
  id: EffectGravityPulse
  categories: [ HideSpawnMenu ]
  components:
  - type: TimedDespawn
    lifetime: 0.32
  - type: Sprite
    drawdepth: Effects
    noRot: true
    layers:
    - shader: unshaded
      map: ["enum.EffectLayers.Unshaded"]
      sprite: Effects/gravityPulse.rsi
      state: gravityPulse
  - type: EffectVisuals
  - type: Tag
    tags:
    - HideContextMenu
  - type: AnimationPlayer
