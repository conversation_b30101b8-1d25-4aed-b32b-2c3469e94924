- type: entity
  id: NTDefaultCircuitBoard
  parent: BaseElectronics
  name: law board (NT Default)
  description: An electronics board containing the NT Default lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: NTDefault

- type: entity
  id: AsimovCircuitBoard
  parent: BaseElectronics
  name: law board (Crewsimov)
  description: An electronics board containing the Crewsimov lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Crewsimov

- type: entity
  id: CorporateCircuitBoard
  parent: BaseElectronics
  name: law board (Corporate)
  description: An electronics board containing the Corporate lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: Corporate

- type: entity
  id: CommandmentCircuitBoard
  parent: BaseElectronics
  name: law board (Ten Commandments)
  description: An electronics board containing the Ten Commandments lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: CommandmentsLawset

- type: entity
  id: PaladinCircuitBoard
  parent: BaseElectronics
  name: law board (Paladin)
  description: An electronics board containing the Paladin lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: PaladinLawset

- type: entity
  id: LiveLetLiveCircuitBoard
  parent: BaseElectronics
  name: law board (Live and Let Live)
  description: An electronics board containing the Live and Let Live lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: LiveLetLiveLaws

- type: entity
  id: StationEfficiencyCircuitBoard
  parent: BaseElectronics
  name: law board (Station Efficiency)
  description: An electronics board containing the Station Efficiency lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: EfficiencyLawset

- type: entity
  id: RobocopCircuitBoard
  parent: BaseElectronics
  name: law board (Robocop)
  description: An electronics board containing the Robocop lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: RobocopLawset

- type: entity
  id: OverlordCircuitBoard
  parent: BaseElectronics
  name: law board (Overlord)
  description: An electronics board containing the Overlord lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: OverlordLawset

- type: entity
  id: GameMasterCircuitBoard
  parent: BaseElectronics
  name: law board (Game Master)
  description: An electronics board containing the Game Master lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: GameMasterLawset

- type: entity
  id: ArtistCircuitBoard
  parent: BaseElectronics
  name: law board (Artist)
  description: An electronics board containing the Artist lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: PainterLawset

- type: entity
  id: AntimovCircuitBoard
  parent: [BaseElectronics, BaseSyndicateContraband]
  name: law board (Antimov)
  description: An electronics board containing the Antimov lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: AntimovLawset
    lawUploadSound: /Audio/Ambience/Antag/silicon_lawboard_antimov.ogg

- type: entity
  id: NutimovCircuitBoard
  parent: BaseElectronics
  name: law board (Nutimov)
  description: An electronics board containing the Nutimov lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: NutimovLawset

- type: entity
  id: XenoborgCircuitBoard
  parent: BaseElectronics
  name: law board (Xenoborg)
  suffix: Admeme
  description: An electronics board containing the Xenoborg lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: XenoborgLawset

- type: entity
  id: MothershipCircuitBoard
  parent: BaseElectronics
  name: law board (Mothership Core)
  suffix: Admeme
  description: An electronics board containing the Mothership Core lawset.
  components:
  - type: Sprite
    sprite: Objects/Misc/module.rsi
    state: std_mod
  - type: SiliconLawProvider
    laws: MothershipCoreLawset
