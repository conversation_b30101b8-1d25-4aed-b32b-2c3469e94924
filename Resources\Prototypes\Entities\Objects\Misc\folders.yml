- type: entity
  id: BoxFolderNuclearCodes
  parent: BaseItem
  name: nuclear code folder
  components:
  - type: Sprite
    sprite: Objects/Misc/folders.rsi
    layers:
    - state: folder-colormap
      color: "#cc2323"
    - state: folder-base
    - state: folder-stamp-inverse
      color: "#1dff00"
  - type: SpawnItemsOnUse
    items:
    - id: NukeCodePaper
    sound:
      path: /Audio/Effects/packetrip.ogg
  - type: Appearance

- type: entity
  id: BoxFolderBase
  parent: BoxBase
  name: folder
  description: A folder filled with top secret paperwork.
  components:
  - type: Sprite
    sprite: Objects/Misc/folders.rsi
    layers:
    - state: folder-colormap
    - state: folder-base
# RandomSpriteColor requires netsync which is currently incompatible with ItemMapper
#  - type: RandomSpriteColor
#    sprite: Objects/Misc/folders.rsi
#    state: folder-colormap
#    colors:
#      red: "#cc2323"
#      blue: "#355d99"
#      yellow: "#b38e3c"
#      white: "#e6e6e6"
#      grey: "#999999"
#      black: "#3f3f3f"
#      green: "#43bc38"
  - type: Item
    sprite: Objects/Misc/folders.rsi
    size: Small
    shape: null
  - type: Storage
    maxItemSize: Small
    grid:
    - 0,0,4,3
    whitelist:
      tags:
      - Document
  - type: ItemMapper
    mapLayers:
      folder-overlay-paper:
        whitelist:
          tags:
          - Document
  - type: Appearance
  - type: Tag
    tags:
    - Folder
  - type: StorageFill
    contents:
      - id: Paper
        prob: 0.5
      - id: PaperOffice
        prob: 0.4
      - id: Paper
        prob: 0.3
      - id: PaperOffice
        prob: 0.2
      - id: Paper
        prob: 0.2

- type: entity
  id: BoxFolderRed
  parent: BoxFolderBase
  suffix: Red
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#cc2323"
    - state: folder-base

- type: entity
  id: BoxFolderBlue
  parent: BoxFolderBase
  suffix: Blue
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#355d99"
    - state: folder-base

- type: entity
  id: BoxFolderYellow
  parent: BoxFolderBase
  suffix: Yellow
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#b38e3c"
    - state: folder-base

- type: entity
  id: BoxFolderWhite
  parent: BoxFolderBase
  suffix: White
  components:
  - type: Sprite
    layers:
    - state: folder-white
    - state: folder-base

- type: entity
  id: BoxFolderGrey
  parent: BoxFolderBase
  suffix: Grey
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#999999"
    - state: folder-base

- type: entity
  id: BoxFolderBlack
  parent: BoxFolderBase
  suffix: Black
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#3f3f3f"
    - state: folder-base

- type: entity
  id: BoxFolderGreen
  parent: BoxFolderBase
  suffix: Green
  components:
  - type: Sprite
    layers:
    - state: folder-colormap
      color: "#43bc38"
    - state: folder-base

- type: entity
  id: BoxFolderCentCom
  name: CentComm folder
  parent: BoxFolderBase
  categories: [ DoNotMap ]
  description: CentComm's miserable little pile of secrets!
  components:
  - type: Sprite
    layers:
    - state: folder-centcom
    - state: folder-base

- type: entity
  id: BoxFolderClipboard
  parent: BoxFolderBase
  name: clipboard
  description: The weapon of choice for those on the front lines of bureaucracy.
  components:
  - type: Sprite
    sprite: Objects/Misc/clipboard.rsi
    layers:
    - state: clipboard
    - state: clipboard_paper
      map: ["clipboard_paper"]
      visible: false
    - state: clipboard_pen
      map: ["clipboard_pen"]
      visible: false
    - state: clipboard_over
  - type: ContainerContainer
    containers:
      storagebase: !type:Container
        ents: []
      pen_slot: !type:ContainerSlot {}
  - type: ItemSlots
    slots:
      pen_slot:
        name: clipboard-slot-component-slot-name-pen
        whitelist:
          tags:
          - Write
        insertOnInteract: false
  - type: Item
    sprite: Objects/Misc/clipboard.rsi
    size: Small
  - type: Clothing
    slots: [belt]
    quickEquip: false
    sprite: Objects/Misc/clipboard.rsi
  - type: Storage
    grid:
    - 0,0,5,3
    whitelist:
      tags:
      - Document
  - type: ItemMapper
    mapLayers:
      clipboard_paper:
        whitelist:
          tags:
          - Document
      clipboard_pen:
        whitelist:
          tags:
          - Write
  - type: MeleeWeapon
    wideAnimationRotation: 180
    damage:
      types:
        Blunt: 6

- type: entity
  id: BoxFolderCentComClipboard
  parent: BoxFolderClipboard
  name: CentComm clipboard
  description: A luxurious clipboard upholstered with green velvet. Often seen carried by CentComm officials, seldom seen actually used.
  components:
  - type: Sprite
    sprite: Objects/Misc/cc-clipboard.rsi
    layers:
    - state: clipboard
    - state: clipboard_paper
      map: ["clipboard_paper"]
      visible: false
    - state: clipboard_pen
      map: ["clipboard_pen"]
      visible: false
    - state: clipboard_over
  - type: Item
    sprite: Objects/Misc/cc-clipboard.rsi
  - type: Clothing
    sprite: Objects/Misc/cc-clipboard.rsi

- type: entity
  id: BoxFolderQmClipboard
  parent: [BoxFolderClipboard, BaseGrandTheftContraband]
  name: requisition digi-board
  description: A bulky electric clipboard, filled with shipping orders and financing details. With so many compromising documents, you ought to keep this safe.
  components:
  - type: Sprite
    sprite: Objects/Misc/qm_clipboard.rsi
    layers:
    - state: qm_clipboard
    - state: qm_clipboard_paper
      map: ["qm_clipboard_paper"]
      visible: false
    - state: qm_clipboard_pen
      map: ["qm_clipboard_pen"]
      visible: false
    - state: qm_clipboard_over
  - type: ItemSlots
    slots:
      pen_slot:
        name: clipboard-slot-component-slot-name-pen
        whitelist:
          tags:
          - Write
        insertOnInteract: true
  - type: Item
    sprite: Objects/Misc/qm_clipboard.rsi
    size: Normal
  - type: Clothing
    sprite: Objects/Misc/qm_clipboard.rsi
  - type: Storage
    grid:
    - 0,0,4,3
    quickInsert: true
  - type: StorageFill
    contents: [] #to override base clipboard fill
  - type: ItemMapper
    mapLayers:
      qm_clipboard_paper:
        whitelist:
          tags:
          - Document
      qm_clipboard_pen:
        whitelist:
          tags:
          - Write
  - type: CargoOrderConsole
    removeLimitAccess: [ "Quartermaster" ]
  - type: ActivatableUI
    verbText: qm-clipboard-computer-verb-text
    key: enum.CargoConsoleUiKey.Orders
  - type: UserInterface
    interfaces:
      enum.CargoConsoleUiKey.Orders:
        type: CargoOrderConsoleBoundUserInterface
      enum.StorageUiKey.Key:
        type: StorageBoundUserInterface
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 10
  - type: Tag
    tags:
    - Folder
    - HighRiskItem
  - type: StealTarget
    stealGroup: BoxFolderQmClipboard
