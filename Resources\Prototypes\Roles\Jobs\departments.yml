# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: department
  id: Cargo
  name: department-Cargo
  description: department-Cargo-description
  color: "#A46106"
  roles:
  - CargoTechnician
  - Quartermaster
  - SalvageSpecialist

- type: department
  id: Civilian
  name: department-Civilian
  description: department-Civilian-description
  color: "#9FED58"
  weight: -10
  roles:
  - Bartender
  - Botanist
  - Boxer
  - Chaplain
  - Chef
  - Clown
  - HeadOfPersonnel
  - Janitor
  - Lawyer
  - Librarian
  - Mime
  - Musician
  - Passenger
  - Reporter
  - Visitor
  - Zookeeper
  - ServiceWorker

- type: department
  id: CentralCommand
  name: department-CentralCommand
  description: department-CentralCommand-description
  color: "#0c344d"
  roles:
  - CentralCommandOfficial
  - CBURN
  - ERTLeader
  - ERTChaplain
  - ERTJanitor
  - ERTMedical
  - ERTSecurity
  - ERTEngineer
  - DeathSquad
  - HecuOperative
  - BlueshieldOfficer # Goobstation - Start
  - NanotrasenRepresentative
  - NanotrasenCareerTrainer
  - NavyOfficer
  - NavyCaptain
  - NavyOfficerUndercover
  - SpecialOperationsOfficer
  - Diplomat
  - GovernmentMan # Goobstation - End
  editorHidden: true
  weight: 120

- type: department
  id: Command
  name: department-Command
  description: department-Command-description
  color: "#334E6D"
  roles:
  - Captain
  - ChiefEngineer
  - ChiefMedicalOfficer
  - HeadOfPersonnel
  - HeadOfSecurity
  - ResearchDirector
  - Quartermaster
  - BlueshieldOfficer # Goobstation
  - NanotrasenRepresentative # Goobstation
  - NanotrasenCareerTrainer # Goobstation
  - CentralCommandOfficial
  - CBURN
  - ERTLeader
  - ERTChaplain
  - ERTJanitor
  - ERTMedical
  - ERTSecurity
  - ERTEngineer
  - DeathSquad
  - HecuOperative
  primary: false
  weight: 100

- type: department
  id: Engineering
  name: department-Engineering
  description: department-Engineering-description
  color: "#EFB341"
  roles:
  - AtmosphericTechnician
  - ChiefEngineer
  - StationEngineer
  - TechnicalAssistant

- type: department
  id: Medical
  name: department-Medical
  description: department-Medical-description
  color: "#52B4E9"
  roles:
  - Chemist
  - ChiefMedicalOfficer
  - MedicalDoctor
  - MedicalIntern
  - Psychologist
  - Paramedic

- type: department
  id: Security
  name: department-Security
  description: department-Security-description
  color: "#DE3A3A"
  weight: 20
  roles:
  - HeadOfSecurity
  - SecurityCadet
  - SecurityOfficer
  - Detective
  - Warden
  - Brigmedic # Goob

- type: department
  id: Science
  name: department-Science
  description: department-Science-description
  color: "#D381C9"
  roles:
  - ResearchDirector
  - Scientist
  - ForensicMantis
  - ResearchAssistant

- type: department
  id: Silicon
  name: department-Silicon
  description: department-Silicon-description
  color: "#D381C9"
  roles:
  - Borg
  - StationAi

- type: department
  id: Specific
  name: department-Specific
  description: department-Specific-description
  color: "#9FED58"
  weight: 10
  roles:
  - Boxer
  - Reporter
  - Zookeeper
  - Psychologist
  primary: false
