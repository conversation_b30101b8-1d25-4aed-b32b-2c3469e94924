- type: entity
  id: LockerForensicMantisFilled
  suffix: Filled
  parent: LockerForensicMantis
  components:
  - type: StorageFill
    contents:
      # Tools
      # - id: FlashlightSeclite # Deltav - Detective is in charge of investigating crimes.
      # - id: ForensicScanner
      # - id: BoxForensicPad
      - id: AntiMindControlDevice
      - id: AntiPsychicKnife
      - id: FlashlightLantern # DeltaV - To replace their lost flashlight
      - id: BoxZiptie # DeltaV - Give the mantis some zipties
      - id: WeaponPistolPsiBreaker # DeltaV - Mantis mindbreaker pistol, see Resources/Prototypes/DeltaV/Entities?Objects/Weapons/Guns/Pistols/pistols.yml
      # Spare change of clothes
      # - id: ClothingUniformJumpsuitMantis
      # - id: ClothingUniformSkirtMantis
      # - id: ClothingBeltMantis
      # - id: ClothingShoesBootsMantis
      # - id: ClothingHandsGlovesForensic # Deltav - Detective is in charge of investigating crimes.
      - id: ClothingHeadHatFezMantis
      # - id: ClothingOuterCoatMantis
      - id: ClothingOuterWinterCoatMantis
      # - id: ClothingEyesGlassesSunglasses
      # Insulative headgear
      - id: ClothingHeadTinfoil
      - id: ClothingHeadCage
        amount: 2
      - id: ClothingEyesGlassesEthereal