- type: entity
  id: SpawnPointGhostIfrit
  name: ghost role spawn point
  suffix: Ifrit
  parent: MarkerBase
  categories: [ HideSpawnMenu ]
  components:
  - type: GhostRoleMobSpawner
    prototype: MobIfritFamiliar
  - type: GhostRole
    name: Mystagogue's Ifrit
    description: Obey the mystagogue. Defend the oracle.
    rules: You are a servant of the mystagogue. Obey them directly.
  - type: Sprite
    sprite: Markers/jobs.rsi
    layers:
      - state: green
      - sprite: Mobs/Animals/bat.rsi
        state: bat
