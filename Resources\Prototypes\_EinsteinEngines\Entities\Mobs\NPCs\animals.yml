# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  name: squackroach
  parent: MobCockroach
  id: MobSquackroach
  description: This is just a horrific genetic stew of a specimen.
  components:
  - type: GhostRole
    makeSentient: true
    allowSpeech: true
    allowMovement: true
    name: ghost-role-information-squackroach-name
    description: ghost-role-information-squackroach-description
    rules: ghost-role-information-freeagent-rules
    mindRoles:
    - MindRoleGhostRoleFreeAgent
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeCircle
          radius: 0.2
        density: 0.007
        mask:
        - SmallMobMask
        layer:
        - SmallMobLayer
  - type: GhostTakeoverAvailable
  - type: Speech
    speechVerb: Harpy
    speechSounds: Harpy
  - type: Sprite
    sprite: _EinsteinEngines/Mobs/Animals/squackroach.rsi
    layers:
    - map: ["enum.DamageStateVisualLayers.Base", "movement"]
      state: squackroach
  - type: SpriteMovement
    movementLayers:
      movement:
        state: squackroach-moving
    noMovementLayers:
      movement:
        state: squackroach
  - type: Item
    size: Normal
  - type: Clothing
    quickEquip: false
    sprite: _EinsteinEngines/Mobs/Animals/squackroach.rsi
    equippedPrefix: sq
    slots:
    - HEAD
    - NECK
  - type: Appearance
  - type: DamageStateVisuals
    states:
      Alive:
        Base: squackroach
      Critical:
        Base: squackroach_dead
      Dead:
        Base: squackroach_dead
  - type: MobThresholds
    thresholds:
      0: Alive
      40: Critical
      60: Dead
  - type: MovementSpeedModifier
    baseWalkSpeed : 2.5
    baseSprintSpeed : 4
    weightlessAcceleration: 1.5
    weightlessFriction: 1
    weightlessModifier: 1.12
  - type: Damageable
    damageContainer: Biological
    damageModifierSet: Moth
  - type: Bloodstream
    bloodReagent: InsectBlood # it's still somewhat an insect... somehow
  - type: Respirator
  - type: CombatMode
  - type: Butcherable
    spawned:
    - id: FoodMeatRat
      amount: 2
  - type: Extractable
    grindableSolutionName: food
  - type: Vocal
    sounds:
      Male: SoundsHarpyMale
      Female: SoundsHarpyFemale
      Unsexed: SoundsHarpyFemale
  - type: GenericVisualizer
    visuals:
      enum.HarpyVisualLayers.Singing:
        singingLayer:
          False: { visible: false }
          True: { visible: true }
    wilhelmProbability: 0.001
  - type: MobPrice
    price: 150
  - type: Tag
    tags:
    - Trash
    - CannotSuicide
    - HarpyEmotes
    - VimPilot
  - type: CanEscapeInventory
  - type: NpcFactionMember
    factions:
    - Mouse
  - type: Body
    prototype: Squackroach
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Blunt
        damage: 60
      behaviors:
      - !type:GibBehavior { }
  - type: FireVisuals
    sprite: Mobs/Effects/onfire.rsi
    normalState: Mouse_burning
  - type: SurgeryTarget
  - type: UserInterface
    interfaces:
      enum.SurgeryUIKey.Key:
        type: SurgeryBui
      enum.InstrumentUiKey.Key:
        type: InstrumentBoundUserInterface
        requireInputValidation: false
