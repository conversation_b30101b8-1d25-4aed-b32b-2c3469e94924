- type: entity
  id: ShellSoulbreaker
  name: shell (.50 soulbreaker)
  parent: BaseShellShotgun
  components:
  - type: Sprite
    layers:
      - state: practice
        map: [ "enum.AmmoVisualLayers.Base" ]
  - type: CartridgeAmmo
    proto: PelletShotgunSoulbreaker
  - type: ChemicalAmmo
  - type: SolutionContainerManager
    solutions:
      ammo:
        reagents:
        - ReagentId: SoulbreakerToxin
          Quantity: 15
  - type: SolutionTransfer
    maxTransferAmount: 15
  - type: SpentAmmoVisuals
    state: "practice"
  - type: GuideHelp
    guides: [ Mindbreaking ]
