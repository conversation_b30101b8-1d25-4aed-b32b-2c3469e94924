# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

  # Flight shaders

- type: shader
  id: Flap
  kind: source
  path: "/Textures/_EinsteinEngines/Shaders/flap.swsl"

- type: shader
  id: Ethereal
  kind: source
  path: "/Textures/_EinsteinEngines/Shaders/ethereal.swsl"

- type: shader
  id: ColorTint
  kind: source
  path: "/Textures/_EinsteinEngines/Shaders/color_tint.swsl"
