# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: emote
  id: Yip
  name: chat-emote-name-yip
  category: Vocal
  icon: { sprite: Mobs/Animals/kobold.rsi, state: full }
  chatMessages: [ "chat-emote-msg-yip" ]
  whitelist:
    tags:
    - ReptilianEmotes
  blacklist:
    components:
      - BorgChassis
  chatTriggers:
  - yip
  - yips
