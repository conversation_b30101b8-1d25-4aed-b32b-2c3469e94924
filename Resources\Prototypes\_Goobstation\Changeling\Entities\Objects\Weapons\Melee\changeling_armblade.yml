# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  parent: ArmBlade
  id: ArmBladeChangeling
  suffix: Unremoveable
  components:
  - type: Sharp
  - type: Sprite
    sprite: _Goobstation/Changeling/arm_blade.rsi
    state: icon
  - type: MeleeWeapon
    angle: 0
    wideAnimationRotation: 75
    attackRate: 1.5
    damage:
      types:
        Slash: 20
        Structural: 20
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
  - type: Item
    size: Ginormous
    sprite: _Goobstation/Changeling/arm_blade.rsi
  - type: Prying
    speedModifier: 2
    pryPowered: true
  - type: UseDelay # For insta prying
    delay: 1
  - type: Unremoveable
  - type: Tool
    qualities:
    - Slicing
    - Prying
  - type: DisarmMalus
    malus: 0

- type: entity
  parent: ArmBladeChangeling
  id: FakeArmBladeChangeling
  components:
  - type: ChangelingFakeWeapon
  - type: MeleeWeapon
    damage:
      types:
        Blunt: 1
        Slash: 1
        Piercing: 1
        Structural: 1
  - type: TimedDespawn
    lifetime: 60
  - type: GibThisGuy
    ocNames:
    - BombasterDS
