# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: BulletMic
  name: MIC bolt
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: Not too bad, but you still don't want to get hit by it.
  components:
  - type: Sprite
    noRot: false
    sprite: _Goobstation/Objects/Weapons/Guns/Projectiles/cbbolt.rsi
    layers:
    - state: cbbolt
  - type: Projectile
    damage:
      types:
        Shock: 10
  - type: StaminaDamageOnCollide
    damage: 30
  - type: KnockdownOnCollide
    behavior: DropIfStanding
  - type: BlurOnCollide
  - type: TimedDespawn
    lifetime: 0.4
  - type: Ammo
    muzzleFlash: null
  - type: Reflective
    reflective:
    - Energy
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.05,-0.05,0.05,0.05"
        hard: false
        mask:
        - Opaque

- type: entity
  id: BulletPlasmaCutter
  name: cutter bolt
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: Not too bad, but you still don't want to get hit by it.
  components:
  - type: Reflective
    reflective:
      - NonEnergy
  - type: Sprite
    noRot: false
    sprite: _Goobstation/Objects/Weapons/Guns/Projectiles/bullet_cutter.rsi
    layers:
    - state: icon
      shader: unshaded
  - type: GatheringProjectile
    amount: 40
  - type: Projectile
    deleteOnCollide: false
    damage:
      types:
        Heat: 5
  - type: StaminaDamageOnCollide
    damage: 20
  - type: TimedDespawn
    lifetime: 0.2
  - type: PointLight
    radius: 2
    color: purple
    energy: 1

- type: entity
  id: BulletPlasmaCutterStrong
  parent: BulletPlasmaCutter
  components:
  - type: GatheringProjectile
  - type: Projectile
    damage:
      types:
        Heat: 18
        Structural: 100
  - type: TimedDespawn
    lifetime: 0.4
  - type: PointLight
    radius: 4
    color: purple
    energy: 2

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseBullet
  id: BulletHighCaliber
  name: bullet (.50 anti-materiel)
  components:
  - type: Projectile
    damage:
      types:
        Piercing: 35
      armorPenetration: 0.5
    penetrationThreshold: 50

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseBulletTrigger
  id: BulletHighCaliberExplosive
  name: bullet (.50 high-explosive incendiary)
  components:
  - type: Projectile
    damage:
      types:
        Heat: 1
        Blunt: 2
        Piercing: 3
        Structural: 10
  - type: ExplodeOnTrigger
  - type: Explosive
    explosionType: FireBombWeak # 1.3 firestacks
    totalIntensity: 10.8 # 3x3 explosion
    intensitySlope: 1.4 # 14 damage at the center tile
    maxIntensity: 2.4 # 8 damage at the edges
    maxTileBreak: 0 # 4 damage at the corners
  - type: PointLight
    radius: 3.5
    color: orange
    energy: 0.5
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
    - state: bullet
      shader: unshaded

- type: hitscan
  id: HeavyPulse
  damage:
    types:
      Heat: 35 # Damage nerfed slightly to give you a BIT of a chance. You're still fucked though.
      Blunt: 10
      Structural: 80
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_blue
  travelFlash:
    sprite: _Goobstation/Objects/Weapons/Guns/Projectiles/heavy_pulse.rsi
    state: beam_blue_heavy
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_blue

- type: hitscan
  id: LightPulse
  damage:
    types:
      Heat: 20 # Slightly nerfing damage, otherwise people will bitch about how I'm giving the pulse pistol out like candy
      Blunt: 5 # Split damage is cool.
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_blue
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_blue
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_blue

- type: hitscan
  id: LaserSniper
  damage:
    types:
      Heat: 30
      Shock: 10
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_blue
  travelFlash:
    sprite: _Goobstation/Objects/Weapons/Guns/Projectiles/heavy_pulse.rsi
    state: beam_blue_heavy
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_blue

- type: hitscan
  id: RedMediumSplitLaser
  damage:
    types:
      Heat: 17
      Shock: 2 # Lecter is slightly armor-piercing and so is this
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_laser
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_laser

- type: entity
  name: heavy laser bolt
  id: BulletLaserHeavy
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 8

- type: entity
  name: heavy laser bolt
  id: BulletLaserHeavyStructural
  parent: BulletLaserHeavy
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Heat: 12
        Structural: 10

- type: entity
  name: heavy laser barrage
  id: BulletLaserSpreadHeavy
  categories: [ HideSpawnMenu ]
  parent: BulletLaserHeavy
  components:
  - type: ProjectileSpread
    proto: BulletLaserHeavyStructural
    count: 6
    spread: 30

- type: hitscan
  id: StunPulse
  staminaDamage: 80
  muzzleFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: muzzle_blue
  travelFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: beam_blue
  impactFlash:
    sprite: Objects/Weapons/Guns/Projectiles/projectiles.rsi
    state: impact_blue

- type: entity
  id: BulletTemperatureCold
  name: cold temperature bolt
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  description: Chilling!
  components:
  - type: Projectile
    damage:
      types:
        Cold: 3
  - type: PointLight
    radius: 2
    color: blue
    energy: 1
  - type: ChangeTemperatureOnCollide
    heat: -35000
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles_tg.rsi
    layers:
    - state: omnilaser
      shader: unshaded

- type: entity
  id: BulletTemperatureHot
  name: hot temperature bolt
  parent: BulletLaser
  categories: [ HideSpawnMenu ]
  description: Burning!
  components:
  - type: Projectile
    damage:
      types:
        Heat: 3
  - type: PointLight
    radius: 2
    color: red
    energy: 1
  - type: ChangeTemperatureOnCollide # This will probably be tweaked.
    heat: 35000


- type: entity
  name: disabler bolt
  id: BulletEnergyGunDisabler
  parent: BulletDisabler
  categories: [ HideSpawnMenu ]
  components:
  - type: Reflective
    reflective:
    - Energy
  - type: FlyBySound
    sound:
      collection: EnergyMiss
      params:
        volume: 5
  - type: Sprite
    sprite: _Goobstation/Objects/Weapons/Guns/Projectiles/energy_bolt.rsi
    layers:
    - state: bolt_blue_heavy
      shader: unshaded
  - type: Physics
  - type: Fixtures
    fixtures:
      projectile:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.15,-0.3,0.15,0.3"
        hard: false
        mask:
        - Opaque
      fly-by: &flybyfixture
        shape: !type:PhysShapeCircle
          radius: 1.5
        layer:
        - Impassable
        - MidImpassable
        - HighImpassable
        - LowImpassable
        hard: False
  - type: Ammo
  - type: StaminaDamageOnCollide
    damage: 33
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Ion: 15
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  name: disabler bolt
  id: BulletSyndicateDisabler
  parent: BulletDisabler
  categories: [ HideSpawnMenu ]
  components:
  - type: StaminaDamageOnCollide
    damage: 27
  - type: Projectile
    impactEffect: BulletImpactEffectDisabler
    damage:
      types:
        Ion: 5
    soundHit:
      collection: WeakHit
    forceSound: true

- type: entity
  id: BulletGrenadeTeargas
  name: tear gas grenade
  parent: BaseBulletTrigger
  categories: [ HideSpawnMenu ]
  components:
  - type: Sprite
    sprite: Objects/Weapons/Guns/Projectiles/projectiles2.rsi
    layers:
      - state: grenade
  - type: SmokeOnTrigger
    duration: 10
    smokePrototype: Smoke
    solution:
      reagents:
      - ReagentId: TearGas
        Quantity: 20
    spreadAmount: 5
  - type: ActiveTimerTrigger
    timeRemaining: 5
  - type: DeleteOnTrigger
  - type: Projectile
    damage:
      types:
        Blunt: 1

- type: entity
  id: HolyBulletCharge
  name: holy bolt
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  description: Marks a target for additional holy damage.
  components:
  - type: Reflective
    reflective:
    - NonEnergy
  - type: Sprite
    noRot: false
    sprite: Objects/Weapons/Guns/Projectiles/magic.rsi
    layers:
    - state: magicm_yellow
      shader: unshaded
  - type: GatheringProjectile
  - type: DamageMarkerOnCollide
    onlyWorkOnFauna: false
    whitelist:
      components:
      - MobState
    damage:
      types:
        Burn: 3
        Holy: 5
    effect: { sprite: /Textures/_ShitChap/Effects/holy_beam.rsi, state: beam }
    sound: /Audio/Effects/holy.ogg
  - type: Projectile
    impactEffect: BulletImpactEffectOrangeDisabler
    damage:
      types:
        Blunt: 0
  # Short lifespan
  - type: TimedDespawn
    lifetime: 0.4

- type: entity
  id: BulletPistolSilver
  name: bullet (.45 silver)
  parent: BaseBullet
  categories: [ HideSpawnMenu ]
  components:
  - type: Projectile
    damage:
      types:
        Piercing: 5
        Holy: 4
