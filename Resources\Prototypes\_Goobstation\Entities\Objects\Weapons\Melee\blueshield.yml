# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2023 Il<PERSON><PERSON><PERSON><PERSON><PERSON>ev <154531074+<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2023 notquitehadouken <1isthisameme>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  name: DT-3 "Atrocity"
  parent: [BaseItem, BaseCentcommContraband]
  id: BlueshieldMace
  description: Crush all those who oppose your leaders. Functions as a stun baton in its powered state.
  components:
  - type: Tag
    tags:
    - Stunbaton
  - type: Sprite
    sprite: _Goobstation/Objects/Weapons/Melee/blueshield_mace.rsi
    layers:
    - state: icon-off
      map: [ "enum.ToggleVisuals.Layer" ]
  - type: ItemToggle
    soundActivate:
      collection: sparks
      params:
        variation: 0.250
    soundDeactivate:
      collection: sparks
      params:
        variation: 0.250
    soundFailToActivate:
      path: /Audio/Machines/button.ogg
      params:
        variation: 0.250
  - type: ItemToggleMeleeWeapon
    activatedDamage:
      types:
        Heat: 1
  - type: Stunbaton
    energyPerUse: 40
    lightAttackEnergyMultiplier: 2
  - type: MeleeWeapon
    angle: 0
    wideAnimationRotation: -135
    damage:
      types:
        Blunt: 13 # Unlucky number
        Slash: 7 # Lucky number
    bluntStaminaDamageFactor: 2 # 26 damage, 4 hit stamcrit
    animation: WeaponArcThrust
    soundHit:
      path: /Audio/_Goobstation/Weapons/Effects/metalcrush.ogg
  - type: StaminaDamageOnHit
    damage: 40
    overtime: 40
    lightAttackDamageMultiplier: 2
    lightAttackOvertimeDamageMultiplier: 0.5
    sound: /Audio/Weapons/egloves.ogg
  - type: StaminaDamageOnCollide
    damage: 15
    overtime: 40
    sound: /Audio/Weapons/egloves.ogg
  - type: LandAtCursor
  - type: Battery
    maxCharge: 600
    startingCharge: 600
  - type: UseDelay
  - type: Item
    heldPrefix: off
    size: Normal
  - type: DisarmMalus
    malus: 0.225
  - type: DelayedKnockdownOnHit
    useDelay: stunbaton
  - type: UseDelayBlockMelee
    delays:
    - stunbaton
  - type: Appearance
  - type: GenericVisualizer
    visuals:
      enum.ToggleVisuals.Toggled:
        enum.ToggleVisuals.Layer:
          True: {state: icon}
          False: {state: icon-off}
  - type: StaticPrice
    price: 1000

- type: entity
  name: combat injector
  parent: [BaseItem, BaseCentcommContraband]
  id: CombatInjector
  description: Injects stored chemicals on hit, has a low capacity due to its sturdy design.
  components:
  - type: Sprite
    sprite: _Goobstation/Objects/Weapons/Melee/blueshield_knife.rsi
    state: icon
  - type: Item
    size: Small
  - type: SolutionContainerManager
    solutions:
      injector:
        maxVol: 5
  - type: Injector
    delay: 1
    maxTransferAmount: 5
    minTransferAmount: 3
    injectOnly: false
  - type: MeleeChemicalInjector
    transferAmount: 5
    solution: injector
    pierceArmor: true
  - type: MeleeWeapon
    angle: 0
    wideAnimationRotation: -135
    damage:
      types:
        Piercing: 5
    animation: WeaponArcThrust
    soundHit:
      path: /Audio/Weapons/bladeslice.ogg
