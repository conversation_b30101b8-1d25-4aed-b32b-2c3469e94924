# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: PlasticPit
  name: plastic pit
  description: Has plastic inside. Don't ask where from.
  suffix: DO NOT MAP
  placement:
    mode: SnapgridCenter
    snap:
    - Wall
  components:
  - type: Transform
    anchored: true
  - type: Clickable
  - type: Sprite
    sprite: Tiles/Planet/Chasms/basalt_chasm.rsi
    drawdepth: BelowFloor
    layers:
    - state: chasm
  - type: Icon
    sprite: Tiles/Planet/Chasms/basalt_chasm.rsi
    state: full
  - type: IconSmooth
    key: chasm
    additionalKeys:
    - walls
    base: chasm
  - type: Physics
    bodyType: Static
  - type: Fixtures
    fixtures:
      fix1:
        shape:
          !type:PhysShapeAabb
          bounds: "-0.5,-0.5,0.5,0.5"
        layer:
        - WallLayer
        mask:
        - ItemMask
        density: 1000
        hard: false
  - type: ItemMiner
    proto: SheetPlastic1
    amount: 30
    miningSound: null
    interval: 10
    needsPower: false
  - type: ItemSlots
    slots:
      miner_slot:
        name: Plastic
        insertSound:
          path: /Audio/Effects/falling.ogg
        ejectSound:
          path: /Audio/_Goobstation/Effects/rising.ogg
        whitelist:
          tags:
          - Plastic
  - type: ContainerContainer
    containers:
      miner_slot: !type:ContainerSlot
        showEnts: False
        occludes: True
        ent: null

- type: entity
  id: PlasteelPit
  parent: PlasticPit
  name: plasteel pit
  description: Has plasteel inside. Don't ask where from.
  components:
  - type: ItemMiner
    proto: SheetPlasteel

- type: entity
  id: Omnilathe
  parent: Autolathe
  name: omnilathe
  description: Produces just about everything researchable and departmental.
  suffix: DO NOT MAP
  components:
  - type: Lathe
    staticPacks:
    # protolathe
    - AdvancedTools
    - ScienceClothing
    - PowerCells
    - Janitor
    - Instruments
    - Equipment
    - FauxTiles
    - SpecOpsGoogles
    - MedicalMisc
    - MedicalVehiclesAdvanced
    - UpgradeKits
    # circuit imprinter
    - EngineeringBoards
    - CargoBoards
    - MedicalBoards
    - ServiceBoards
    - CameraBoards
    # - ShuttleBoards # better to not
    - SharedBoards
    # exofab
    - Robotics
    - BorgModules
    - MechParts
    - MechEquipment
    - MechPartsGoob
    - Modsuits
    - PowerCages
    - BorgModulesGoob
    - Cybernetics
    # secfab - real guns and mech weapons 1984'd
    - SecurityEquipmentStatic
    - SecurityEquipmentStaticGoob
    - SecurityBoardsStaticGoob
    - SecurityPracticeStatic
    - MechWeaponsSecurityStatic
    - MechBoardsSecurity
    - SecurityCybernetics
    # medfab
    - TopicalsStatic
    - ChemistryStatic
    - MedicalStatic
    - MedicalClothingStatic
    - EmptyMedkitsStatic
    - SurgeryStatic
    - ParamedHypoStatic
    - MedicalPatchesStatic
    - SurgeryStaticGoob
    - MedicalBoardsStatic
    - Chemistry
    - Surgery
    - MedicalCybernetics
    - MedicalMiscRestricted
    # emagged uniform printer
    - ClothingCentComm
    - ClothingSyndie
    - ClothingDurathreadArmoured
    - ClothingCasualContrabandJumpsuits
    # engifab
    - EngineeringBoardsStatic
    - AtmosTools
    - EngineeringWeapons
    - BluespaceTheory
    # cargofab
    - MiningToolsStatic
    - Mining
    - MiningWeapons
    - MiningBoards
    - MiningUpgrades
    - MechBoardsCargo
    - MechWeaponsSalvage
    # scifab
    - ScienceBoardsStatic
    - ScienceEquipment
    - ScienceWeapons
    - ScienceBoards
    - MedicalBoardsGoob
    - ExperimentalScienceGoob
    - MechBoardsScience
    - Pinpointers
    # civfab
    - MechBoardsCivilian
    - MechWeaponsCivilian
    - ServiceBoardsEvilStatic
