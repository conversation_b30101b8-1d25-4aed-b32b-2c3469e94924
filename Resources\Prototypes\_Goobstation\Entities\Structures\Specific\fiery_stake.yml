# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: FieryStake
  parent: BaseStructure
  name: fiery stake
  description: But <PERSON> demonstrates his own love for us in this... While we were still sinners, Christ died for us!
  components:
  - type: Sprite
    noRot: true
    sprite: _ShitChap/Structures/bonfire.rsi
    offset: 0,0.5
    layers:
    - state: bonfire
    - state: burning
  - type: PointLight
    radius: 5
    energy: 3
    color: "#FFC90C"
  - type: Damageable
    damageContainer: StructuralInorganic
    damageModifierSet: Wood
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTrigger
        damage: 50
      behaviors:
      - !type:DoActsBehavior
        acts: [ "Destruction" ]
  - type: AmbientSound
    volume: -5
    range: 5
    sound:
      path: /Audio/Ambience/Objects/fireplace.ogg
  - type: Strap
    position: Stand
    buckleOffset: "0,0.25"
  - type: IgniteOnBuckle
    fireStacks: 15
  - type: IgnitionSource
    temperature: 400
    ignited: true
  - type: AlwaysHot
  - type: Construction
    graph: FieryStakeGraph
    node: BonfireNode

- type: construction
  id: FieryStake
  graph: FieryStakeGraph
  startNode: start
  targetNode: BonfireNode
  category: construction-category-furniture
  objectType: Structure
  placementMode: SnapgridCenter
  canBuildInImpassable: false
  conditions:
  - !type:TileNotBlocked
