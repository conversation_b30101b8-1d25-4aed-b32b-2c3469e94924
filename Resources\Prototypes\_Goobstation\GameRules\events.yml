# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: TidemindDevelop
  parent: BaseGameRule
  components:
  - type: GameRule
    chaosScore: 150
  - type: StationEvent
    weight: 3 # rare
    duration: 1
    # no occurences limit so newly arriving tiders can also get it
  - type: JobAddCollectiveMindRule
    message: station-event-tidemind-message
    affected:
      - Passenger
    channel: Tidemind

- type: entity
  parent: BaseGameRule
  id: DerelictCyborgSyndicateSpawn
  components:
  - type: GameRule
    chaosScore: 150
  - type: StationEvent
    weight: 6 # Rare, but not AS rare
    earliestStart: 15
    reoccurrenceDelay: 20
    minimumPlayers: 4
    duration: null
  - type: SpaceSpawnRule
    spawnDistance: 0
  - type: AntagSpawner
    prototype: PlayerBorgDerelictSyndicateAssaultBattery
  - type: AntagSelection
    definitions:
    - spawnerPrototype: SpawnPointGhostDerelictCyborgSyndicate
      min: 1
      max: 1
      pickPlayer: false
