# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entityTable
  id: SecretPlusTable
  table: !type:AllSelector # we need to pass a list of rules, since rules have further restrictions to consider via StationEventComp
    children:
      - !type:NestedSelector
        tableId: SpaceTrafficControlTable
      - !type:NestedSelector
        tableId: BasicGameRulesTable

# extras to pick if primary didn't spend our chaos budget
- type: weightedRandom
  id: SecretPlus
  weights:
    # subgamemodes
    Thief: 0.2
    Devil: 0.1
    # normal
    Traitor: 0.25
    Changeling: 0.10
    Heretic: 0.11

# important note: those (and above) weights aren't entirely accurate,
# as secret+ can randomly decide it's not going to use this gamerule
# if it doesn't entirely fit into its remaining budget,
# so if we rolled 850 budget and a rule takes 1000, we have a 15% chance to skip it
# however the amount of effective budget used is reduced for primary rule so this only matters on low/midpop
- type: weightedRandom
  id: SecretPlusPrimary
  weights:
    Traitor: 0.25
    Changeling: 0.10
    Heretic: 0.11
    Nukeops: 0.05
    Revolutionary: 0.05
    Zombie: 0.04 # in search of how to make a rule always run alone? just tag it with LoneRunRule like zombies are
    BlobGameMode: 0.05
    Wizard: 0.025
    CosmicCult: 0.05

# all antags
- type: weightedRandom
  id: SecretPlusChaos
  weights:
    Thief: 0.2
    Devil: 0.1
    Traitor: 0.25
    Changeling: 0.10
    Heretic: 0.11
    Nukeops: 0.05
    Revolutionary: 0.05
    Zombie: 0.04
    BlobGameMode: 0.05
    Wizard: 0.025
    CosmicCult: 0.05

- type: entity
  id: SecretPlusMid
  parent: BaseGameRule
  categories: [ HideSpawnMenu ]
  components:
  - type: SecretPlus
    # if you've come here due to "too many antags", you probably want to raise playerRatio on the individual antag rules instead
    minStartingChaos: 10 # scales per player
    maxStartingChaos: 20
    livingChaosChange: -0.012
    deadChaosChange: 0.024
    # chance for greenshift as well as evilshift
    chaosChangeVariationMin: 0.65
    chaosChangeVariationMax: 1.3
    # often fires chud events so relatively low
    eventIntervalMin: 90
    eventIntervalMax: 300
    # there's some misc variables in the comp if you want to try tweaking those
  - type: SelectedGameRules
    scheduledGameRules: !type:NestedSelector
      tableId: SecretPlusTable

- type: entity
  id: SecretPlusAdmeme
  parent: SecretPlusMid
  categories: [ HideSpawnMenu ]
  components:
  - type: SecretPlus
    minStartingChaos: 40
    maxStartingChaos: 40 # ling-blob-nukeops is fun
    livingChaosChange: -0.03
    deadChaosChange: 0.03
    chaosExponent: 1
    eventIntervalMin: 60
    eventIntervalMax: 200 # hell
    primaryAntagsWeightTable: SecretPlusChaos
    ignoreIncompatible: true
    ignoreTimings: true # would run out of events otherwise

- type: entity
  id: SecretPlusLow
  parent: SecretPlusMid
  categories: [ HideSpawnMenu ]
  components:
  - type: SecretPlus
    minStartingChaos: 0
    maxStartingChaos: 0
    noRoundstartAntags: true
    livingChaosChange: -0.006
    deadChaosChange: 0.015
    chaosExponent: 1.2
    eventIntervalMin: 120
    eventIntervalMax: 450

# survival+
- type: entity
  id: SecretPlusRampingMid
  parent: SecretPlusMid
  categories: [ HideSpawnMenu ]
  components:
  - type: SecretPlus
    minStartingChaos: 0
    maxStartingChaos: 0
    noRoundstartAntags: true
    speedRamping: 0.0003 # ramps to 2x in about an hour, to 3x in ~2
