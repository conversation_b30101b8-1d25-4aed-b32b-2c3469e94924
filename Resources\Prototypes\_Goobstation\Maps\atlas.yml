# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Atlas
  mapName: Атлас
  mapPath: /Maps/_Goobstation/atlas.yml
  minPlayers: 5
  maxPlayers: 20
  stations:
    Atlas:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Атлас {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'R4' # R4407/Goon. GS isn't as cool sounding.
        - type: StationJobs
          availableJobs:
            #service
            StationAi: [ 1, 1 ]
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2 ]
            Chef: [ 1, 1 ]
            Janitor: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 1, 1 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 2 ]
            StationEngineer: [ 2, 2 ]
            TechnicalAssistant: [ 1, 3 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 1, 1 ]
            MedicalDoctor: [ 2, 2 ]
            MedicalIntern: [ 1, 3 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 2, 2 ]
            ResearchAssistant: [ 1, 3 ]
            Borg: [ 2, 2 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 3, 3 ]
            SecurityCadet: [ 1, 3 ]
            Detective: [ 1, 1 ]
            Brigmedic: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 4, 4 ]
            CargoTechnician: [ 2, 2 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Lawyer: [ 1, 1 ]

        # Goobstation blob-config-start SMALL+
        - type: StationBlobConfig
          stageBegin: 25
          stageCritical: 350
          stageTheEnd: 700
        # Goobstation blob-config-end
