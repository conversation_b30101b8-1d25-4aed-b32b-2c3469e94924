# SPDX-FileCopyrightText: 2021 <PERSON>rod <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 Alex <PERSON> <<EMAIL>>
# SPDX-FileCopyrightText: 2024 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Barratry
  mapName: 'Баратрія'
  mapPath: /Maps/_Goobstation/barratry.yml
  minPlayers: 30
  maxPlayers: 60
  stations:
    Barratry:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Баратрія {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_raven.yml
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Bartender: [ 2, 2 ]
            Botanist: [ 2, 2 ]
            Chef: [ 2, 2 ]
            Janitor: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            Lawyer: [ 2, 2 ]
            ServiceWorker: [ 1, 2 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 3, 3 ]
            StationEngineer: [ 5, 5 ]
            TechnicalAssistant: [ 2, 4 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            MedicalDoctor: [ 4, 4 ]
            MedicalIntern: [ 2, 4 ]
            Paramedic: [ 1, 1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 5, 5 ]
            ResearchAssistant: [2, 4]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 5, 5 ]
            SecurityCadet: [ 2, 4 ]
            Brigmedic: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 6, 6 ]
            CargoTechnician: [ 2, 2 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            #silicon
            StationAi: [ 1, 1 ]

