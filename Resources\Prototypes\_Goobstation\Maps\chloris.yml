# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 Ted <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Chloris
  mapName: 'Хлоріс'
  mapPath: /Maps/_Goobstation/chloris.yml
  minPlayers: 80
  stations:
    Chloris:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Хлоріс {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'LPK'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_wode.yml
        - type: StationJobs
          availableJobs:
            # command
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            # cargo
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 6, 8 ]
            CargoTechnician: [ 5, 6 ]
            # engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 3 ]
            StationEngineer: [ 6, 8 ]
            TechnicalAssistant: [ 3, 4 ]
            # medical
            ChiefMedicalOfficer: [ 1, 1 ]
            MedicalDoctor: [ 6, 8 ]
            MedicalIntern: [ 3, 4 ]
            Psychologist: [ 1, 1 ]
            Paramedic: [ 1, 2 ]
            Chemist: [ 2, 2 ]
            # science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 6, 8 ]
            ResearchAssistant: [ 3, 4 ]
            # security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 7, 9 ]
            SecurityCadet: [ 3, 4 ]
            Detective: [ 1, 1 ]
            Brigmedic: [ 1, 1 ]
            # service
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 3, 4 ]
            Chaplain: [ 1, 1 ]
            Chef: [ 2, 3 ]
            Clown: [ 1, 1 ]
            Janitor: [ 2, 3 ]
            Librarian: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 2 ]
            Reporter: [ 1, 2 ]
            ServiceWorker: [ 3, 4 ]
            Zookeeper: [ 1, 1 ]
            Passenger: [ -1, -1 ]
            # silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 3 ]

      # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
      # backmen blob-config-end
