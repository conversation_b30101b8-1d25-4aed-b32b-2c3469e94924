# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON> <<EMAIL>>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Delta
  mapName: 'Станція Дельта'
  mapPath: /Maps/_Goobstation/delta.yml
  minPlayers: 55
  stations:
    Delta:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Дельта {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'TG'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_delta.yml
        - type: StationJobs
          availableJobs:
            # command
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            # cargo
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 6, 6 ]
            CargoTechnician: [ 5, 6 ]
            # engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 2, 3 ]
            StationEngineer: [ 6, 7 ]
            TechnicalAssistant: [ 3, 3 ]
            # medical
            ChiefMedicalOfficer: [ 1, 1 ]
            MedicalDoctor: [ 4, 5 ]
            MedicalIntern: [ 3, 3 ]
            Psychologist: [ 1, 1 ]
            Paramedic: [ 1, 1 ]
            Chemist: [ 2, 2 ]
            # science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 6, 7 ]
            ResearchAssistant: [ 3, 3 ]
            # security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 6, 7 ]
            SecurityCadet: [ 3, 3 ]
            Detective: [ 1, 1 ]
            Brigmedic: [ 1, 1 ]
            # service
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 1 ]
            Botanist: [ 3, 4 ]
            Boxer: [ 2, 2 ]
            Chaplain: [ 1, 1 ]
            Chef: [ 2, 3 ]
            Clown: [ 1, 1 ]
            Janitor: [ 1, 2 ]
            Lawyer: [ 1, 2 ]
            Librarian: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 2, 2 ]
            Reporter: [ 2, 2 ]
            ServiceWorker: [ 3, 3 ]
            Zookeeper: [ 1, 1 ]
            Passenger: [ -1, -1 ]
            # silicon
            StationAi: [ 1, 1 ]
            Borg: [ 2, 3 ]

        # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # backmen blob-config-end
