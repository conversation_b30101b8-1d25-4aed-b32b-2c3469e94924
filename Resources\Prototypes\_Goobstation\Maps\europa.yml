# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 Aiden<PERSON><PERSON> <<EMAIL>>
# SPDX-FileCopyrightText: 2024 Alice "Arimah" He<PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Europa
  mapName: 'Європа'
  mapPath: /Maps/_Goobstation/europa.yml
  maxRandomOffset: 0
  randomRotation: false
  minPlayers: 0
  maxPlayers: 40
  stations:
    Europa:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationBiome
          biome: Snow
        - type: StationNameSetup
          mapNameTemplate: '{0} Європа {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationJobs
          availableJobs:
            #service
            Bartender: [ 1, 1 ]
            Botanist: [ 2, 2]
            Chef: [ 1, 1 ]
            Janitor: [ 2, 2 ]
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 3, 4 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            StationEngineer: [ 3, 3 ]
            AtmosphericTechnician: [ 2, 2 ]
            TechnicalAssistant: [ 1, 1 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            MedicalDoctor: [ 2, 3 ]
            Chemist: [ 1, 2 ]
            MedicalIntern: [ 1, 1 ]
            Paramedic: [ 1, 2 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 2, 3 ]
            ResearchAssistant: [ 1, 1 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            SecurityOfficer: [ 3, 3 ]
            Warden: [ 1, 1 ]
            Lawyer: [ 1, 1 ]
            SecurityCadet: [ 3, 3 ]
            Detective: [ 1, 1 ]
            #supply
            CargoTechnician: [ 3, 3 ]
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 5, 5 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 1 ]
            Mime: [ 1, 1 ]
            Musician: [ 1, 1 ]
            Reporter: [ 1, 1 ]
