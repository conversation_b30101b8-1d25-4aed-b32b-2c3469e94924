# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: FlandHighPop
  mapName: 'Установка Фланд'
  mapPath: /Maps/_Goobstation/fland.yml
  minPlayers: 85
  stations:
    Fland:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Установка Фланд {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'B'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_delta.yml
        - type: StationCargoShuttle
          path: /Maps/Shuttles/cargo_fland.yml
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 3, 3 ]
            Botanist: [ 5, 5 ]
            Chef: [ 4, 4 ]
            Janitor: [ 6, 6 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 2, 2 ]
            ServiceWorker: [ 4, 4 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 5, 5 ]
            StationEngineer: [ 8, 8 ]
            TechnicalAssistant: [ -1, -1 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 3, 3 ]
            MedicalDoctor: [ 10, 10 ]
            Paramedic: [ 3, 3 ]
            MedicalIntern: [ -1, -1 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 10, 10 ]
            ResearchAssistant: [ -1, -1 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 8, 8 ]
            Detective: [ 1, 1 ]
            SecurityCadet: [ -1, -1 ]
            Lawyer: [ 3, 3 ]
            Brigmedic: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 8, 8 ]
            CargoTechnician: [ -1, -1 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 2, 2 ]
            Mime: [ 2, 2 ]
            Musician: [ 3, 3 ]
            #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 4, 4 ]

        # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # backmen blob-config-end
