# SPDX-FileCopyrightText: 2021 <PERSON>rod <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Kettle
  mapName: 'Чайник'
  mapPath: /Maps/_Goobstation/kettle.yml
  minPlayers: 35
  stations:
    Kettle:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Чайник {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: '14'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_courser.yml
        - type: StationJobs
          availableJobs:
            #service
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            Captain: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 1, 2 ]
            Botanist: [ 3, 4 ]
            Chef: [ 2, 3 ]
            Janitor: [ 4, 4 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 2 ]
            Lawyer: [ 2, 3 ]
            Zookeeper: [ 1, 1 ]
            ServiceWorker: [ 4, 4 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 3, 3 ]
            StationEngineer: [ 4, 6 ]
            TechnicalAssistant: [ 8, 8 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 2, 3 ]
            Paramedic: [ 2, 2 ]
            MedicalDoctor: [ 3, 5 ]
            MedicalIntern: [ 8, 8 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 4, 6 ]
            ResearchAssistant: [ 8, 8 ]
            Borg: [ 2, 2 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            Detective: [ 1, 1 ]
            SecurityOfficer: [ 4, 6 ]
            SecurityCadet: [ 8, 8 ]
            Brigmedic: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 6, 6 ]
            CargoTechnician: [ 3, 3 ]
            #civillian
            Passenger: [ -1, -1 ]
            Clown: [ 1, 2 ]
            Mime: [ 1, 2 ]
            Musician: [ 1, 2 ]
            StationAi: [ 1, 1 ]

        # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # Goobstation blob-config-end
