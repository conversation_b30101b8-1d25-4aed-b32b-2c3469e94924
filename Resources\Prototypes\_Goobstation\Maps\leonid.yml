# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Leonid
  mapName: 'Станція Леонід'
  mapPath: /Maps/_Goobstation/leonid.yml
  minPlayers: 35
  stations:
    Leonid:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: '{0} Станція Леонід {1}'
          nameGenerator:
            !type:NanotrasenNameGenerator
            prefixCreator: 'B'
        - type: StationEmergencyShuttle
          emergencyShuttlePath: /Maps/Shuttles/emergency_delta.yml
        - type: StationCargoShuttle
          path: /Maps/Shuttles/cargo_fland.yml
        - type: StationJobs
          availableJobs:
            #service
            Captain: [ 1, 1 ]
            NanotrasenRepresentative: [ 1, 1 ]
            BlueshieldOfficer: [ 1, 1 ]
            HeadOfPersonnel: [ 1, 1 ]
            Bartender: [ 2, 2 ]
            Botanist: [ 3, 3 ]
            Chef: [ 3, 3 ]
            Janitor: [ 3, 3 ]
            Chaplain: [ 1, 1 ]
            Librarian: [ 1, 1 ]
            ServiceWorker: [ 2, 2 ]
            #engineering
            ChiefEngineer: [ 1, 1 ]
            AtmosphericTechnician: [ 4, 4 ]
            StationEngineer: [ 6, 6 ]
            TechnicalAssistant: [ 6, 6 ]
            #medical
            ChiefMedicalOfficer: [ 1, 1 ]
            Chemist: [ 3, 3 ]
            MedicalDoctor: [ 6, 6 ]
            Paramedic: [ 2, 2 ]
            MedicalIntern: [ 6, 6 ]
            #science
            ResearchDirector: [ 1, 1 ]
            Scientist: [ 6, 6 ]
            ResearchAssistant: [ 6, 6 ]
            #security
            HeadOfSecurity: [ 1, 1 ]
            Warden: [ 1, 1 ]
            SecurityOfficer: [ 6, 6 ]
            Detective: [ 1, 1 ]
            SecurityCadet: [ 6, 6 ]
            Lawyer: [ 3, 3 ]
            Brigmedic: [ 1, 1 ]
            #supply
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ 6, 6 ]
            CargoTechnician: [ 5, 5 ]
            #civilian
            Passenger: [ -1, -1 ]
            Clown: [ 2, 2 ]
            Mime: [ 2, 2 ]
            Musician: [ 3, 3 ]
            #silicon
            StationAi: [ 1, 1 ]
            Borg: [ 4, 4 ]

        # Goobstation blob-config-start HUGE
        - type: StationBlobConfig
          stageBegin: 40
          stageCritical: 450
          stageTheEnd: 900
        # backmen blob-config-end
