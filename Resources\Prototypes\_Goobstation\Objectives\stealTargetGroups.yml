# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: stealTargetGroup
  id: SupermatterSliver
  name: steal-target-groups-cargo-supermatter-sliver
  sprite:
    sprite: _Goobstation/Supermatter/supermatter_sliver.rsi
    state: icon

- type: stealTargetGroup
  id: WeaponEnergyGunLawbringer
  name: steal-target-groups-weapon-lawbringer
  sprite:
    sprite: _Goobstation/Objects/Weapons/Guns/Battery/lawbringer.rsi
    state: icon

- type: stealTargetGroup
  id: RapidSyringeGun
  name: steal-target-groups-rapid-syringe-gun
  sprite:
    sprite: _Goobstation/Objects/Weapons/Guns/Cannons/rapid_syringe_gun.rsi
    state: icon

- type: stealTargetGroup
  id: ClothingBeltGeminiHoloProjector
  name: steal-target-groups-gemini-projector
  sprite:
    sprite: _Goobstation/Clothing/Belt/geminiprojector.rsi
    state: icon
