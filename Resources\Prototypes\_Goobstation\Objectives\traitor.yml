# SPDX-FileCopyrightText: 2024 Aiden<PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2024 whateverusername0 <whateveremail>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
# Kill

# random alive traitor
- type: entity
  parent: [BaseTraitorObjective, BaseKillObjective]
  id: PermaKillRandomTraitorObjective
  description: Do it however you like, as long as they're not breathing anymore.
  components:
  - type: Objective
    difficulty: 2.5 # Most cases tots are armed themselves so have fun
    unique: false
  - type: TargetObjective
    title: objective-condition-pkill-person
  - type: PickRandomTraitor
  - type: KillPersonCondition
    requireDead: true
  - type: MultipleTraitorsRequirement

# random person
# This is a separate objective to prevent metagaming by having a different condition for killing traitors, by also requiring you just kill someone entirely
- type: entity
  parent: [BaseTraitorObjective, BaseKillObjective]
  id: PermaKillRandomPersonObjective
  description: Do it however you like, as long as they're not breathing anymore.
  components:
  - type: Objective
    difficulty: 1.75
    unique: false
  - type: TargetObjective
    title: objective-condition-pkill-person
  - type: PickRandomPerson
  - type: KillPersonCondition
    requireDead: true

# Protect

# another traitor's target
# Note: The possibility of having both protect and kill objectives on the same person is intentional
# to cause kino situations where the traitor kills the person they were protecting at the end of the round
- type: entity
  parent: [BaseTraitorSocialObjective, BaseKeepAliveObjective]
  id: RandomTraitorTargetAliveObjective
  description: Rival agents intend to assassinate this target. Identify yourself at your own risk.
  components:
  - type: Objective
    difficulty: 1 # This can be either protect HoS (objective solves itself) or having some tider die in maints silently. Difficulty is set to 1 to have other objectives.
  - type: TargetObjective
    title: objective-condition-traitor-target-alive-title
  - type: RandomTraitorTarget
  - type: MultipleTraitorsRequirement

# Steal

- type: entity
  categories: [ HideSpawnMenu ]
  parent: BaseTraitorStealObjective
  id: StealSupermatterSliverObjective
  components:
  - type: Objective
    difficulty: 3.5
  - type: StealCondition
    verifyMapExistence: true
    stealGroup: SupermatterSliver
    objectiveNoOwnerText: objective-condition-steal-smsliver-title
    descriptionText: objective-condition-steal-smsliver-description

- type: entity
  parent: BaseTraitorStealObjective
  id: LawbringerStealObjective
  components:
  - type: Objective
    # HoS will have this on them a lot of the time so..
    difficulty: 3
  - type: NotJobRequirement
    job: HeadOfSecurity
  - type: StealCondition
    stealGroup: WeaponEnergyGunLawbringer
    owner: job-name-hos

- type: entity
  parent: BaseCMOStealObjective
  id: CMORapidSyringeGunStealObjective
  components:
  - type: StealCondition
    stealGroup: RapidSyringeGun
  - type: Objective
    difficulty: 3 # Slightly more than normal as it does not fit into storage implant

- type: entity
  parent: BaseRDStealObjective
  id: GeminiProjectorStealObjective
  components:
  - type: NotJobRequirement
    job: ResearchDirector
  - type: StealCondition
    stealGroup: ClothingBeltGeminiHoloProjector
    descriptionText: objective-condition-steal-gemini-projector-description
  - type: Objective
    # This bad boy is LITERALLY directly screwed into the RDs spinal cord. Good luck!
    difficulty: 3.5
