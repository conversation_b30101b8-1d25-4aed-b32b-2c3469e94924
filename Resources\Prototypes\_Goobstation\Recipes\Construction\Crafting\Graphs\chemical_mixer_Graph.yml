# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: portableChemicalMixerGraph
  start: start
  graph:
  - node: start
    edges:
    - to: end
      steps:
      - tag: Bucket # general mixing and container
        icon:
          sprite: Objects/Tools/bucket.rsi
          state: icon
        name: construction-graph-tag-bucket
      - tag: Multitool  # for the screen
        icon:
          sprite: Objects/Tools/multitool.rsi
          state: icon
        name: construction-graph-tag-multitool
      - tag: GlassBeaker
        icon:
          sprite: Objects/Specific/Chemistry/beaker.rsi
          state: beaker
        name: construction-graph-tag-beaker
      - material: Cable
        amount: 15
      - anyTags:        # Powers the contraption
        - PowerCell
        - PowerCellSmall
        name: construction-graph-tag-power-cell
        icon:
          sprite: "Objects/Power/power_cells.rsi"
          state: "medium"
        doAfter: 5

  - node: end
    entity: portableChemicalMixer
