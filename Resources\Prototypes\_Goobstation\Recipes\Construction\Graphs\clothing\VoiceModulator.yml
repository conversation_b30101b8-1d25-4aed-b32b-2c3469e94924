# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: VoiceMaskCraftable
  start: start
  graph:
    - node: start
      edges:
        - to: end
          steps:
            - tag: GasMaskFullDefault
              name: construction-graph-tag-gas-mask
              icon:
                sprite: Clothing/Mask/gas.rsi
                state: icon

            - tag: VoiceTrigger
              name: construction-graph-tag-voice-trigger
              icon:
                sprite: Objects/Devices/voice.rsi
                state: voice

            - material: Cable
              amount: 5
              doAfter: 15
    - node: end
      entity: ClothingMaskGasVoiceCraftable
