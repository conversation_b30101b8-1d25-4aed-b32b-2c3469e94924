# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: GlassesMedDiagHUD
  start: start
  graph:
    - node: start
      edges:
        - to: glassesMedDiag
          steps:
            - tag: Sunglasses
              name: construction-graph-tag-sun-glasses
              icon:
                sprite: Clothing/Eyes/Glasses/sunglasses.rsi
                state: icon
              doAfter: 5
            - tag: HudMedicalDiagnostic
              name: construction-graph-tag-meddiag-hud
              icon:
                sprite: _Goobstation/Clothing/Eyes/Hud/meddiag.rsi
                state: icon
              doAfter: 5
            - material: Cable
              amount: 5
              doAfter: 5

    - node: glassesMedDiag
      entity: ClothingEyesGlassesMedDiag
      edges:
      - to: start
        steps:
          - tool: Screwing
            doAfter: 5
        completed:
        - !type:SpawnPrototype
          prototype: ClothingEyesGlassesSunglasses
        - !type:SpawnPrototype
          prototype: ClothingEyesHudDiagnosticMedical
        - !type:DeleteEntity
