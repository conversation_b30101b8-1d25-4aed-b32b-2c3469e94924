# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: GlassesMedSecHUD
  start: start
  graph:
    - node: start
      edges:
        - to: glassesMedSec
          steps:
            - tag: Sunglasses
              name: construction-graph-tag-sun-glasses
              icon:
                sprite: Clothing/Eyes/Glasses/sunglasses.rsi
                state: icon
              doAfter: 5
            - tag: HudMedicalSecurity
              name: construction-graph-tag-medsec-hud
              icon:
                sprite: Clothing/Eyes/Hud/medsec.rsi
                state: icon
              doAfter: 5
            - material: Cable
              amount: 5
              doAfter: 5

    - node: glassesMedSec
      entity: ClothingEyesGlassesMedSec
      edges:
      - to: start
        steps:
          - tool: Screwing
            doAfter: 5
        completed:
        - !type:SpawnPrototype
          prototype: ClothingEyesGlassesSunglasses
        - !type:SpawnPrototype
          prototype: ClothingEyesHudMedSec
        - !type:DeleteEntity
