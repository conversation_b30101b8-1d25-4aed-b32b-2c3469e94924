# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: Modsuit
  start: start
  graph:
  - node: start
    entity: ModsuitShell
    edges:
    - to: shell-core
      steps:
      - tag: ModsuitCore
        name: construction-graph-tag-mod-core
        store: core-container
        completed:
        - !type:PlaySound
          sound: /Audio/Items/screwdriver2.ogg

  - node: shell-core
    actions:
    - !type:AppearanceChange
    edges:
    - to: start
      steps:
      - tool: Prying
        completed:
          - !type:EmptyContainer
            container: core-container
    - to: shell-core-secured
      steps:
      - tool: Screwing
        doAfter: 1

  - node: shell-core-secured
    actions:
    - !type:AppearanceChange
    edges:
    - to: shell-helmet
      steps:
      - tag: ModsuitHelmet
        name: construction-graph-tag-mod-helmet
        doAfter: 1
        completed:
        - !type:PlaySound
          sound: /Audio/Items/screwdriver2.ogg

  - node: shell-helmet
    actions:
    - !type:AppearanceChange
    edges:
    - to: shell-chestplate
      steps:
      - tag: ModsuitChestplate
        name: construction-graph-tag-mod-chestplate
        doAfter: 1
        completed:
        - !type:PlaySound
          sound: /Audio/Items/screwdriver2.ogg

  - node: shell-chestplate
    actions:
    - !type:AppearanceChange
    edges:
    - to: shell-gauntlets
      steps:
      - tag: ModsuitGauntlets
        name: construction-graph-tag-mod-gauntlets
        doAfter: 1
        completed:
        - !type:PlaySound
          sound: /Audio/Items/screwdriver2.ogg

  - node: shell-gauntlets
    actions:
    - !type:AppearanceChange
    edges:
    - to: shell-boots
      steps:
      - tag: ModsuitBoots
        name: construction-graph-tag-mod-boots
        doAfter: 1
        completed:
        - !type:PlaySound
          sound: /Audio/Items/screwdriver2.ogg

  - node: shell-boots
    actions:
    - !type:AppearanceChange
    edges:
    - to: shell-secured
      steps:
      - tool: Anchoring
        doAfter: 1

      - tool: Screwing
        doAfter: 1

  - node: shell-secured
    actions:
    - !type:AppearanceChange
    edges:
    - to: standard
      steps:
        - tag: ModsuitPlatingExternal
          name: construction-graph-tag-mod-plating
          doAfter: 1
          completed:
          - !type:PlaySound
            sound: /Audio/Items/screwdriver2.ogg

        - tool: Anchoring
          doAfter: 1

  - node: standard
    entity: ClothingModsuitStandard

  - node: security
    entity: ClothingModsuitSecurity

  - node: paramedic
    entity: ClothingModsuitParamedic

  - node: headofsecurity
    entity: ClothingModsuitHeadOfSecurity

  - node: engineer
    entity: ClothingModsuitEngineer

  - node: atmostech
    entity: ClothingModsuitAtmostech

  - node: rd
    entity: ClothingModsuitResearchDirector

  - node: captain
    entity: ClothingModsuitCaptain
