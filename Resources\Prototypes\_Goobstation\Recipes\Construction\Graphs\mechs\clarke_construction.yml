# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: Clarke
  start: start
  graph:
  - node: start
    edges:
    - to: clarke
      steps:
      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 1

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 2

      - material: Cable
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 3

      - tool: Cutting
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 4

      - tag: ClarkeCentralControlModule
        name: construction-graph-tag-clarke-central-control-module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "mainboard"
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 5

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 6

      - tag: ClarkePeripheralsControlModule
        name: construction-graph-tag-clarke-peripherals-control-module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: id_mod
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 7

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 8

      - tag: CapacitorStockPart
        name: construction-graph-tag-capacitor
        icon:
          sprite: Objects/Misc/stock_parts.rsi
          state: capacitor
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 9

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 10

      - component: PowerCell
        name: construction-graph-component-power-cell
        store: battery-container
        icon:
          sprite: Objects/Power/power_cells.rsi
          state: small
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 11

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 12

      - material: Steel
        amount: 5
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 14

      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 15

      - tool: Welding
        doAfter: 1

      - material: Gold
        amount: 5
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 16

      - tool: Anchoring
        doAfter: 1

      - tag: MechAirTank
        name: construction-graph-tag-exosuit-air-tank
        icon:
          sprite: Objects/Specific/Mech/mecha_equipment.rsi
          state: mecha_air_tank

      - tool: Anchoring
        doAfter: 1

      - tag: MechThruster
        name: construction-graph-tag-exosuit-thruster
        icon:
          sprite: Objects/Specific/Mech/mecha_equipment.rsi
          state: mecha_bin

      - tool: Anchoring
        doAfter: 1

      - tool: Welding
        doAfter: 1

  - node: clarke
    actions:
    - !type:BuildMech
      mechPrototype: MechClarke
