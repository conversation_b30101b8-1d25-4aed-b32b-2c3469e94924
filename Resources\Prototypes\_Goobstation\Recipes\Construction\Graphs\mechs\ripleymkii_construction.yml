# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: constructionGraph
  id: RipleyMKII
  start: start
  graph:
  - node: start
    edges:
    - to: ripleymkii
      steps:
      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 1

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 2

      - material: Cable
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 3

      - tool: Cutting
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 4

      - tag: RipleyCentralControlModule
        name: construction-graph-tag-ripley-central-control-module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: "mainboard"
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 5

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 6

      - tag: RipleyPeripheralsControlModule
        name: construction-graph-tag-ripley-peripherals-control-module
        icon:
          sprite: "Objects/Misc/module.rsi"
          state: id_mod
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 7

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 8

      - component: PowerCell
        name: construction-graph-component-power-cell
        store: battery-container
        icon:
          sprite: Objects/Power/power_cells.rsi
          state: small
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 9

      - tool: Screwing
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 12

      - material: Steel
        amount: 5
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 15

      - tool: Anchoring
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 16

      - tool: Welding
        doAfter: 1
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 17

      - tag: MechAirTank
        name: construction-graph-tag-exosuit-air-tank
        icon:
          sprite: Objects/Specific/Mech/mecha_equipment.rsi
          state: mecha_air_tank

      - tool: Anchoring
        doAfter: 1

      - material: Plasteel
        amount: 10
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 19

      - tool: Anchoring
        doAfter: 2
        completed:
        - !type:VisualizerDataInt
          key: "enum.MechAssemblyVisuals.State"
          data: 20

      - tool: Welding
        doAfter: 1

  - node: ripleymkii
    actions:
    - !type:BuildMech
      mechPrototype: MechRipley2
