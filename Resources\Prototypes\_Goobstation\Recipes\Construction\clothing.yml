# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: construction
  id: GoliathCloak
  graph: GoliathCloak
  startNode: start
  targetNode: cloak
  category: construction-category-clothing
  objectType: Item

- type: construction
  id: makeshiftvoicechanger
  graph: VoiceMaskCraftable
  startNode: start
  targetNode: end
  category: construction-category-clothing
  objectType: Item

- type: construction
  id: MedicalGlasses
  graph: GlassesMedHUD
  startNode: start
  targetNode: glassesMed
  category: construction-category-clothing
  objectType: Item

- type: construction
  id: MedicalSecurityGlasses
  graph: GlassesMedSecHUD
  startNode: start
  targetNode: glassesMedSec
  category: construction-category-clothing
  objectType: Item

- type: construction
  id: GlassesMedDiag
  graph: GlassesMedDiagHUD
  startNode: start
  targetNode: glassesMedDiag
  category: construction-category-clothing
  objectType: Item

- type: construction
  id: HudMedDiagHUD
  graph: HudMedDiag
  startNode: start
  targetNode: meddiagHud
  category: construction-category-clothing
  objectType: Item
