# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: construction
  id: AutomationFilterLabel
  graph: AutomationFilter
  startNode: start
  targetNode: label
  category: construction-category-misc
  objectType: Item

- type: construction
  id: AutomationFilterName
  graph: AutomationFilter
  startNode: start
  targetNode: name
  category: construction-category-misc
  objectType: Item

- type: construction
  id: AutomationFilterStack
  graph: AutomationFilter
  startNode: start
  targetNode: stack
  category: construction-category-misc
  objectType: Item

- type: construction
  id: AutomationFilterPressure
  graph: AutomationFilter
  startNode: start
  targetNode: pressure
  category: construction-category-misc
  objectType: Item

- type: construction
  id: AutomationFilterCombined
  graph: AutomationFilter
  startNode: start
  targetNode: label
  category: construction-category-misc
  objectType: Item
