# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
## Static
- type: latheRecipePack
  id: ParamedHypoStatic
  recipes:
  - CartridgeAtropine
  - CartridgeEpinephrine
  - CartridgeBicaridine
  - CartridgeDermaline
  - CartridgeSaline
  - ParamedHypo

- type: latheRecipePack
  id: MedicalPatchesStatic
  recipes:
  - MedicalPatchBasic
  - MedicalPatchRapid
  - MedicalPatchTherapeutic
  - MedicalPatchLarge

- type: latheRecipePack
  id: SurgeryStaticGoob
  recipes:
  - BoneGel
  - MedicalStitches
  - Bonesetter

- type: latheRecipePack
  id: MedicalVehiclesStatic
  recipes:
  - VehicleWheelchairFolded
  - Cane

- type: latheRecipePack
  id: MedicalVehiclesAdvanced
  recipes:
  - VehicleHoverchairSci

## Dynamic
- type: latheRecipePack
  id: Surgery
  recipes:
  - EnergyScalpel
  - AdvancedRetractor
  - EnergyCautery
  - AdvancedBoneGel
  - OmnimedTool

- type: latheRecipePack
  id: MedicalCybernetics
  recipes:
  - MedicalCyberneticEyes

- type: latheRecipePack
  id: MedicalMisc
  recipes:
  - PrescriptionGlasses
  - VehicleHoverchairSci

- type: latheRecipePack
  id: MedicalMiscRestricted
  recipes:
  - SyringeGun
  - HandheldCrewMonitor
  - DefibrillatorCompact
  - ClothingHandsGlovesSurgical
