# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 Ted <PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
## Static
- type: latheRecipePack
  id: IPCPartsStatic
  recipes:
  - ChestIPC
  - GroinIPC
  - LeftArmIPC
  - RightArmIPC
  - LeftLegIPC
  - RightLegIPC
  - HeadIPC
  - LeftHandIPC
  - RightHandIPC
  - LeftFootIPC
  - RightFootIPC
  - OrganIPCEyes
  - OrganIPCPump

- type: latheRecipePack
  id: BorgModulesStaticGoob
  recipes:
  - BorgModuleSurgery

- type: latheRecipePack
  id: PowerCagesStatic
  recipes:
  - PowerCageMedium
  - PowerCageSmall

## Dynamic
- type: latheRecipePack
  id: Modsuits
  recipes:
  - ModsuitChestplate
  - ModsuitBoots
  - ModsuitHelmet
  - ModsuitGauntlets
  - ModsuitShell
  - ModsuitPlatingExternal

- type: latheRecipePack
  id: BorgModulesGoob
  recipes:
  - BorgModuleAdvancedSurgery
  - BorgModuleLollypop

- type: latheRecipePack
  id: PowerCages
  recipes:
  - PowerCageHigh

- type: latheRecipePack
  id: Cybernetics
  recipes:
  - JawsOfLifeLeftArm
  - JawsOfLifeRightArm
  - SpeedLeftLeg
  - SpeedRightLeg
  - DexLeftHand
  - DexRightHand
  - BasicCyberneticEyes
