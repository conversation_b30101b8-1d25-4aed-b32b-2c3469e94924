# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: latheRecipe
  id: VehicleWheelchairFolded
  result: VehicleWheelchairFolded
  materials:
    Plastic: 500
    Steel: 500

- type: latheRecipe
  id: PrescriptionGlasses
  result: ClothingEyesPrescriptionGlasses
  completetime: 3
  materials:
    Steel: 500
    Glass: 500

- type: latheRecipe
  id: Cane
  result: Cane
  completetime: 3
  materials:
    Wood: 100

- type: latheRecipe
  id: VehicleHoverchairSci
  result: VehicleHoverchair
  completetime: 5
  materials:
    Steel: 500
    Gold: 300
    Plasma: 250

- type: latheRecipe
  id: SyringeGun
  result: SyringeGun
  completetime: 5
  materials:
    Steel: 500
    Glass: 500
    Plastic: 500
    Gold: 100
    Plasma: 100

- type: latheRecipe
  id: ParamedHypo
  result: ParamedHypo
  completetime: 3
  materials:
    Steel: 1500
    Plastic: 800
    Glass: 1200
    Gold: 500

- type: latheRecipe
  id: HandheldCrewMonitor
  result: HandheldCrewMonitorEmpty
  completetime: 5
  materials:
    Steel: 500
    Glass: 200
    Plastic: 200
    Gold: 100

- type: latheRecipe
  id: DefibrillatorCompact
  result: DefibrillatorCompactEmpty
  completetime: 5
  materials:
    Steel: 250
    Silver: 50