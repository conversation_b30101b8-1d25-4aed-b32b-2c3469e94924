# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: PlayerBorgGeneric
  parent: BorgChassisGeneric
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgMining
  parent: BorgChassisMining
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgEngineer
  parent: BorgChassisEngineer
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgJanitor
  parent: BorgChassisJanitor
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgMedical
  parent: BorgChassisMedical
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgService
  parent: BorgChassisService
  suffix: Battery, Module
  components:
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper

- type: entity
  id: PlayerBorgSyndicateMedicalBattery
  parent: BorgChassisSyndicateMedical
  suffix: Battery, Module, Operative
  components:
  - type: NukeOperative
  - type: ContainerFill
    containers:
      borg_brain:
        - PositronicBrain
      borg_module:
        - BorgModuleOperative
        - BorgModuleAdvancedSurgery
        - BorgModuleChemical
        - BorgModuleRescue
        - BorgModuleTopicals
  - type: ItemSlots
    slots:
      cell_slot:
        name: power-cell-slot-component-slot-name-default
        startingItem: PowerCellHyper
