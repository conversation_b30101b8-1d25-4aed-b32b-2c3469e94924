# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
# Alphabetical order people!

- type: Tag
  id: ArrythmicKnife

- type: Tag
  id: AthiestsFedora

- type: Tag
  id: AntagGateway

- type: Tag
  id: AutoinjectorCartridge

- type: Tag
  id: BindSoulAction

- type: Tag
  id: Bible

- type: Tag
  id: Blobbernaut

- type: Tag
  id: BlockLightning

- type: Tag
  id: BlueprintMedicalFabricator

- type: Tag
  id: BolaEnergy

- type: Tag
  id: BudgetInsuls

- type: Tag
  id: BreadEmergency

- type: Tag
  id: BreathMask

- type: Tag
  id: CartridgeHighCaliber

- type: Tag
  id: CartridgeLowCaliber

- type: Tag
  id: CartridgeMagicBullet

- type: Tag
  id: CartridgeSmart

- type: Tag
  id: CartridgeG8

- type: Tag
  id: CannotSuicideAny

- type: Tag
  id: Chair

- type: Tag
  id: ClothingBeltGeminiHoloProjector

- type: Tag
  id: Clarke

- type: Tag
  id: ClarkeCentralControlModule

- type: Tag
  id: ClarkeHead

- type: Tag
  id: ClarkeLArm

- type: Tag
  id: ClarkePeripheralsControlModule

- type: Tag
  id: ClarkeRArm

- type: Tag
  id: ClarkeTreads

- type: Tag
  id: ClownEmergencyOxygenTank

- type: Tag
  id: CombatMech

- type: Tag
  id: ContrabandDetectorCircuitboard

- type: Tag
  id: CursedAnimalMask

- type: Tag
  id: Durand

- type: Tag
  id: DurandArmor

- type: Tag
  id: DurandCentralControlModule

- type: Tag
  id: DurandHead

- type: Tag
  id: DurandLArm

- type: Tag
  id: DurandLLeg

- type: Tag
  id: DurandPeripheralsControlModule

- type: Tag
  id: DurandRArm

- type: Tag
  id: DurandRLeg

- type: Tag
  id: DurandTargetingControlModule

- type: Tag
  id: EmergencyMedipen

- type: Tag
  id: EmergencyNitrogenTank

- type: Tag
  id: EmergencyOxygenTank

- type: Tag
  id: EnergySpeedloader

- type: Tag
  id: EnergySpeedloaderDisabler

- type: Tag
  id: EnergySpeedloaderLethal

- type: Tag
  id: EnergySpeedloaderPulse

- type: Tag
  id: ExtendedEmergencyOxygenTank

- type: Tag
  id: Eyes

- type: Tag
  id: EnergyMagAmmo

- type: Tag
  id: FelinidEmotes

- type: Tag
  id: FitsInCharger

- type: Tag
  id: FlashIgnoreResistances

- type: Tag
  id: FlashVulnerable

- type: Tag
  id: FollowerStayOnPolymorph

- type: Tag
  id: FrozenIgnoreMindAction

- type: Tag
  id: GasMask

- type: Tag
  id: GasMaskFullDefault

- type: Tag
  id: GodHand

- type: Tag
  id: GorillaFormAction

- type: Tag
  id: Gygax

- type: Tag
  id: GygaxArmor

- type: Tag
  id: GygaxCentralControlModule

- type: Tag
  id: GygaxHead

- type: Tag
  id: GygaxLArm

- type: Tag
  id: GygaxLLeg

- type: Tag
  id: GygaxPeripheralsControlModule

- type: Tag
  id: GygaxRArm

- type: Tag
  id: GygaxRLeg

- type: Tag
  id: GygaxTargetingControlModule

- type: Tag
  id: GunCanAimShooter

- type: Tag
  id: Hammer

- type: Tag
  id: HardHat

- type: Tag
  id: HandheldCrewMonitor

- type: Tag
  id: HarpyEmotes

- type: Tag
  id: Heart

- type: Tag
  id: HereticBlade

- type: Tag
  id: HereticBladeBlade

- type: Tag
  id: HereticItem

- type: Tag
  id: HEVSuit

- type: Tag
  id: HolyClaymore

- type: Tag
  id: HonkmotherMaw

- type: Tag
  id: HydroponicsToolClippers

- type: Tag
  id: IgnoreBindSoul

- type: Tag
  id: IgnoreImmovableRod

- type: Tag
  id: ItemPickaxe

- type: Tag
  id: IndustrialMech

- type: Tag
  id: JanicartKeys

- type: Tag
  id: Justice

- type: Tag
  id: Kidneys

- type: Tag
  id: LaserLMG

- type: Tag
  id: LaserLMGBackpack

- type: Tag
  id: Liver

# tag roundstart rules that should be run alone
- type: Tag
  id: LoneRunRule

- type: Tag
  id: Lungs

- type: Tag
  id: MagazineAntiMateriel

- type: Tag
  id: MagazineHighCaliber

- type: Tag
  id: MagazineLowCaliberM7S

- type: Tag
  id: MagazineMagnumLeverRifle

- type: Tag
  id: MagazineNailgun

- type: Tag
  id: MagazineShotgunHeavy

- type: Tag
  id: MagazineSmart

- type: Tag
  id: MantisBlade

- type: Tag
  id: Mask

- type: Tag
  id: MechAirTank

- type: Tag
  id: MechThruster

- type: Tag
  id: MedicalPatch

- type: Tag
  id: MilitaryPowerCell

- type: Tag
  id: MilitaryPowerCellLMG

- type: Tag
  id: MilitaryPowerCellRevolver

- type: Tag
  id: MilitaryPowerCellSniper

- type: Tag
  id: MilitaryPowerCellSMG

- type: Tag
  id: ModsuitBoots

- type: Tag
  id: ModsuitChestplate

- type: Tag
  id: ModsuitCore

- type: Tag
  id: ModsuitGauntlets

- type: Tag
  id: ModsuitHelmet

- type: Tag
  id: ModsuitPart

- type: Tag
  id: ModsuitPlatingExternal

- type: Tag
  id: ModsuitShell

- type: Tag
  id: MonkStaff

- type: Tag
  id: Muzzle

- type: Tag
  id: Nail

- type: Tag
  id: Nailgun

- type: Tag
  id: NonLethalGrenade

- type: Tag
  id: NukeOpsCommanderUplink

- type: Tag
  id: NukeOpsMedicUplink

- type: Tag
  id: NukeOpsReinforcementUplink

- type: Tag
  id: Nullrod

- type: Tag
  id: NunHood

- type: Tag
  id: Organ

- type: Tag
  id: PepperSpray

- type: Tag
  id: Poppy

- type: Tag
  id: ReptilianEmotes

- type: Tag
  id: RipleyMKIIUpgradeKit

- type: Tag
  id: RipleyMkII

- type: Tag
  id: Rosary

- type: Tag
  id: SealedAirlock

- type: Tag
  id: SealedGlassAirlock

- type: Tag
  id: SecurityBreathMask

- type: Tag
  id: ShadowCloakAction

- type: Tag
  id: ShowWizardIcons

- type: Tag
  id: SiliconMob

- type: Tag
  id: SnapPop

- type: Tag
  id: SoulTapped

- type: Tag
  id: SpaceMedipen

- type: Tag
  id: SpecialMech

- type: Tag
  id: StasisBattery

- type: Tag
  id: StasisCage

- type: Tag
  id: Steel

- type: Tag
  id: Stomach

- type: Tag
  id: SummonSimiansMaxLevelAction

- type: Tag
  id: SyndicateBreathMask

- type: Tag
  id: ShellShotgunHeavier

- type: Tag
  id: ShellShotgunHeavy

- type: Tag
  id: SyringeArmor

- type: Tag
  id: Table

- type: Tag
  id: Telecrystal

- type: Tag
  id: TemplarHelmet

- type: Tag
  id: Tesla

- type: Tag
  id: Tongue

- type: Tag
  id: Tourniquet

- type: Tag
  id: ToyGrenade

- type: Tag
  id: ToySword

- type: Tag
  id: MolotovConstruction

- type: Tag
  id: IgnitionSource

- type: Tag
  id: Undead

- type: Tag
  id: UristHand

- type: Tag
  id: VehicleKey

- type: Tag
  id: VulpEmotes

- type: Tag
  id: WaterTank

- type: Tag
  id: WeaponDisabler

- type: Tag
  id: WeaponPistolDualetta

- type: Tag
  id: WeaponEnergyGunLawbringer

- type: Tag
  id: WeaponPistolCasul

- type: Tag
  id: WeaponPistolJackal

- type: Tag
  id: WeldingTool

- type: Tag
  id: WinterCoat

- type: Tag
  id: WizardHat

- type: Tag
  id: HudMedicalSecurity

- type: Tag
  id: HudDiagnostic

- type: Tag
  id: HudMedicalDiagnostic

- type: Tag
  id: ReverseBearTrapKey

- type: Tag
  id: Rifle

- type: Tag
  id: Hacking

- type: Tag
  id: Jetpack

- type: Tag
  id: RoundstartAntag

- type: Tag
  id: CalmAntag

- type: Tag
  id: MidroundAntag
