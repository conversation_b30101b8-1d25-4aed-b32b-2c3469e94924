# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: gameMap
  id: Lavatest
  mapName: Лаваленд Тест
  mapPath: /Maps/_Lavaland/lavaland_playtest.yml
  minPlayers: 0
  stations:
    Lavatest:
      stationProto: StandardNanotrasenStation
      components:
        - type: StationNameSetup
          mapNameTemplate: "ЛаваТест"
        - type: StationJobs
          availableJobs:
            Quartermaster: [ 1, 1 ]
            SalvageSpecialist: [ -1, -1 ]
