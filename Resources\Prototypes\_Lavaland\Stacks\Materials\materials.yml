# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2021 <PERSON> Aguilera Puerto <<EMAIL>>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2022 <PERSON>-<PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: stack
  id: GoldOreUnprocessed
  name: stack-unprocessed-gold-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: gold }
  spawn: GoldOre1Unprocessed
  maxCount: 30

- type: stack
  id: DiamondOreUnprocessed
  name: stack-unprocessed-diamond-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: diamond }
  spawn: DiamondOre1Unprocessed
  maxCount: 30

- type: stack
  id: SteelOreUnprocessed
  name: stack-unprocessed-steel-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: iron }
  spawn: SteelOre1Unprocessed
  maxCount: 30

- type: stack
  id: PlasmaOreUnprocessed
  name: stack-unprocessed-plasma-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: plasma }
  spawn: PlasmaOre1Unprocessed
  maxCount: 30

- type: stack
  id: SilverOreUnprocessed
  name: stack-unprocessed-silver-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: silver }
  spawn: SilverOre1Unprocessed
  maxCount: 30

- type: stack
  id: SpaceQuartzUnprocessed
  name: stack-unprocessed-quartz-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: spacequartz }
  spawn: SpaceQuartz1Unprocessed
  maxCount: 30

- type: stack
  id: UraniumOreUnprocessed
  name: stack-unprocessed-uranium-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: uranium }
  spawn: UraniumOre1Unprocessed
  maxCount: 30


- type: stack
  id: BananiumOreUnprocessed
  name: stack-unprocessed-bananium-ore
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: bananium }
  spawn: BananiumOre1Unprocessed
  maxCount: 30

- type: stack
  id: CoalUnprocessed
  name: stack-unprocessed-coal
  icon: { sprite: /Textures/Objects/Materials/ore.rsi, state: coal }
  spawn: Coal1Unprocessed
  maxCount: 30
