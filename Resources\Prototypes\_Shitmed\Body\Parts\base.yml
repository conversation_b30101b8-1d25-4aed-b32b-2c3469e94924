# SPDX-FileCopyrightText: 2025 <PERSON> <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 <PERSON><PERSON><PERSON> <<EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
# body part with regular organic destruction
- type: entity
  abstract: true
  parent: BasePartInorganic
  id: BasePart
  components:
  - type: BodyPart
    partComposition: Organic
  - type: Damageable
    damageContainer: Biological # Shitmed Change
  - type: Nerve
  - type: Extractable
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 3
      - ReagentId: Blood
        Quantity: 10
  - type: Tag # Goobstation
    tags:
    - Trash
    - Meat

# organic torsos have higher health
# this doesn't affect gibbing bodies as there is code to prevent torso gibbing while part of a body
- type: entity
  abstract: true
  parent: BaseChestInorganic
  id: BaseChest
  components:
  - type: BodyPart
    partComposition: Organic
  - type: Woundable
    damageContainer: Biological
  - type: Destructible
    thresholds:
    - trigger:
        !type:DamageTypeTrigger
        damageType: Heat
        damage: 400
      behaviors:
      - !type:SpawnEntitiesBehavior
        spawnInContainer: true
        spawn:
          Ash:
            min: 1
            max: 1
      - !type:BurnBodyBehavior { }
      - !type:PlaySoundBehavior
        sound:
          collection: MeatLaserImpact
  - type: Extractable # torso is meatier
    juiceSolution:
      reagents:
      - ReagentId: Fat
        Quantity: 10
      - ReagentId: Blood
        Quantity: 20
  - type: WoundableVisuals
    thresholds: [ 10, 20, 30, 50, 70, 100 ]
    occupiedLayer: Chest
    damageOverlayGroups:
      Brute:
        sprite: _Shitmed/Mobs/Effects/brute_damage.rsi
        color: "#FF0000"
      Burn:
        sprite: _Shitmed/Mobs/Effects/burn_damage.rsi
    bleedingOverlay: _Shitmed/Mobs/Effects/bleeding_damage.rsi

- type: entity
  abstract: true
  id: BaseGroin
  parent: BaseGroinInorganic
  components:
  - type: BodyPart
    partComposition: Organic
  - type: Woundable
    damageContainer: Biological
  - type: WoundableVisuals
    thresholds: [ 10, 20, 30, 50, 70, 100 ]
    occupiedLayer: Groin
    damageOverlayGroups:
      Brute:
        sprite: _Shitmed/Mobs/Effects/brute_damage.rsi
        color: "#FF0000"
      Burn:
        sprite: _Shitmed/Mobs/Effects/burn_damage.rsi
