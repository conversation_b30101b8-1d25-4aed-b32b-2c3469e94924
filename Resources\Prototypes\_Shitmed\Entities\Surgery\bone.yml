# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: Bone
  parent: BaseItem
  name: bone
  description: A real bone. Contains calcium, a lot of calcium.
  components:
  - type: Bone # backmen: wounding
  - type: Sprite
    sprite: Objects/Materials/materials.rsi
    state: bones
  - type: Appearance
  - type: Item
    sprite: Objects/Materials/materials.rsi
    size: Normal
  - type: Tag
    tags:
      - RawMaterial
  - type: Damageable
    damageContainer: Inorganic

- type: entity
  id: BoneNeck
  parent: Bone
  name: neck bone
  description: A bone from the neck. Contains a lot of calcium.
  components:
  - type: Bone
    integrityCap: 120
    boneIntegrity: 120
  - type: Sprite
    sprite: Objects/Materials/materials.rsi
    state: bones
  - type: Appearance
