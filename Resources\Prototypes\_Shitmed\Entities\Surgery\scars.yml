# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: BluntScar
  parent: WoundBase
  components:
  - type: Wound
    damageType: Blunt
    woundType: External
    woundVisibility: Always
    canBeHealed: false
    isScar: true
  - type: PainInflicter
    multiplier: 0

- type: entity
  id: PiercingScar
  parent: WoundBase
  components:
  - type: Wound
    damageType: Piercing
    woundType: External
    woundVisibility: Always
    canBeHealed: false
    isScar: true
  - type: PainInflicter
    multiplier: 0

- type: entity
  id: SlashScar
  parent: WoundBase
  components:
  - type: Wound
    damageType: Slash
    woundType: External
    woundVisibility: Always
    canBeHealed: false
    isScar: true
  - type: PainInflicter
    multiplier: 0

- type: entity
  id: BurnScar
  parent: WoundBase
  components:
  - type: Wound
    damageType: Heat
    woundType: External
    woundVisibility: Always
    canBeHealed: false
    isScar: true
  - type: PainInflicter
    multiplier: 0

- type: entity
  id: RadiationScar
  parent: WoundBase
  components:
  - type: Wound
    damageType: Radiation
    woundType: External
    woundVisibility: Always
    canBeHealed: false
    isScar: true
  - type: PainInflicter
    multiplier: 0 # lmao
