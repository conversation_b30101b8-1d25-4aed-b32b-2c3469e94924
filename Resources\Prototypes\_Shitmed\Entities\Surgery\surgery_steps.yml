# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 Janet <PERSON>ll <<EMAIL>>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  abstract: true
  id: SurgeryStepBase
  components:
  - type: SurgeryStep

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepOpenIncisionScalpel
  name: Cut with a scalpel
  components:
  - type: SurgeryStep
    tool:
    - type: Scalpel
    add:
    - type: IncisionOpen
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/scalpel.rsi
    state: scalpel
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Bloodloss: 10
    sleepModifier: 0.5
  - type: SurgeryStepPainInflicter
    painDuration: 30
    painType: TraumaticPain
    amount: 34
    sleepModifier: 0.12

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepClampBleeders
  name: Clamp the bleeders
  components:
  - type: SurgeryStep
    tool:
    - type: Hemostat
    add:
    - type: BleedersClamped
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryRepeatableStep
  - type: SurgeryBleedsTreatmentStep
    amount: 2

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepCloseBloodOutputs
  name: Stitch up destroyed tissues
  components:
  - type: SurgeryStep
    tool:
    - type: Stitches
    remove:
    - type: BleedersClamped
    duration: 5
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/stitches.rsi
    state: stitches
  - type: SurgeryStepPainInflicter
    painDuration: 30
    painType: TraumaticPain
    amount: 34
    sleepModifier: 0.12

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRetractSkin
  name: Retract the skin
  components:
  - type: SurgeryStep
    tool:
    - type: Retractor
    add:
    - type: SkinRetracted
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/retractor.rsi
    state: retractor

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRemoveSeveredSkin
  name: Remove the dead skin
  components:
  - type: SurgeryStep
    tool:
    - type: Scalpel
    add:
    - type: BodyPartSawed
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/scalpel.rsi
    state: scalpel
  - type: SurgeryStepPainInflicter
    painDuration: 60
    amount: 45
    sleepModifier: 0.07

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSawBones
  name: Saw through bones
  components:
  - type: SurgeryStep
    tool:
    - type: BoneSaw
    add:
    - type: RibcageSawed
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    state: circular-saw
  - type: SurgeryStepPainInflicter
    painType: TraumaticPain
    painDuration: 40
    amount: 34
    sleepModifier: 0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRemoveLeftoverBones
  name: Clear up bone leftovers
  components:
  - type: SurgeryStep
    tool:
    - type: BoneSaw
    add:
    - type: RibcageOpen
    duration: 5
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    state: circular-saw

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepMendBones
  name: Mend bones
  components:
  - type: SurgeryStep
    tool:
    - type: BoneGel
    duration: 7
  - type: SurgeryAffixPartStep
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/bone-gel.rsi
    state: bone-gel
  - type: SurgeryStepPainInflicter
    amount: 12
    painType: TraumaticPain
    sleepModifier: 0
  - type: SurgeryRepeatableStep
  - type: SurgeryTraumaTreatmentStep
    traumaType: BoneDamage
    amount: 17

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepHealOrgans
  name: Heal Organs
  components:
  - type: SurgeryStep
    tool:
    - type: Tending
    duration: 7
  - type: SurgeryAffixOrganStep
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryStepPainInflicter
    amount: 24
    painType: TraumaticPain
    sleepModifier: 0
  - type: SurgeryRepeatableStep
  - type: SurgeryTraumaTreatmentStep
    traumaType: OrganDamage
    amount: 17

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepPriseOpenBones
  name: Prise the bones open
  components:
  - type: SurgeryStep
    tool:
    - type: Retractor
    add:
    - type: RibcageOpen
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/retractor.rsi
    state: retractor

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepCutLarvaRoots
#  name: Cut larva roots
#  components:
#  - type: SurgeryStep
#    skill: 2
#    tool:
#    - type: Scalpel
#  - type: SurgeryCutLarvaRootsStep
#  - type: Sprite
#    sprite: Objects/Specific/Medical/Surgery/scalpel.rsi
#    state: scalpel
#  - type: SurgeryOperatingTableCondition

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepRemoveLarva
#  name: Remove larva
#  components:
#  - type: SurgeryStep
#    skill: 2
#    tool:
#    - type: Hemostat
#    bodyRemove:
#    - type: VictimInfected
#  - type: Sprite
#    sprite: Objects/Specific/Medical/Surgery/scissors.rsi
#    state: hemostat
#  - type: SurgeryOperatingTableCondition
#  - type: SurgeryStepSpawnEffect
#    entity: XenoEmbryo

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepCloseBones
  name: Close bones
  components:
  - type: SurgeryStep
    tool:
    - type: Retractor
    remove:
    - type: RibcageOpen
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/retractor.rsi
    state: retractor

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepMendRibcage
  name: Mend ribcage
  components:
  - type: SurgeryStep
    tool:
    - type: BoneGel
    remove:
    - type: RibcageSawed
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/bone-gel.rsi
    state: bone-gel

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepCloseIncision
  name: Close incision
  components:
  - type: SurgeryStep
    tool:
    - type: Cautery
    remove:
    # This surgery removes a bunch of components that might be leftover from other surgeries in unintended cases.
    # Essentially a bit of a fallback for endusers :)
    - type: SkinRetracted
    - type: BleedersClamped
    - type: IncisionOpen
    - type: BodyPartReattached
    - type: InternalBleedersClamped
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    state: cautery
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Heat: -5
    sleepModifier: 2
  - type: SurgeryStepPainInflicter
    amount: 9
    sleepModifier: 0.34

# Feature Insertion

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepInsertFeature
  name: Insert part
  components:
  - type: SurgeryStep
    tool:
    - type: BodyPart
    duration: 6
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
    state: insertion
  - type: SurgeryAddPartStep

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSealWounds
  name: Seal wounds
  components:
  - type: SurgeryStep
    tool:
    - type: Cautery
    remove:
    - type: SkinRetracted
    - type: BleedersClamped
    - type: IncisionOpen
    - type: InternalBleedersClamped
    duration: 2
  - type: SurgeryAffixPartStep
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    state: cautery
  - type: SurgeryStepPainInflicter
    amount: 12
    painDuration: 60
    sleepModifier: 0.34
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Heat: -5
    sleepModifier: 2

# Feature Removal

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSawFeature
  name: Saw through bones
  components:
  - type: SurgeryStep
    tool:
    - type: BoneSaw
    add:
    - type: BodyPartSawed
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    state: circular-saw
  - type: SurgeryStepPainInflicter
    amount: 26
    painDuration: 40
    painType: TraumaticPain
    sleepModifier: 0.0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepClampInternalBleeders
  name: Clamp internal bleeders
  components:
  - type: SurgeryStep
    tool:
    - type: Hemostat
    add:
    - type: InternalBleedersClamped
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Bloodloss: -5
    sleepModifier: 2

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRemoveFeature
  name: Amputate part
  components:
  - type: SurgeryStep
    tool:
    - type: BoneSaw
    remove:
    # We remove these components to force people to go through all the steps again lol, otherwise you can just keep chopping.
    - type: SkinRetracted
    - type: BleedersClamped
    - type: InternalBleedersClamped
    - type: IncisionOpen
    duration: 8
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/circular-saw.rsi
    state: circular-saw
  - type: SurgeryRemovePartStep
  - type: SurgeryStepPainInflicter
    amount: 50
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0.14

# Tend Wounds

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepCarefulIncisionScalpel
  name: Make a careful incision
  components:
  - type: SurgeryStep
    tool:
    - type: Scalpel
    add:
    - type: IncisionOpen
    duration: 3
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/scalpel.rsi
    state: scalpel
  - type: SurgeryStepPainInflicter
    amount: 17
    painDuration: 30
    sleepModifier: 0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRepairBruteTissue
  name: Repair damaged tissue
  components:
  - type: SurgeryStep
    tool:
    - type: Tending
    duration: 1
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryTendWoundsEffect
    damage:
      groups:
        Brute: -15
  - type: SurgeryRepeatableStep

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRepairBurnTissue
  name: Repair burnt tissue
  components:
  - type: SurgeryStep
    tool:
    - type: Tending
    duration: 1
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryTendWoundsEffect
    mainGroup: Burn
    damage:
      groups:
        Burn: -20
  - type: SurgeryRepeatableStep

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSealTendWound
  name: Seal the wound
  components:
  - type: SurgeryStep
    tool:
    - type: Cautery
    remove:
    - type: IncisionOpen
    duration: 2
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    state: cautery
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Heat: -5
    sleepModifier: 2
  - type: SurgeryStepPainInflicter
    amount: 21
    painDuration: 40
    sleepModifier: 0.34

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSealDismembermentWound
  name: Stitch up the dismemberment wound
  components:
  - type: SurgeryStep
    tool:
    - type: Stitches
    remove:
    - type: IncisionOpen
    duration: 8
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/stitches.rsi
    state: stitches
  - type: SurgeryTraumaTreatmentStep
    traumaType: Dismemberment
  - type: SurgeryStepPainInflicter
    amount: 34
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0.25

# Cavity Implanting

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepInsertItem
  name: Insert item into cavity
  components:
  - type: SurgeryStep
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
    state: insertion
  - type: SurgeryStepCavityEffect
    action: Insert
  - type: SurgeryStepPainInflicter
    amount: 34
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRemoveItem
  name: Remove item from cavity
  components:
  - type: SurgeryStep
    duration: 4
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
    state: insertion
  - type: SurgeryStepCavityEffect
    action: Remove
  - type: SurgeryStepPainInflicter
    amount: 34
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0

# Organ Manipulation

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepRemoveOrgan
  name: Remove organ
  components:
  - type: SurgeryStep
    tool:
    - type: Tweezers
    duration: 8
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/hemostat.rsi
    state: hemostat
  - type: SurgeryRemoveOrganStep
  - type: SurgeryStepPainInflicter
    amount: 90
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepInsertOrgan
  name: Add organ
  components:
  - type: SurgeryStep
    tool:
    - type: Organ
    duration: 6
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
    state: insertion
  - type: SurgeryAddOrganStep
  - type: SurgeryStepPainInflicter
    amount: 90
    painDuration: 60
    painType: TraumaticPain
    sleepModifier: 0

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepOpenOrganSlot
  name: Carve out a cavity
  components:
  - type: SurgeryStep
    tool:
    - type: Scalpel
    duration: 6
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
    state: insertion
  - type: SurgeryAddOrganSlotStep
  - type: SurgeryStepEmoteEffect

- type: entity
  parent: SurgeryStepInsertOrgan
  id: SurgeryStepInsertLungs
  name: Add lungs
  components:
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Asphyxiation: -********** # Literally the max 32 bit value, if your patient has gone higher than this, maybe it's time to restart the round.
    sleepModifier: 1
    isConsumable: true
    affectAll: true

- type: entity
  parent: SurgeryStepInsertOrgan
  id: SurgeryStepInsertLiver
  name: Add liver
  components:
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Poison: -********** # Literally the max 32 bit value, if your patient has gone higher than this, maybe it's time to restart the round.
    sleepModifier: 1
    isConsumable: true
    affectAll: true

- type: entity
  parent: SurgeryStepInsertOrgan
  id: SurgeryStepInsertEyes
  name: Add eyes

- type: entity
  parent: SurgeryStepInsertOrgan
  id: SurgeryStepInsertHeart
  name: Add heart
  components:
  - type: SurgerySpecialDamageChangeEffect
    damageType: Rot
    isConsumable: true

# Doesn't serve much of a purpose right now. Just here for completeness-sake.
- type: entity
  parent: SurgeryStepInsertOrgan
  id: SurgeryStepInsertStomach
  name: Add stomach

- type: entity
  parent: SurgeryStepBase
  id: SurgeryStepSealOrganWound
  name: Seal wounds
  components:
  - type: SurgeryStep
    tool:
    - type: Cautery
    duration: 2
  - type: SurgeryAffixOrganStep
  - type: Sprite
    sprite: _Shitmed/Objects/Specific/Medical/Surgery/cautery.rsi
    state: cautery
  - type: SurgeryDamageChangeEffect
    damage:
      types:
        Heat: -5
    sleepModifier: 2
  - type: SurgeryStepPainInflicter
    amount: 21
    painDuration: 60
    sleepModifier: 0.34



# The lengths I go to just for a joke... I HATE HARDCODING AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
# Maybe I should modify species prototypes to include tails and ears properly...

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepAddFelinidEars
#  name: Add cat ears
#  components:
#  - type: SurgeryStep
#    tool:
#    - type: Organ
#  - type: SurgeryAddMarkingStep
#    marking: FelinidEarsBasic
#    markingCategory: HeadTop
#    matchString: FelinidEars
#    organ:
#    - type: Ears
#    accent:
#    - type: OwOAccent
#  - type: Sprite
#    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
#    state: insertion

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepAddFelinidTail
#  name: Add cat tail
#  components:
#  - type: SurgeryStep
#    tool:
#    - type: Organ
#  - type: SurgeryAddMarkingStep
#    marking: FelinidTailBasic
#    markingCategory: Tail
#    matchString: FelinidTail
#    organ:
#    - type: Tail
#  - type: Sprite
#    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
#    state: insertion

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepRemoveFelinidEars
#  name: Remove cat ears
#  components:
#  - type: SurgeryStep
#    tool:
#    - type: Organ
#  - type: SurgeryRemoveMarkingStep
#    markingCategory: HeadTop
#    matchString: FelinidEars
#  - type: Sprite
#    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
#    state: insertion

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepRemoveFelinidTail
#  name: Remove cat tail
#  components:
#  - type: SurgeryStep
#    tool:
#    - type: Organ
#  - type: SurgeryRemoveMarkingStep
#    markingCategory: Tail
#    matchString: FelinidTail
#  - type: Sprite
#    sprite: _Shitmed/Objects/Specific/Medical/Surgery/manipulation.rsi
#    state: insertion

#- type: entity
#  parent: SurgeryStepBase
#  id: SurgeryStepWingReconstruction
#  name: Start wing reconstruction
#  components:
#  - type: SurgeryStep
#    duration: 6 # On TG success chance is lower, so here the surgery slower
#    tool:
#    - type: Tending
#  - type: ??? moth ops go here when wings can be destroyed
#  - type: Sprite
#    sprite: Interface/Emotes/chitter.png
