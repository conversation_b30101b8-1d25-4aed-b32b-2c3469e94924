# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: entity
  id: ExperimentalTeleporterInEffect
  categories: [ HideSpawnMenu ]
  components:
    - type: TimedDespawn
      lifetime: 0.6
    - type: EvaporationSparkle
    - type: Transform
      noRot: true
      anchored: true
    - type: Sprite
      layers:
        - sprite: /Textures/_White/Objects/Devices/experimentalsyndicateteleporter.rsi
          state: in
          shader: unshaded
      netsync: false
      drawdepth: Effects
    - type: PointLight
      color: "#008DFE"

- type: entity
  id: ExperimentalTeleporterOutEffect
  categories: [ HideSpawnMenu ]
  components:
    - type: TimedDespawn
      lifetime: 0.6
    - type: EvaporationSparkle
    - type: Transform
      noRot: true
      anchored: true
    - type: Sprite
      layers:
        - sprite: /Textures/_White/Objects/Devices/experimentalsyndicateteleporter.rsi
          state: out
          shader: unshaded
      netsync: false
      drawdepth: Effects
    - type: PointLight
      color: "#008DFE"
