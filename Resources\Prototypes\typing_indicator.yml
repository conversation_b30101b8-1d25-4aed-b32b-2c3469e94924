# SPDX-FileCopyrightText: 2022 <PERSON> <aev<PERSON><EMAIL>>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
#
# SPDX-License-Identifier: AGPL-3.0-or-later

- type: typingIndicator
  id: default
  typingState: default0
  idleState: default3

- type: typingIndicator
  id: empty
  typingState: empty0
  idleState: empty0

- type: typingIndicator
  id: robot
  typingState: robot0
  idleState: robot3

- type: typingIndicator
  id: alien
  typingState: alien0
  idleState: alien3

- type: typingIndicator
  id: guardian
  typingState: guardian0
  idleState: guardian3

- type: typingIndicator
  id: holo
  typingState: holo0
  idleState: holo3
  offset: 0, 0.0625

- type: typingIndicator
  id: lawyer
  typingState: lawyer0
  idleState: lawyer3
  offset: 0, 0.125

- type: typingIndicator
  id: moth
  typingState: moth0
  idleState: moth3
  offset: 0, 0.125

- type: typingIndicator
  id: spider
  typingState: spider0
  idleState: spider3
  offset: 0, 0.125

- type: typingIndicator
  id: vox
  typingState: vox0
  idleState: vox0 # TODO add idle state sprite
  offset: -0.125, 0.125

- type: typingIndicator
  id: lizard
  typingState: lizard0
  idleState: lizard3
  offset: 0, 0.0625

- type: typingIndicator
  id: slime
  typingState: slime0
  idleState: slime3
  offset: 0, 0.125

- type: typingIndicator
  id: gingerbread
  typingState: gingerbread0
  idleState: gingerbread0
  offset: 0, 0.125

- type: typingIndicator
  id: diona
  typingState: diona0
  idleState: diona0
  offset: 0, 0.125
