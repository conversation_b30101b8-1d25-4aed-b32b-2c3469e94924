<!--
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>
SPDX-FileCopyrightText: 2025 ArtisticRoomba <<EMAIL>>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Document>
  # Вентиляція
  Вентиляція - це стандартний вентиляційний отвір, що знаходиться у всіх кімнатах, які ви хочете наповнити придатною для дихання атмосферою.
  Її основна робота - перекачувати гази в атмосферу, до якої вона піддається, доки не досягне встановленого тиску.

  <Box>
    <GuideEntityEmbed Entity="GasVentPump"/>
  </Box>

  Для функціонування вентиляції потрібне [textlink="живлення" link="Power"] через найближчий [textlink="НН кабель" link="VoltageNetworks"].

  Вентиляцію можна заварити будь-яким зварювальним інструментом, щоб припинити її роботу.

  ## Стандартна робота
  Стандартна поведінка вентиляції - просто випускати газ у відкриту атмосферу при тиску [color=orange][protodata="GasVentPump" comp="GasVentPump" member="DefaultExternalBound"/] кПа[/color].

  Швидкість вентиляції залежить від різниці між подвоєним тиском підключеної [textlink="трубопровідної мережі" link="PipeNetworks"] та тиском відкритої атмосфери.
  Словесне рівняння:
  <Box>
    [italic]Швидкість = 2 × Тиск у трубопроводі - Тиск у відкритій атмосфері[/italic]
  </Box>

  Наприклад, це означає, що вентиляція не зможе качати до 101.3 кПа, якщо газ усередині вентиляції опуститься нижче половини цього значення.

  ## Блокування за тиском
  Вентиляція переходить у [color=red]блокування за тиском[/color], коли тиск атмосфери, до якої вона піддається, нижчий за [color=orange][protodata="GasVentPump" comp="GasVentPump" member="UnderPressureLockoutThreshold"/] кПа[/color].
  Це робиться для того, щоб запобігти витраті корисних газів у космос, якщо кімната не герметична.
  Індикатори на вентиляції стануть [color=yellow]жовтими[/color], щоб підкреслити цю зміну.

  Це можна [color=green]скасувати[/color], клацнувши правою кнопкою миші на вентиляції та вибравши 'Зняти блокування за тиском', або встановивши режим "Заповнення" на підключеній [textlink="повітряній сигналізації" link="AirAlarms"].

  Важливо зазначити, що вентиляція в режимі [color=red]блокування за тиском[/color] все ще дуже повільно випускає газ.
  Кількість газу, що витікає, залежить від поточного тиску в підключеній трубопровідній мережі.

  ## Параметри конфігурації
  При [textlink="підключенні" link="Networking"] до [textlink="повітряної сигналізації" link="AirAlarms"] вентиляція отримує більше функціональних можливостей.

  Вентиляція має два варіанти "напрямку вентиляції": Випуск та Відкачування.
  - Випуск просто випускає гази в атмосферу з підключеної трубопровідної мережі.
  - Відкачування всмоктує повітря з атмосфери в підключену трубопровідну мережу. Індикатори на вентиляції стануть [color=red]червоними[/color], щоб підкреслити цю зміну.

  ## Межі тиску
  Вентиляція має систему меж тиску, яка контролює тиск, при якому вентиляція буде наповнювати атмосферу або відбирати газ із підключеної трубопровідної мережі. Ці обмеження враховуються як у режимі випуску, так і в режимі відкачування.

  Налаштування "PressureBound" має чотири варіанти:
  - ExternalBound змушує вентиляцію дотримуватися меж тиску, визначених у записі External bound.
  - InternalBound змушує вентиляцію дотримуватися меж тиску, визначених у записі Internal bound.
  - Both змушує вентиляцію дотримуватися меж тиску, визначених як у записах External, так і Internal bound.
  - NoBound змушує вентиляцію не дотримуватися жодних обмежень.

  Коли вентиляція в режимі випуску:
  - Якщо тиск External bound встановлено на 101.3 кПа, вентиляція не буде наповнювати атмосферу вище 101.3 кПа.
  - Якщо тиск Internal bound встановлено на 50 кПа, вентиляція не буде відбирати газ із підключеної труби, якщо її тиск нижчий за 50 кПа.

  Коли вентиляція в режимі відкачування:
  - Якщо тиск External bound встановлено на 101.3 кПа, вентиляція буде відкачувати гази, доки атмосфера не досягне 101.3 кПа.
  - Якщо тиск Internal bound встановлено на 50 кПа, вентиляція не буде подавати гази в трубопровідну мережу, якщо її тиск вищий за 50 кПа.

  Якщо ви все ще не розібралися з PressureBounds, ось простий спосіб про це подумати:
  - Ви можете думати про External bound як про верхню межу для відкритої атмосфери. "Я не буду наповнювати відкриту атмосферу вище цього тиску або відбирати з атмосфери нижче цього тиску."
  - Ви можете думати про Internal bound як про нижню межу для підключеної трубопровідної мережі. "Я не буду відбирати з мого джерела нижче цього тиску або подавати повітря в трубопровідну мережу вище цього тиску."
  - Налаштування "Pressure bound" просто визначають, яких обмежень дотримуватися чи ігнорувати.

</Document>
