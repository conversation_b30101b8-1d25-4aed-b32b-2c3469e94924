<!--
SPDX-FileCopyrightText: 2023 M3739 <<EMAIL>>
SPDX-FileCopyrightText: 2024 Pieter-<PERSON>riers <<EMAIL>>
SPDX-FileCopyrightText: 2025 Aiden <28298836+<PERSON><PERSON><PERSON>@users.noreply.github.com>

SPDX-License-Identifier: AGPL-3.0-or-later
-->

<Document>
  # Кіборги
  Кіборги - це гібриди людини та машини, спеціально створені відділом [textlink="робототехніки"  link="Robotics"] для виконання різноманітних обов'язків на станції. Зв'язані суворо дотримуваними [color=cyan]кремнієвими законами[/color], вони беззастережно служать екіпажу. [italic](Більшість часу.)[italic]

  ## Основні компоненти
  <Box>
    <GuideEntityEmbed Entity="CyborgEndoskeleton" Caption="Ендоскелет кіборга"/>
    <GuideEntityEmbed Entity="ExosuitFabricator" Caption="Виробник екзокостюмів"/>
  </Box>
  Усі кіборги будуються на ендоскелеті, який можна виготовити у [color=#a4885c]Виробнику екзокостюмів[/color]. Подальші кроки збирання можна виконати, [color=#a4885c]оглянувши[/color] ендоскелет. Після створення, подальші оновлення, такі як додаткові інструменти та довший час роботи від батареї, можна розблокувати за допомогою модулів або покращених елементів живлення.

  Новоствореним кіборгам потрібен [color=#a4885c]мозок[/color], здатний взаємодіяти з електронним тілом. Існує два основних типи, обидва з яких здатні говорити самостійно, якщо шасі буде виведено з експлуатації, замінено або зазнає незапланованого розбирання.
  - [color=#a4885c]Людино-машинний інтерфейс[/color] або коротко ММІ, це пристрій, здатний забезпечити зв'язок між біологічним мозком та електронними компонентами, дозволяючи тим, хто постраждав від серйозних нещасних випадків на виробництві, продовжувати служити своєму екіпажу, навіть після втрати тіла.
  - [color=#a4885c]Позитронний мозок[/color] - це повністю штучний мозок, здатний до спонтанної нейронної активності. Час для розшифровки синтетичних нейронів може сильно відрізнятися, але вони дозволяють створювати кіборгів без необхідності вилучати мозок у члена екіпажу.
  <Box>
    <GuideEntityEmbed Entity="MMIFilled" Caption="Людино-машинний інтерфейс"/>
    <GuideEntityEmbed Entity="PositronicBrain" Caption="Позитронний мозок"/>
  </Box>
  Обидва типи мозку можна виготовити без додаткових досліджень.

  ## Типи кіборгів
  Після створення кіборг повинен спеціалізувати своє шасі для виконання обов'язків на станції. Це визначає, з якими модулями він починає, які додаткові модулі можна встановити, і до якого [color=#a4885c]радіоканалу відділу[/color] він має доступ. Усі кіборги мають доступ до [color=#D381C9]Наукового[/color] та [color=green]загальностанційного[/color] радіоканалів. Усі типи кіборгів мають [color=#a4885c]повний доступ[/color].
  <Box>
    <GuideEntityEmbed Entity="BorgChassisGeneric" Caption="Загальний"/>
    <GuideEntityEmbed Entity="BorgChassisEngineer" Caption="Інженерний"/>
    <GuideEntityEmbed Entity="BorgChassisMining" Caption="Видобувний"/>
    <GuideEntityEmbed Entity="BorgChassisJanitor" Caption="Прибиральник"/>
    <GuideEntityEmbed Entity="BorgChassisService" Caption="Обслуговуючий"/>
    <GuideEntityEmbed Entity="BorgChassisMedical" Caption="Медичний"/>
  </Box>
  <Box>
    [italic]Приклади різних типів кіборгів[/italic]
  </Box>
  Після спеціалізації шасі кіборга його не можна змінити. Щоб змінити тип, необхідно створити нове шасі. Мозок, елемент живлення та будь-які модулі [italic](якщо вони сумісні з новим шасі)[/italic] можна перенести зі старого шасі за бажанням.

  ## Модулі
  <Box>
    <GuideEntityEmbed Entity="BorgModuleTool" Caption="Інструментальний модуль кіборга"/>
  </Box>
  Кіборги не мають рук, і тому не можуть піднімати предмети, як більшість інших гравців. Замість цього їхнє обладнання забезпечується різними [color=#a4885c]модулями[/color]. Кожен тип кіборга починає з власного набору модулів, але додаткові модулі можна вставляти як оновлення. Ці додаткові модулі можна надрукувати у [color=#a4885c]Виробнику екзокостюмів[/color].

  [color=#a4885c]Загальні[/color] модулі додають універсальності. Їх можна встановити в будь-яке шасі, надаючи корисні інструменти, такі як ломи, GPS та можливість взаємодіяти з кабелями. [bold]Загальне шасі кіборга може вмістити до п'яти додаткових модулів.[/bold]
  <Box>
    <GuideEntityEmbed Entity="BorgModuleCable" Caption="Кабель"/>
    <GuideEntityEmbed Entity="BorgModuleFireExtinguisher" Caption="Вогнегасник"/>
  </Box>
  <Box>
    [italic]Приклади загальних модулів[/italic]
  </Box>

  Для більш специфічних потреб доступні [color=#a4885c]спеціалізовані[/color] модулі, що надають такі можливості, як сканування аномалій, будівництво стін, реанімація членів екіпажу або прибирання розливу космічного мастила. Ці модулі зазвичай пофарбовані в ту ж палітру, що й відділ [italic](або професія)[/italic], до якого вони належать. Ці модулі [italic](за винятком [color=#D381C9]наукових[/color] модулів, які можна встановити в будь-яке шасі,)[/italic] можна встановлювати лише у відповідне шасі кіборга. [bold]Спеціалізовані шасі кіборгів, а саме інженерне, прибиральницьке, обслуговуюче, медичне та видобувне, можуть вмістити до трьох додаткових модулів.[/bold]
  <Box>
    <GuideEntityEmbed Entity="BorgModuleAnomaly" Caption="Аномалія"/>
    <GuideEntityEmbed Entity="BorgModuleRCD" Caption="RCD"/>
    <GuideEntityEmbed Entity="BorgModuleMining" Caption="Видобувний"/>
    <GuideEntityEmbed Entity="BorgModuleCleaning" Caption="Прибиральний"/>
    <GuideEntityEmbed Entity="BorgModuleService" Caption="Обслуговуючий"/>
    <GuideEntityEmbed Entity="BorgModuleChemical" Caption="Хімічний"/>
  </Box>
  <Box>
    [italic]Приклади спеціалізованих модулів. Зверніть увагу на колір корпусу та плати.
  </Box>
  Додаткові модулі з розширеними або новими можливостями можна отримати за допомогою нових [textlink="технологій"  link="Technologies"], досліджених вченими.
</Document>
