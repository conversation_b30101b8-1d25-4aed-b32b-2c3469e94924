{"version": 1, "license": "CC-BY-SA-3.0", "copyright": "Taken from vgstation at commit https://github.com/vgstation-coders/vgstation13/commit/cdbcb1e858b11f083994a7a269ed67ef5b452ce9, Module actions by Scarky0. chem, adv-chem, and adv-mining by mub<PERSON><PERSON>_, xenoborg actions by Samuka-C (github)", "size": {"x": 32, "y": 32}, "states": [{"name": "state-laws"}, {"name": "no-action"}, {"name": "tool-module"}, {"name": "wire-module"}, {"name": "gps-module"}, {"name": "extinguisher-module"}, {"name": "geiger-module"}, {"name": "rcd-module"}, {"name": "adv-tools-module"}, {"name": "construction-module"}, {"name": "appraisal-module"}, {"name": "grappling-module"}, {"name": "mining-module"}, {"name": "adv-mining-module"}, {"name": "light-replacer-module"}, {"name": "cleaning-module"}, {"name": "adv-cleaning-module"}, {"name": "diagnosis-module"}, {"name": "treatment-module"}, {"name": "surgery-module"}, {"name": "adv-surgery-module"}, {"name": "chem-module"}, {"name": "adv-chem-module"}, {"name": "adv-diagnosis-module"}, {"name": "defib-module"}, {"name": "node-scanner-module"}, {"name": "anomaly-module"}, {"name": "service-module"}, {"name": "musical-module"}, {"name": "gardening-module"}, {"name": "harvesting-module"}, {"name": "clowning-module"}, {"name": "syndicate-weapon-module"}, {"name": "syndicate-operative-module"}, {"name": "syndicate-esword-module"}, {"name": "syndicate-l6c-module"}, {"name": "syndicate-martyr-module"}, {"name": "xenoborg-access-breaker-module"}, {"name": "xenoborg-basic-module"}, {"name": "xenoborg-extinguisher-module"}, {"name": "xenoborg-eye-module"}, {"name": "xenoborg-hypo-module"}, {"name": "xenoborg-jammer-module"}, {"name": "xenoborg-laser-module"}, {"name": "xenoborg-laser2-module"}, {"name": "xenoborg-projector-module"}, {"name": "xenoborg-space-movement-module"}, {"name": "xenoborg-sword-module"}, {"name": "xenoborg-sword2-module"}, {"name": "xenoborg-tool-module"}, {"name": "select-type"}]}