<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="32"
   height="32"
   viewBox="0 0 32 32"
   fill="none"
   version="1.1"
   id="svg4"
   sodipodi:docname="rounded_button_half_bordered.svg"
   inkscape:version="1.0.1 (3bc2e813f5, 2020-09-07)"
   inkscape:export-filename="C:\ss14\space-station-14\Resources\Textures\Interface\Nano\rounded_button_half_bordered.svg.96dpi.png"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96">
   <metadata
      id="metadata10">
      <rdf:RDF>
         <cc:Work
            rdf:about="">
            <dc:format>image/svg+xml</dc:format>
            <dc:type
               rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
            <dc:title />
         </cc:Work>
      </rdf:RDF>
   </metadata>
   <defs
      id="defs8">
      <clipPath id="round-corner">
         <rect x="0" y="0" width="32" height="32" rx="5" ry="5" />
      </clipPath>
   </defs>
   <sodipodi:namedview
      pagecolor="#ffffff"
      bordercolor="#666666"
      borderopacity="1"
      objecttolerance="10"
      gridtolerance="10"
      guidetolerance="10"
      inkscape:pageopacity="0"
      inkscape:pageshadow="2"
      inkscape:window-width="2560"
      inkscape:window-height="1377"
      id="namedview6"
      showgrid="false"
      fit-margin-top="0"
      fit-margin-left="0"
      fit-margin-right="0"
      fit-margin-bottom="0"
      inkscape:zoom="11.136932"
      inkscape:cx="15.825633"
      inkscape:cy="16.930202"
      inkscape:window-x="1912"
      inkscape:window-y="-8"
      inkscape:window-maximized="1"
      inkscape:current-layer="svg4"
      inkscape:document-rotation="0"
      inkscape:pagecheckerboard="true" />
   <rect
      x="0"
      y="0"
      width="32"
      height="50"
      rx="5"
      fill="#ffffff"
      id="rect2"
      style="stroke:#cfcfcf;stroke-opacity:1;fill:#ffffff;fill-opacity:1"
   />
</svg>