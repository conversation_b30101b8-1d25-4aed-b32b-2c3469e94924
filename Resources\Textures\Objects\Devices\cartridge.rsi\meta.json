{"version": 1, "license": "CC-BY-SA-3.0", "copyright": "Taken from vgstation at https://github.com/vgstation-coders/vgstation13/commit/1cdfb0230cc96d0ba751fa002d04f8aa2f25ad7d and tgstation at tgstation at https://github.com/tgstation/tgstation/commit/0c15d9dbcf0f2beb230eba5d9d889ef2d1945bb8, cart-log made by <PERSON><PERSON><PERSON><PERSON> (github), cart-sec made by dieselmohawk (discord), cart-nav, cart-med made by ArchRBX (github), cart-psi made by CerberusWolfie (github/discord)", "size": {"x": 32, "y": 32}, "states": [{"name": "cart"}, {"name": "cart-a"}, {"name": "cart-b"}, {"name": "cart-c"}, {"name": "cart-ce"}, {"name": "cart-chem"}, {"name": "cart-clown"}, {"name": "cart-cmo"}, {"name": "cart-e"}, {"name": "cart-eye"}, {"name": "cart-h"}, {"name": "cart-hos"}, {"name": "cart-j"}, {"name": "cart-lib"}, {"name": "cart-log"}, {"name": "cart-m"}, {"name": "cart-med"}, {"name": "cart-mi"}, {"name": "cart-nav"}, {"name": "cart-ord"}, {"name": "cart-q"}, {"name": "cart-rd"}, {"name": "cart-s"}, {"name": "cart-sec"}, {"name": "cart-tear"}, {"name": "cart-tox"}, {"name": "cart-y"}, {"name": "insert_overlay"}, {"name": "cart-psi"}]}