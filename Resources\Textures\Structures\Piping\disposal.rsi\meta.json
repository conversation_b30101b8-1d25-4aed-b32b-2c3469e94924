{"version": 1, "size": {"x": 32, "y": 32}, "license": "CC-BY-SA-3.0", "copyright": "https://github.com/discordia-space/CEV-Eris/blob/bbe32606902c90f5290b57d905a3f31b84dc6d7d/icons/obj/pipes/disposal.dmi and modified by DrSmugleaf. Signal router sprites based on normal router modified by deltanedas (github). disposal bins by EmoGarbage404 (github). pipe-x pipe-xf, conpipe-x by K-Dynamic (github).", "states": [{"name": "condisposal", "directions": 1}, {"name": "conmailing", "directions": 1}, {"name": "conpipe-c", "directions": 4}, {"name": "conpipe-j1", "directions": 4}, {"name": "conpipe-j1s", "directions": 4}, {"name": "conpipe-j2", "directions": 4}, {"name": "conpipe-j2s", "directions": 4}, {"name": "conpipe-x", "directions": 4}, {"name": "conpipe-s", "directions": 4}, {"name": "conpipe-t", "directions": 4}, {"name": "conpipe-tagger", "directions": 4}, {"name": "conpipe-y", "directions": 4}, {"name": "disposal", "directions": 1}, {"name": "mailing", "directions": 1}, {"name": "disposal-charging", "directions": 1}, {"name": "mailing-charging", "directions": 1}, {"name": "disposal-flush", "directions": 1, "delays": [[0.066, 0.066, 0.066, 0.066, 0.066, 0.066, 0.5, 0.066, 0.066, 0.066, 0.066, 0.066]]}, {"name": "mailing-flush", "directions": 1, "delays": [[0.066, 0.066, 0.066, 0.066, 0.066, 0.066, 0.5, 0.066, 0.066, 0.066, 0.066, 0.066]]}, {"name": "dispover-charge", "directions": 1, "delays": [[0.4, 0.4]]}, {"name": "dispover-full", "directions": 1, "delays": [[0.2, 0.2]]}, {"name": "dispover-handle", "directions": 1}, {"name": "mailover-handle", "directions": 1}, {"name": "dispover-ready", "directions": 1}, {"name": "intake", "directions": 4}, {"name": "intake-closing", "directions": 4, "delays": [[0.5, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1], [0.5, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1], [0.5, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1], [0.5, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]]}, {"name": "outlet", "directions": 4}, {"name": "outlet-open", "directions": 4, "delays": [[0.5, 0.5, 0.5, 0.5, 0.5, 0.1, 1.5, 0.1], [0.5, 0.5, 0.5, 0.5, 0.5, 0.1, 1.5, 0.1], [0.5, 0.5, 0.5, 0.5, 0.5, 0.1, 1.5, 0.1], [0.5, 0.5, 0.5, 0.5, 0.5, 0.1, 1.5, 0.1]]}, {"name": "pipe-b", "directions": 4}, {"name": "pipe-bf", "directions": 4}, {"name": "pipe-c", "directions": 4}, {"name": "pipe-cf", "directions": 4}, {"name": "pipe-d", "directions": 4}, {"name": "pipe-j1", "directions": 4}, {"name": "pipe-j1f", "directions": 4}, {"name": "pipe-j1s", "directions": 4}, {"name": "pipe-j1sf", "directions": 4}, {"name": "pipe-j2", "directions": 4}, {"name": "pipe-j2f", "directions": 4}, {"name": "pipe-j2s", "directions": 4}, {"name": "pipe-j2sf", "directions": 4}, {"name": "pipe-x", "directions": 4}, {"name": "pipe-xf", "directions": 4}, {"name": "pipe-s", "directions": 4}, {"name": "pipe-sf", "directions": 4}, {"name": "pipe-t", "directions": 4}, {"name": "pipe-tagger", "directions": 4}, {"name": "pipe-tagger-partial", "directions": 4}, {"name": "pipe-tf", "directions": 4}, {"name": "pipe-y", "directions": 4}, {"name": "pipe-yf", "directions": 4}, {"name": "signal-router", "directions": 4}, {"name": "signal-router-free", "directions": 4}, {"name": "signal-router-flipped", "directions": 4}, {"name": "signal-router-flipped-free", "directions": 4}]}