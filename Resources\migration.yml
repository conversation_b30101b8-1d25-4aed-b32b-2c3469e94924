# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 deltanedas <@deltanedas:kde.org>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# <AUTHOR> <EMAIL>
# SPDX-FileCopyrightText: 2025 whateverusername0 <whateveremail>
#
# SPDX-License-Identifier: AGPL-3.0-or-later
# This is a basic dictionary that maps old entity prototype ids to new ids. This only works for entity prototypes, and
# is intended to allow maps to load without having to manually edit them. An empty or "null" string results in the
# entity getting deleted.

# e.g., you can swap all walls with windows and delete all tables by adding lines like:
#
# Window: WallSolid
# WallSolid: Window
# Table: null

# Goobstation - Submarine from DeltaV
AirlockBoxerGlassLocked: AirlockServiceGlassLocked
AirlockClownGlassLocked: AirlockServiceGlassLocked
AirlockClownLocked: AirlockServiceGlassLocked
AirlockCorpsmanGlassLocked: AirlockServiceGlassLocked
AirlockExternalGlassShuttleShipyard: AirlockExternalGlassLocked
AirlockMailGlassLocked: AirlockCargoGlassLocked
AirlockMaintMailLocked: AirlockMaintCargoLocked
AirlockMaintMimeLocked: AirlockMaintTheatreLocked
AirlockMaintPsychologistLocked: AirlockMaintMedLocked
AirlockMaintReporterLocked: AirlockServiceLocked
AirlockMaintZookeeperLocked: AirlockServiceLocked
AirlockMantisGlassLocked: null
AirlockMantisLocked: null
AirlockMimeGlassLocked: AirlockTheatreGlassLocked
AirlockMusicianGlassLocked: AirlockTheatreGlassLocked
AirlockPsychologistLocked: AirlockMedicalLocked
AirlockReporterGlassLocked: AirlockServiceGlassLocked
AirlockZookeeperGlassLocked: AirlockServiceGlassLocked
AirlockZookeeperLocked: AirlockServiceLocked
AlwaysPoweredLightPostSmallRed: null
AsteroidAltRock: AsteroidRock
BenchParkBambooMiddle: null
BenchParkLeft: null
BenchParkMiddle: null
BenchParkRight: null
BenchPewLeft: null
BenchPewMiddle: null
BenchPewRight: null
BenchSofaCorpLeft: null
BenchSofaCorpMiddle: null
BenchSofaCorpRight: null
BenchSofaLeft: null
BenchSofaRight: null
BenchSteelLeft: null
BenchSteelMiddle: null
BenchSteelRight: null
BenchSteelWhiteLeft: null
BenchSteelWhiteMiddle: null
BenchSteelWhiteRight: null
BoardGameSpawner: null
BoxColoredLighttube: BoxLighttube
BoxEncryptionKeyPrisoner: BoxEncryptionKeyPassenger
BoxMaintenanceLightbulb: BoxLightMixed
BoxPDAPrisoner: BoxPDA
ClothingBeltMartialBlack: null
ClothingCostumeArcDress: null
ClothingHeadCourier: null
ClothingHeadHatFedoraBlack: null
ClothingHeadHatFedoraChoc: null
ClothingHeadHatFezMantis: null
ClothingHeadPrisonGuard: null
ClothingUniformMartialGi: null
ClothingUniformTShirtKhakiPants: null
ComputerShipyard: null
CourierBag: null
CryogenicSleepUnitSpawnerPrisoner: null
DefaultStationBeaconBoxing: null
DefaultStationBeaconCameraServerRoom: null
DefaultStationBeaconConferenceRoom: null
DefaultStationBeaconCorpsman: null
DefaultStationBeaconEngiOutpost: null
DefaultStationBeaconExam: null
DefaultStationBeaconJanitorsOffice: null
DefaultStationBeaconMailroom: null
DefaultStationBeaconMantis: null
DefaultStationBeaconMetempsychosis: null
DefaultStationBeaconPark: null
DefaultStationBeaconPsychologist: null
DefaultStationBeaconVirology: null
DrinkBubbleTeaGlass: null
FishLabeler: null
FoodLollipop: null
FoodPSB: null
GlimmerProber: null
GunSafeAdjutantShotgun: null
GunSafeEnergyGunMini: null
GunSafeVulcanRifle: null
HatSpawner: null
KitchenDeepFryer: null
KvassTankFull: null
LockerForensicMantisFilled: null
LunchboxSecurityFilledRandom: null
MagazineBoxSpecialPractice: null
MetempsychoticMachine: null
Oracle: null
PaintingMothBigCatch: null
PaintingSpoon: null
PlushieMothMusician: null
PlushieMothRandom: null
PosterContrabandBreadLies: null
PosterContrabandGotWood: null
PosterContrabandSafetyMothSyndie: null
PosterContrabandSaucerNumberOne: null
PosterLegitBarDrinks: null
PosterLegitBotanyFood: null
PosterLegitChknDnnr: null
PosterLegitCornzza: null
PosterLegitDejaVu: null
PosterLegitDontPanic: null
PosterLegitEatMeat: null
PosterLegitFuckAround: null
PosterLegitHotDonkExplosion: null
PosterLegitMedicate: null
PosterLegitNoTouching: null
PosterLegitPieSlice: null
PosterLegitPizzaHope: null
PosterLegitSafetyMothBoH: null
PosterLegitSafetyMothFires: null
PosterLegitSafetyMothGlimmer: null
PosterLegitSafetyMothPills: null
PosterLegitSafetyMothPoisoning: null
PosterLegitShoukou: null
PoweredLightBlueInterior: null
PoweredLightColoredRed: null
PoweredSmallLightMaintenance: null
PoweredSmallLightMaintenanceRed: null
RandomAmmoBox: null
RandomAnimalSpawner: null
RandomBoards: null
RandomBook: null
RandomCrystalSpawner: null
ReverseEngineeringMachine: null
Roboisseur: null
SalvagePartsSpawnerLow: null
SalvagePartsSpawnerMid: null
SecBreachingHammer: null
Shinai: null
ShockCollar: null
shuttle_manipulator: null
SignConspiracyBoard: null
SignDirectionalLogistics: null
SignDirectionalMail: null
SignDojo: null
SignLastIdiot: null
SignSec: null
SophicScribe: null
SpaceCashLuckyBill: null
SpareIdCabinetFilled: null
SpawnMobArcticFoxSiobhan: null
SpawnMobFerret: null
SpawnMobMothroach: null
SpawnPointCourier: null
SpawnPointForensicMantis: null
SpawnPointLocationMidRoundAntag: null
SpawnPointMartialArtist: null
SpawnPointMedicalBorg: null
SpawnPointPrisoner: null
SpawnPointPrisonGuard: null
SpeedLoaderSpecialPractice: null
SuitStorageCorpsman: null
SuitStorageParamedic: null
TableWoodReinforced: TableWood
tatamimat: null
ToyRenault: null
ToySiobhan: null
VendingMachineBoozeUnlocked: null
VendingMachineBoxingDrobe: null
VendingMachineCourierDrobe: null
VendingMachineMNKDrobe: null
VendingMachineRepDrobe: null
WallPaper: null
WarpPointAI: null
WarpPointArrivals: null
WarpPointAtmos: null
WarpPointBar: null
WarpPointCryo: null
WarpPointDetective: null
WarpPointDisposals: null
WarpPointEpistemics: null
WarpPointHOP: null
WarpPointJanitor: null
WarpPointKitchen: null
WarpPointLawyer: null
WarpPointLibrary: null
WarpPointMorgue: null
WarpPointReporter: null
WarpPointSingulo: null
WashingMachine: null
WashingMachineBroken: null
WashingMachineFilledClothes: null
WeaponShotgunKammererNonLethal: null
WindoorSecureMailLocked: null
WindoorSecureParamedicLocked: null
SpawnPointMailCarrier: null
HoloprojectorEngineering: null
ClothingOuterWinterCoatPlaid: null
ClothingOuterWinterCoatLong: null
ClothingHandsGlovesColorYellowUnsulated: null
AirlockMusicianLocked: null
AirlockMimeLocked: null
DoorElectronicsParamedic: null
AirlockExternalGlassSalvageLocked: AirlockExternalGlassCargoLocked
AirlockExternalGlassShuttleEscapeSub: AirlockExternalGlassCargoLocked
AirlockParamedicLocked: AirlockMedicalLocked
FoodMothBakedCheesePlatter: null
PlushieMothBartender: PlushieMoth

# 2023-07-03
ClothingHeadHelmetHelmet: ClothingHeadHelmetBasic
ClothingHeadHelmetHelmetOld: ClothingHeadHelmetBasic

# 2023-07-04
# Bulletproof armor is almost statistically identical to kevlar, however, before this kevlar armor was the closest thing there was to "basic" armor. It makes the most sense to replace it with this.
ClothingOuterVestKevlar: ClothingOuterArmorBasic

# 2023-07-10
Intercom: IntercomCommon

# 2023-07-12
ToyAssistant: ToyFigurinePassenger

# 2023-07-20
ForensicGloves: ClothingHandsGlovesForensic

# 2023-07-24
ClothingEyesGlassesBeer: ClothingEyesHudBeer

# 2023-08-01
lantern: Lantern
lanternextrabright: LanternFlash

# 2023-08-04
BoxMagazineLightRifleHighVelocity: BoxMagazineLightRifle
BoxMagazineMagnumSubMachineGunHighVelocity: BoxMagazineMagnumSubMachineGun
BoxMagazinePistolCaselessRifleHighVelocity: BoxMagazinePistolCaselessRifle
BoxMagazinePistolHighCapacityHighVelocity: BoxMagazinePistolHighCapacity
BoxMagazinePistolHighVelocity: BoxMagazinePistol
BoxMagazinePistolSubMachineGunHighVelocity: BoxMagazinePistolSubMachineGun
BoxMagazineRifleHighVelocity: BoxMagazineRifle
BulletCaselessRifleHighVelocity: BulletCaselessRifle
BulletLightRifleHighVelocity: BulletLightRifle
BulletMagnumHighVelocity: BulletMagnum
BulletPistolHighVelocity: BulletPistol
BulletRifleHighVelocity: BulletRifle
CartridgeCaselessRifleHighVelocity: CartridgeCaselessRifle
CartridgeLightRifleHighVelocity: CartridgeLightRifle
CartridgeMagnumHighVelocity: CartridgeMagnum
CartridgePistolHighVelocity: CartridgePistol
CartridgeRifleHighVelocity: CartridgeRifle
MagazineBoxCaselessRifleHighVelocity: MagazineBoxCaselessRifle
MagazineBoxLightRifleHighVelocity: MagazineBoxLightRifle
MagazineBoxMagnumHighVelocity: MagazineBoxMagnum
MagazineBoxPistolHighVelocity: MagazineBoxPistol
MagazineBoxRifleHighVelocity: MagazineBoxRifle
MagazineCaselessRifleHighVelocity: MagazineCaselessRifle
MagazineCaselessRifleShortHighVelocity: MagazineCaselessRifleShort
MagazineLightRifleHighVelocity: MagazineLightRifle
MagazineMagnumSubMachineGunHighVelocity: MagazineMagnumSubMachineGun
MagazinePistolCaselessRifleHighVelocity: MagazinePistolCaselessRifle
MagazinePistolHighCapacityHighVelocity: MagazinePistolHighCapacity
MagazinePistolHighVelocity: MagazinePistol
MagazinePistolSubMachineGunHighVelocity: MagazinePistolSubMachineGun
MagazineRifleHighVelocity: MagazineRifle
SpeedLoaderMagnumHighVelocity: SpeedLoaderMagnum
SpeedLoaderPistolHighVelocity: SpeedLoaderPistol

# 2023-08-07
#If the name is anything to go off of, these are presumably just CEV-Eris versions of the snow rock (which we already have.)
#They are practically never used in this way however, so they're migrated to the basic rock type.
MountainRock: AsteroidRock
MountainRockMining: AsteroidRockMining

# 2023-08-08
WindowTintedDirectional: WindowFrostedDirectional

# 2023-08-10
SyringeSpaceacillin: null

# 2023-08-13
AirlockPainter: SprayPainter

# 2023-08-19
GeneratorPlasma: PortableGeneratorPacman
GeneratorUranium: PortableGeneratorSuperPacman
GeneratorPlasmaMachineCircuitboard: PortableGeneratorPacmanMachineCircuitboard
GeneratorUraniumMachineCircuitboard: PortableGeneratorSuperPacmanMachineCircuitboard

# 2023-12-10
SpawnPointSeniorResearcher: null
SpawnPointSeniorOfficer: null
SpawnPointSeniorEngineer: null
SpawnPointSeniorPhysician: null

# 2023-12-12
#No this is not the CMO hardsuit, their prototype IDs were just confusingly similar
ClothingOuterHardsuitMedic: ClothingOuterHardsuitSyndieMedic

# 2023-12-13
VendingMachineSmartFridge: SmartFridge

# 2023-12-18
ReagentContainerMilk: DrinkMilkCarton
ReagentContainerMilkSoy: DrinkSoyMilkCarton
ReagentContainerMilkOat: DrinkOatMilkCarton

# 2023-12-20
MiasmaCanister: AmmoniaCanister

# 2023-12-28
WarpPointBeaconBar: null
WarpPointBeaconCargo: null
WarpPointBeaconCommand: null
WarpPointBeaconEngineering: null
WarpPointBeaconMedical: null
WarpPointBeaconNeutral: null
WarpPointBeaconScience: null
WarpPointBeaconSecurity: null
WarpPointBeaconService: null

# 2023-12-30
ClothingEyesGlassesCosmeticSunglasses: ClothingEyesGlassesSunglasses
SpawnPointAssistant: SpawnPointPassenger

# 2024-01-05
DrinkGoldschlagerBottleFull: DrinkGildlagerBottleFull
DrinkGoldschlagerGlass: DrinkGildlagerGlass

# 2024-01-07
ClosetBase: ClosetSteelBase

# 2024-01-08
SalvagePartsT4Spawner: SalvageLootSpawner
SalvagePartsT3Spawner: SalvageLootSpawner
SalvagePartsT3T4Spawner: SalvageLootSpawner
SalvagePartsT2Spawner: SalvageLootSpawner
AdvancedCapacitorStockPart: CapacitorStockPart
SuperCapacitorStockPart: CapacitorStockPart
QuadraticCapacitorStockPart: CapacitorStockPart
NanoManipulatorStockPart: MicroManipulatorStockPart
PicoManipulatorStockPart: MicroManipulatorStockPart
FemtoManipulatorStockPart: MicroManipulatorStockPart
AdvancedMatterBinStockPart: MatterBinStockPart
SuperMatterBinStockPart: MatterBinStockPart
BluespaceMatterBinStockPart: MatterBinStockPart
AnsibleSubspaceStockPart: null
FilterSubspaceStockPart: null
AmplifierSubspaceStockPart: null
TreatmentSubspaceStockPart: null
AnalyzerSubspaceStockPart: null
CrystalSubspaceStockPart: null
TransmitterSubspaceStockPart: null

# 2024-01-10
ClothingHeadHatHoodRad: null

# 2024-01-12
SpaceMedipen: null

# 2024-01-18
ClothingHeadHelmetVoidParamed: null

# 2024-01-19
DefaultStationBeaconTeslaEngine: null

# 2024-01-27 (Reverted on 2024-03-10)
# ClothingBackpackSecurityFilledDetective: ClothingBackpackFilledDetective
# ClothingBackpackDuffelSecurityFilledDetective: ClothingBackpackDuffelFilledDetective
# ClothingBackpackSatchelSecurityFilledDetective: ClothingBackpackSatchelFilledDetective

# 2024-01-28
FoodBoxDonkpocketGondola: FoodBoxDonkpocketPizza

# 2024-01-31
SpyCrewMonitor: null
SpyCrewMonitorEmpty: null
SyndiCrewMonitor: null
SyndiCrewMonitorEmpty: null

#SpawnVehicleWheelchair: null Goobstation - Re-add vehicles
#SpawnVehicleWheelchairFolded: null Goobstation - Re-add vehicles
#VehicleWheelchair: null Goobstation - Re-add vehicles
#VehicleWheelchairFolded: null Goobstation - Re-add vehicles
VehicleSecwayStealObjective: null
#VehicleKeyJanicart: null Goobstation - Re-add vehicles
#VehicleKeySecway: null Goobstation - Re-add vehicles
#VehicleKeyATV: null Goobstation - Re-add vehicles
VehicleKeySkeleton: null
#VehicleKeySyndicateSegway: null Goobstation - Re-add vehicles
VehicleKeySkeletonMotorcycle: null
#VehicleSecway: null Goobstation - Re-add vehicles
#VehicleATV: null Goobstation - Re-add vehicles
#VehicleSyndicateSegway: null Goobstation - Re-add vehicles
VehicleSkeletonMotorcycle: null
VehicleUnicycle: null
VehicleUnicycleFolded: null
ActionVehicleHorn: null
#CrateFunATV: null Goobstation - Re-add vehicles
#CrateFunSyndicateSegway: null Goobstation - Re-add vehicles
MobTaxiBot: null
# MobSupplyBot: null
SpawnVehicleMotobike: null
SpawnVehicleATV: null
#SpawnVehicleSecway: null Goobstation - Re-add vehicles
#SpawnVehicleJanicart: null Goobstation - Re-add vehicles
#VehicleJanicart: null Goobstation - Re-add vehicles
VehicleJanicartDestroyed: null

# 2024-02-01
YellowOxygenTank: OxygenTank
YellowOxygenTankFilled: OxygenTankFilled

# 2024-02-12
FoodDonutBlumpkin: FoodDonutBluePumpkin
FoodDonutJellyBlumpkin: FoodDonutJellyBluePumpkin

# 2024-02-19
#Drone: null
#SpawnMobDrone: null
#Onestar: null # I dont think this is even mapped, but just in case

# 2024-02-22
SolarAssemblyPart: SolarAssemblyFlatpack
AmePart: AmePartFlatpack
AmePartStealObjective: AmePartFlatpackStealObjective
Observationskit: null

# 2024-02-26
CrateBaseWeldable: CrateGenericSteel

# 2024-03-05
BookBotanicalTextbook: BookRandomStory
BookEscalation: BookRandomStory
BookEscalationSecurity: BookRandomStory
BookDemonomiconRandom: BookRandomStory
BookDemonomicon1: BookRandomStory
BookDemonomicon2: BookRandomStory
BookDemonomicon3: BookRandomStory
BookChemistryInsane: BookRandomStory
BookGnominomicon: BookRandomStory
BookFishing: BookRandomStory
BookDetective: BookRandomStory

# 2024-03-07
AirlockExternalEasyPry: AirlockExternal
AirlockExternalGlassEasyPry: AirlockExternalGlass
AirlockGlassShuttleEasyPry: AirlockGlassShuttle
AirlockShuttleEasyPry: AirlockShuttle
AirlockExternalEasyPryLocked: AirlockExternalLocked
AirlockExternalGlassEasyPryLocked: AirlockExternalGlassLocked
AirlockGlassShuttleEasyPryLocked: AirlockExternalGlassShuttleLocked
AirlockShuttleEasyPryLocked: AirlockExternalShuttleLocked

# 2024-03-10
FoodChili: FoodChiliPepper
FoodChilly: FoodChillyPepper
# ClothingBackpackFilledDetective: ClothingBackpackSecurityFilledDetective
# ClothingBackpackDuffelFilledDetective: ClothingBackpackDuffelSecurityFilledDetective
# ClothingBackpackSatchelFilledDetective: ClothingBackpackSatchelSecurityFilledDetective

# 2024-03-11
ImprovisedExplosive: FireBomb
ImprovisedExplosiveEmpty: FireBombEmpty
ImprovisedExplosiveFuel: FireBombFuel

# 2024-03-16
ClothingHeadHatHairflower: FoodPoppy

# 2024-03-21
RPED: null

# 2024-03-30
TraversalDistorterMachineCircuitboard: null
MachineTraversalDistorter: null
# These are technically not equivalent, but it probably makes more sense to replace any existing SCAF stuff with SOME kind of armor, instead of just deleting it outright.
ClothingHeadHelmetScaf: ClothingHeadHelmetBasic
ClothingOuterArmorScaf: ClothingOuterArmorBasic

# 2024-03-31
ClothingNeckFlowerWreath: ClothingHeadHatFlowerWreath
ClothingHeadHatFlowerCrown: ClothingHeadHatFlowerWreath
BriefcaseSyndieBase: null

# 2024-04-08
BodyBag_Container: BodyBag
BodyBag_Folded: BodyBagFolded

# 2024-04-26
BaseBulletRubber: null
BulletPistolRubber: BulletPistol
BulletMagnumRubber: BulletMagnum
BulletLightRifleRubber: BulletLightRifle
BulletRifleRubber: BulletRifle
BulletCaselessRifleRubber: BulletCaselessRifle
CartridgePistolRubber: CartridgePistol
CartridgeMagnumRubber: CartridgeMagnum
CartridgeLightRifleRubber: CartridgeLightRifle
CartridgeRifleRubber: CartridgeRifle
CartridgeCaselessRifleRubber: CartridgeCaselessRifle
MagazinePistolRubber: MagazinePistol
MagazinePistolSubMachineGunRubber: MagazinePistolSubMachineGun
MagazinePistolCaselessRifleRubber: MagazinePistolCaselessRifle
MagazineMagnumRubber: MagazineMagnum
MagazineMagnumSubMachineGunRubber: MagazineMagnumSubMachineGun
MagazineLightRifleRubber: MagazineLightRifle
MagazineRifleRubber: MagazineRifle
MagazineCaselessRifleRubber: MagazineCaselessRifle
MagazineCaselessRifleShortRubber: MagazineCaselessRifleShort
SpeedLoaderPistolRubber: SpeedLoaderPistol
SpeedLoaderMagnumRubber: SpeedLoaderMagnum
MagazineBoxPistolRubber: MagazineBoxPistol
# RIP box of magnum rubbers 202X to 2024
MagazineBoxMagnumRubber: MagazineBoxMagnum
MagazineBoxLightRifleRubber: MagazineBoxLightRifle
MagazineBoxRifleRubber: MagazineBoxRifle
MagazineBoxRifleBigRubber: MagazineBoxRifleBig
MagazineBoxCaselessRifleRubber: MagazineBoxCaselessRifle
MagazineBoxCaselessRifleBigRubber: MagazineBoxCaselessRifle10x24
BoxMagazinePistolRubber: BoxMagazinePistol
BoxMagazinePistolHighCapacityRubber: BoxMagazinePistolHighCapacity
BoxMagazinePistolSubMachineGunRubber: BoxMagazinePistolSubMachineGun
BoxMagazineLightRifleRubber: BoxMagazineLightRifle
BoxMagazineRifleRubber: BoxMagazineRifle
BoxMagazineCaselessRifleRubber: BoxMagazinePistolCaselessRifle
BoxMagazineMagnumSubMachineGunRubber: BoxMagazineMagnumSubMachineGun
WeaponPistolMk58Nonlethal: WeaponPistolMk58
WeaponPistolN1984Nonlethal: WeaponPistolN1984
WeaponSubMachineGunDrozdRubber: WeaponSubMachineGunDrozd
WeaponRifleLecterRubber: WeaponRifleLecter

# 2024-04-26
GlassBoxLaserBroken: GlassBoxBroken
ReinforcementRadioSyndicateMonkey: ReinforcementRadioSyndicateAncestor
ReinforcementRadioSyndicateMonkeyNukeops: ReinforcementRadioSyndicateAncestorNukeops

# 2024-05-01
DrinkBottleGoldschlager: DrinkBottleGildlager

# 2024-05-14
soda_dispenser: SodaDispenser
chem_master: ChemMaster

# 2024-05-21
CrateJanitorExplosive: ClosetJanitorBombFilled

# Goobstation - re adding fire door remote

# 2024-06-03
AirlockServiceCaptainLocked: AirlockCaptainLocked

#2024-06-05
DisasterVictimSpawner: CommandVisitorSpawner
LostCargoTechnicianSpawner: VisitorCargoTechnicianSpawner
ClownTroupeSpawner: VisitorClownSpawner
TravelingChefSpawner: VisitorChefSpawner
SyndieDisasterVictimSpawner: SyndieVisitorSpawner

# 2024-06-15
ClothingOuterCoatInspector: ClothingOuterCoatJensen

# 2024-06-23
FloorTileItemReinforced: PartRodMetal1

#2024-06-25
BookChefGaming: BookHowToCookForFortySpaceman

#2024-06-29
IntercomAssesmbly: IntercomAssembly

# 2024-07-7
SignScience1: SignScience
SignScience2: SignScience
SignXenobio2: SignXenobio
SignXenolab: SignXenobio
SignToxins2: SignToxins
SignMinerDock: SignShipDock
SignChemistry1: SignChem
SignChemistry2: SignChem
SignCourt: SignLawyer
SignAtmosMinsky: SignAtmos
SignDrones: SignMaterials
SignShield: null # what was this even for?
SignHydro2: SignHydro1
SignHydro3: SignHydro1

# 2024-07-27
LogicGate: LogicGateOr

# 2024-08-08
MaterialReclaimer: null
MaterialReclaimerMachineCircuitboard: null

# 2024-08-11
FoodTacoBeef: FoodTacoShell
FoodTacoChicken: FoodTacoShell
FoodTacoFish: FoodTacoShell
FoodTacoBeefSupreme: FoodTacoShell
FoodTacoChickenSupreme: FoodTacoShell
FoodTacoRat: FoodTacoShell

FoodMeatHumanKebab: FoodKebabSkewer
FoodMeatLizardtailKebab: FoodKebabSkewer
FoodMeatRatKebab: FoodKebabSkewer
FoodMeatRatdoubleKebab: FoodKebabSkewer
FoodMeatSnakeKebab: FoodKebabSkewer
FoodMeatHawaiianKebab: FoodKebabSkewer
FoodMeatKebab: FoodKebabSkewer
FoodMeatFiestaKebab: FoodKebabSkewer

#2024-08-14
ClothingBeltSuspenders: ClothingBeltSuspendersRed

# 2024-08-19
ClothingNeckShockCollar: ClothingBackpackElectropack

# 2024-08-28
ClothingBackpackDuffelSyndicateCostumeCentcom: null
ClothingHeadsetAltCentComFake: null
CentcomPDAFake: null
CentcomIDCardSyndie: null

# 2024-09-06
AntimovCircuitBoard: null
OverlordCircuitBoard: null

# 2024-09-08
HatBase: null

# 2024-09-19
BlueprintFlare: null

# 2024-10-04
BaseAdvancedPen: Pen

# 2024-10-09
# Removal of separate borg chassis parts, replace them with generic borg parts.
LeftArmBorgEngineer: LeftArmBorg
RightArmBorgEngineer: RightArmBorg
LeftLegBorgEngineer: LeftLegBorg
RightLegBorgEngineer: RightLegBorg
HeadBorgEngineer: LightHeadBorg
TorsoBorgEngineer: TorsoBorg

LeftArmBorgMedical: LeftArmBorg
RightArmBorgMedical: RightArmBorg
LeftLegBorgMedical: LeftLegBorg
RightLegBorgMedical: RightLegBorg
HeadBorgMedical: LightHeadBorg
TorsoBorgMedical: TorsoBorg

LeftArmBorgMining: LeftArmBorg
RightArmBorgMining: RightArmBorg
LeftLegBorgMining: LeftLegBorg
RightLegBorgMining: RightLegBorg
HeadBorgMining: LightHeadBorg
TorsoBorgMining: TorsoBorg

LeftArmBorgService: LeftArmBorg
RightArmBorgService: RightArmBorg
LeftLegBorgService: LeftLegBorg
RightLegBorgService: RightLegBorg
HeadBorgService: LightHeadBorg
TorsoBorgService: TorsoBorg

LeftLegBorgJanitor: LeftLegBorg
RightLegBorgJanitor: RightLegBorg
HeadBorgJanitor: LightHeadBorg
TorsoBorgJanitor: TorsoBorg

# 2024-11-17
PresentRandomAsh: PresentRandomCoal

# 2024-11-19
CrateCrewMonitoringBoards: CrateCrewMonitoring

# 2024-11-25
CrateSlimepersonLifeSupport: CrateNitrogenInternals

# 2024-12-01
DungeonMasterCircuitBoard: GameMasterCircuitBoard

# 2024-12-12
FloraRockSolid01: FloraRockSolid
FloraRockSolid02: FloraRockSolid
FloraRockSolid03: FloraRockSolid
FloraStalagmite1: FloraStalagmite
FloraStalagmite2: FloraStalagmite
FloraStalagmite3: FloraStalagmite
FloraStalagmite4: FloraStalagmite
FloraStalagmite5: FloraStalagmite
FloraStalagmite6: FloraStalagmite
FloraGreyStalagmite1: FloraGreyStalagmite
FloraGreyStalagmite2: FloraGreyStalagmite
FloraGreyStalagmite3: FloraGreyStalagmite
FloraGreyStalagmite4: FloraGreyStalagmite
FloraGreyStalagmite5: FloraGreyStalagmite
FloraGreyStalagmite6: FloraGreyStalagmite
FloraTree01: FloraTree
FloraTree02: FloraTree
FloraTree03: FloraTree
FloraTree04: FloraTree
FloraTree05: FloraTree
FloraTree06: FloraTree
FloraTreeSnow01: FloraTreeSnow
FloraTreeSnow02: FloraTreeSnow
FloraTreeSnow03: FloraTreeSnow
FloraTreeSnow04: FloraTreeSnow
FloraTreeSnow05: FloraTreeSnow
FloraTreeSnow06: FloraTreeSnow
FloraTreeLarge01: FloraTreeLarge
FloraTreeLarge02: FloraTreeLarge
FloraTreeLarge03: FloraTreeLarge
FloraTreeLarge04: FloraTreeLarge
FloraTreeLarge05: FloraTreeLarge
FloraTreeLarge06: FloraTreeLarge
FloraTreeConifer01: FloraTreeConifer
FloraTreeConifer02: FloraTreeConifer
FloraTreeConifer03: FloraTreeConifer
ShadowTree01: ShadowTree
ShadowTree02: ShadowTree
ShadowTree03: ShadowTree
ShadowTree04: ShadowTree
ShadowTree05: ShadowTree
ShadowTree06: ShadowTree
LightTree01: LightTree
LightTree02: LightTree
LightTree03: LightTree
LightTree04: LightTree
LightTree05: LightTree
LightTree06: LightTree

# 2024-12-22 # Lavaland Change: Migrations
VendingMachineRestockSalvageEquipment: null
ComputerSalvageExpedition: ComputerShuttleMiningLavaland
SalvageExpeditionsComputerCircuitboard: LavalandShuttleConsoleCircuitboard

# 2025-01-06
BorgModuleRadiationDetection: null
BorgModuleGPS: null
BorgModuleLightReplacer: BorgModuleCustodial
BorgModuleAdvancedTreatment: null

# 2025-01-27
FoodCondimentPacketFrostoil: FoodCondimentPacketColdsauce

# 2025-02-05
WeaponSubMachineGunVector: null
WeaponSubMachineGunVectorRubber: null

# 2025-02-11
WallShuttleInterior: WallShuttle

# 2025-02-18
PosterContrabandLustyExomorph: PosterContrabandRealExomorph
PosterContrabandBustyBackdoorExoBabes6: PosterContrabandRouny

# 2025-02-20
MagazineBoxAntiMaterielBig: null
MagazineBoxCaselessRifle10x24: null
MagazineBoxCaselessRifleBig: null
MagazinePistolHighCapacityRubber: null

# 2025-03-09 - Shitmed Woundmed, separating torsos into chest and groin
TorsoHuman: ChestHuman
TorsoSkeleton: ChestSkeleton
# 2025-03-18
EpinephrineChemistryBottle: ChemistryBottleEpinephrine
RobustHarvestChemistryBottle: ChemistryBottleRobustHarvest
EZNutrientChemistryBottle: ChemistryBottleEZNutrient
Left4ZedChemistryBottle: ChemistryBottleLeft4Zed
UnstableMutagenChemistryBottle: ChemistryBottleUnstableMutagen
PotassiumChemistryBottle: ChemistryBottlePotassium
NocturineChemistryBottle: ChemistryBottleNocturine
NitrogenChemistryBottle: ChemistryBottleNitrogen
EphedrineChemistryBottle: ChemistryBottleEphedrine
PhosphorusChemistryBottle: ChemistryBottlePhosphorus
OmnizineChemistryBottle: ChemistryBottleOmnizine
HydrogenChemistryBottle: ChemistryBottleHydrogen
CognizineChemistryBottle: ChemistryBottleCognizine
EthanolChemistryBottle: ChemistryBottleEthanol
PaxChemistryBottle: ChemistryBottlePax
MuteToxinChemistryBottle: ChemistryBottleMuteToxin
LeadChemistryBottle: ChemistryBottleLead
ToxinChemistryBottle: ChemistryBottleToxin

# Goob - making wizden maps compatible to support mapping efforts. 2025-04-13.
CargoMailTeleporter: MailTeleporter
ClothingMaskBlushingMime: ClothingMaskSexyMime
ClothingMaskBlushingClown: ClothingMaskSexyMime
ClothingNeckGoldAutismPin: ClothingNeckAutismPin
MachineMaterialSilo: null
CrateMaterialSilo: null
  
# 2025-03-29
ClothingBackpackDuffelSyndicateRaidBundle: ClothingBackpackSyndicateRaidBundle

# 2025-04-15
SimpleXenoArtifact: ComplexXenoArtifact
MediumXenoArtifact: ComplexXenoArtifact
SimpleXenoArtifactItem: ComplexXenoArtifactItem
MediumXenoArtifactItem: ComplexXenoArtifactItem
VariedXenoArtifactItem: ComplexXenoArtifactItem

# 2025-04-19
ClothingOuterHardsuitSyndicate: ClothingOuterEVASuitSyndicate
ClothingOuterHardsuitSyndicateDurathread: ClothingOuterEVASuitSyndicate
AirlockMaintCommonLocked: AirlockMaintLocked
AirlockMaintIntLocked: AirlockMaintLocked

# 2025-04-26
ComputerCargoShuttle: ComputerShuttleCargo
CargoShuttleComputerCircuitboard: CargoShuttleConsoleCircuitboard

# 2025-05-09 Goobstation
ClothingBackpackCentcom: ClothingBackpackCentcomm

# 2025-06-04 Goobstation Plushie Death
PlushieBenny: null
PlushieCairo: null
PlushiePash: null
PlushieDelta: null
PlushieRaki: null
PlushieTina: null
PlushieKnott: null
PlushieZlorb: null
PlushieTiderMoth: null
PlushieCommandHuman: null
PlushieRanork: null
PlushieWehWinter: null
PlushieHaleigh: null
PlushieStoop: null
PlushieAhti: null
PlushieSparkle: null
PlushieGrub: null
PlushieHexadecimal: null
PlushieFlint: null
PlushiePistachio: null
PlushieSeesTheStars: null
PlushieDumbLizard: null
PlushieSqueaks: null
PlushieHaato: null
PlushieTinyLane: null
PlushieChewsTheShoes: null
PlushieBlackburn: null
PlushieBlackburnMatryoskya: null
PlushieDesislavaBlackburn: null
PlushieBlackburnKobliska: null

# 2025-06-13 Goobstation
BroomWitch: Broomstick
