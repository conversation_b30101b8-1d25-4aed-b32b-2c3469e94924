<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <!-- Avoid MSBuild adding a None entry for XAML files because they'd show up TWICE in the project view. -->
    <DefaultItemExcludes>**/*.xaml</DefaultItemExcludes>
    <RobustUseExternalMSBuild>true</RobustUseExternalMSBuild>
    <_RobustUseExternalMSBuild>$(RobustUseExternalMSBuild)</_RobustUseExternalMSBuild>
    <_RobustUseExternalMSBuild Condition="'$(_RobustForceInternalMSBuild)' == 'true'">false</_RobustUseExternalMSBuild>
  </PropertyGroup>
  <ItemGroup>
    <Compile Update="**\*.xaml.cs">
      <DependentUpon>%(Filename)</DependentUpon>
    </Compile>
    <EmbeddedResource Include="**\*.xaml"/>
    <AdditionalFiles Include="**\*.xaml"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="$(MSBuildThisFileDirectory)\..\Robust.Client.NameGenerator\Robust.Client.NameGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false"/>
    <ProjectReference Include="$(MSBuildThisFileDirectory)\..\Robust.Client.Injectors\Robust.Client.Injectors.csproj" ReferenceOutputAssembly="false"/>
  </ItemGroup>

  <!-- XamlIL does not make use of special Robust configurations like DebugOpt. Convert these down. -->
  <PropertyGroup>
    <RobustInjectorsConfiguration>$(Configuration)</RobustInjectorsConfiguration>
    <RobustInjectorsConfiguration Condition="'$(Configuration)' == 'DebugOpt'">Debug</RobustInjectorsConfiguration>
    <RobustInjectorsConfiguration Condition="'$(Configuration)' == 'Tools'">Release</RobustInjectorsConfiguration>
    <RobustInjectorsConfiguration Condition="'$(UseArtifactsOutput)' == 'true' And '$(RuntimeIdentifier)' != ''">$(RobustInjectorsConfiguration)_$(RuntimeIdentifier)</RobustInjectorsConfiguration>
    <RobustInjectorsConfiguration Condition="'$(UseArtifactsOutput)' == 'true'">$(RobustInjectorsConfiguration.ToLower())</RobustInjectorsConfiguration>
    <CompileRobustXamlTaskAssemblyFile Condition="'$(UseArtifactsOutput)' != 'true'">$(MSBuildThisFileDirectory)\..\Robust.Client.Injectors\bin\$(RobustInjectorsConfiguration)\netstandard2.0\Robust.Client.Injectors.dll</CompileRobustXamlTaskAssemblyFile>
    <CompileRobustXamlTaskAssemblyFile Condition="'$(UseArtifactsOutput)' == 'true'">$(MSBuildThisFileDirectory)\..\..\artifacts\bin\Robust.Client.Injectors\$(RobustInjectorsConfiguration)\Robust.Client.Injectors.dll</CompileRobustXamlTaskAssemblyFile>
  </PropertyGroup>

  <UsingTask
    Condition="'$(_RobustUseExternalMSBuild)' != 'true' And $(DesignTimeBuild) != true"
    TaskName="CompileRobustXamlTask"
    AssemblyFile="$(CompileRobustXamlTaskAssemblyFile)"/>
  <Target
    Name="CompileRobustXaml"
    Condition="Exists('@(IntermediateAssembly)')"
    AfterTargets="AfterCompile"
    Inputs="@(IntermediateAssembly);@(ReferencePathWithRefAssemblies)"
    Outputs="$(IntermediateOutputPath)XAML/doot">
    <PropertyGroup>
      <RobustXamlReferencesTemporaryFilePath Condition="'$(RobustXamlReferencesTemporaryFilePath)' == ''">$(IntermediateOutputPath)XAML/references</RobustXamlReferencesTemporaryFilePath>
      <RobustXamlOriginalCopyFilePath Condition="'$(RobustXamlOriginalCopyFilePath)' == ''">$(IntermediateOutputPath)XAML/original.dll</RobustXamlOriginalCopyFilePath>
    </PropertyGroup>
    <WriteLinesToFile
      Condition="'$(_RobustForceInternalMSBuild)' != 'true'"
      File="$(RobustXamlReferencesTemporaryFilePath)"
      Lines="@(ReferencePathWithRefAssemblies)"
      Overwrite="true"/>

    <!--
    UpdateBuildIndicator is done so that we can use MSBuild Inputs and Outputs on the target
    to avoid unecessary execution of this target
    Saves compile time if e.g. ONLY Robust.Client changes (Content.Client doesn't have to re-xaml).
    -->
    <CompileRobustXamlTask
      Condition="'$(_RobustUseExternalMSBuild)' != 'true'"
      AssemblyFile="@(IntermediateAssembly)"
      ReferencesFilePath="$(RobustXamlReferencesTemporaryFilePath)"
      OriginalCopyPath="$(RobustXamlOriginalCopyFilePath)"
      ProjectDirectory="$(MSBuildProjectDirectory)"
      AssemblyOriginatorKeyFile="$(AssemblyOriginatorKeyFile)"
      SignAssembly="$(SignAssembly)"
      DelaySign="$(DelaySign)"
      UpdateBuildIndicator="$(IntermediateOutputPath)XAML/doot"/>

    <PropertyGroup>
      <DOTNET_HOST_PATH Condition="'$(DOTNET_HOST_PATH)' == ''">dotnet</DOTNET_HOST_PATH>
    </PropertyGroup>
    <Exec
      Condition="'$(_RobustUseExternalMSBuild)' == 'true'"
      Command="&quot;$(DOTNET_HOST_PATH)&quot; msbuild /nodereuse:false $(MSBuildProjectFile) /t:CompileRobustXaml /p:_RobustForceInternalMSBuild=true /p:Configuration=$(Configuration) /p:RuntimeIdentifier=$(RuntimeIdentifier) /p:TargetFramework=$(TargetFramework) /p:BuildProjectReferences=false /p:IntermediateOutputPath=&quot;$(IntermediateOutputPath.TrimEnd('\'))/&quot;"/>
  </Target>
</Project>
